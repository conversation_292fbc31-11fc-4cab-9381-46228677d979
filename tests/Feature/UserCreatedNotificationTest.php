<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\User;
use App\Notifications\App\User\UserCreated;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class UserCreatedNotificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_created_notification_is_sent_for_internal_user(): void
    {
        Notification::fake();

        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'test-password-123',
            'active' => true,
        ];

        $user = User::create($userData);

        Notification::assertSentTo(
            $user,
            UserCreated::class,
            function ($notification) use ($userData) {
                $mailMessage = $notification->toMail($notification);

                // Check that the notification contains the correct password
                $this->assertStringContainsString($userData['password'], $mailMessage->render());

                // Check that the access URL points to /app for internal users
                $this->assertStringContainsString('/app', $mailMessage->actionUrl);

                return true;
            }
        );
    }

    public function test_user_created_notification_is_sent_for_customer_user(): void
    {
        Notification::fake();

        $customerData = [
            'name' => 'Test Customer',
            'trading_name' => 'Test Customer Trading',
            'tax_id_number' => '12345678901234',
            'active' => true,
        ];

        $customer = Customer::create($customerData);

        $userData = [
            'name' => 'Customer User',
            'email' => '<EMAIL>',
            'password' => 'customer-password-456',
        ];

        $user = $customer->user()->create($userData);

        Notification::assertSentTo(
            $user,
            UserCreated::class,
            function ($notification) use ($userData) {
                $mailMessage = $notification->toMail($notification);

                // Check that the notification contains the correct password
                $this->assertStringContainsString($userData['password'], $mailMessage->render());

                // Check that the access URL points to /customer for customer users
                $this->assertStringContainsString('/customer', $mailMessage->actionUrl);

                return true;
            }
        );
    }

    public function test_user_created_notification_contains_correct_content(): void
    {
        Notification::fake();

        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'test-password-123',
            'active' => true,
        ];

        $user = User::create($userData);

        Notification::assertSentTo(
            $user,
            UserCreated::class,
            function ($notification) use ($userData, $user) {
                $mailMessage = $notification->toMail($user);
                $renderedMessage = $mailMessage->render();

                // Check subject
                $this->assertStringContainsString('Bem-vindo(a) ao', $mailMessage->subject);

                // Check greeting contains user name
                $this->assertStringContainsString($user->name, $renderedMessage);

                // Check email is displayed
                $this->assertStringContainsString($user->email, $renderedMessage);

                // Check password is displayed
                $this->assertStringContainsString($userData['password'], $renderedMessage);

                // Check Portuguese content
                $this->assertStringContainsString('Sua conta foi criada com sucesso', $renderedMessage);
                $this->assertStringContainsString('informações de primeiro acesso', $renderedMessage);

                return true;
            }
        );
    }
}
