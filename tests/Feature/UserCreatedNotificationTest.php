<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\User;
use App\Notifications\App\User\UserCreated;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class UserCreatedNotificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_created_notification_is_sent_for_internal_user(): void
    {
        Notification::fake();

        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'test-password-123',
            'active' => true,
        ];

        $user = User::create($userData);

        Notification::assertSentTo(
            $user,
            UserCreated::class,
            function ($notification) use ($userData) {
                // Verify the notification was created with the correct password
                $reflection = new \ReflectionClass($notification);
                $passwordProperty = $reflection->getProperty('password');
                $passwordProperty->setAccessible(true);
                $notificationPassword = $passwordProperty->getValue($notification);

                $this->assertEquals($userData['password'], $notificationPassword);

                // Verify the access URL
                $urlProperty = $reflection->getProperty('accessUrl');
                $urlProperty->setAccessible(true);
                $accessUrl = $urlProperty->getValue($notification);

                $this->assertStringContainsString('/app', $accessUrl);

                return true;
            }
        );
    }

    public function test_user_created_notification_is_sent_for_customer_user(): void
    {
        Notification::fake();

        $customerData = [
            'name' => 'Test Customer',
            'trading_name' => 'Test Customer Trading',
            'tax_id_number' => '12345678901234',
            'active' => true,
        ];

        $customer = Customer::create($customerData);

        $userData = [
            'name' => 'Customer User',
            'email' => '<EMAIL>',
            'password' => 'customer-password-456',
        ];

        $user = $customer->user()->create($userData);

        Notification::assertSentTo(
            $user,
            UserCreated::class,
            function ($notification) use ($userData) {
                // Verify the notification was created with the correct password
                $reflection = new \ReflectionClass($notification);
                $passwordProperty = $reflection->getProperty('password');
                $passwordProperty->setAccessible(true);
                $notificationPassword = $passwordProperty->getValue($notification);

                $this->assertEquals($userData['password'], $notificationPassword);

                // Verify the access URL
                $urlProperty = $reflection->getProperty('accessUrl');
                $urlProperty->setAccessible(true);
                $accessUrl = $urlProperty->getValue($notification);

                $this->assertStringContainsString('/customer', $accessUrl);

                return true;
            }
        );
    }

    public function test_user_created_notification_contains_correct_content(): void
    {
        Notification::fake();

        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'test-password-123',
            'active' => true,
        ];

        $user = User::create($userData);

        Notification::assertSentTo(
            $user,
            UserCreated::class,
            function ($notification) use ($userData, $user) {
                $mailMessage = $notification->toMail($user);

                // Check subject
                $this->assertStringContainsString('Bem-vindo(a) ao', $mailMessage->subject);

                // Verify the notification was created with the correct password
                $reflection = new \ReflectionClass($notification);
                $passwordProperty = $reflection->getProperty('password');
                $passwordProperty->setAccessible(true);
                $notificationPassword = $passwordProperty->getValue($notification);

                $this->assertEquals($userData['password'], $notificationPassword);

                return true;
            }
        );
    }
}
