<?php

return [

    'forms' => [
        'fields' => [
            'customer_id' => 'Cliente',
            'name' => 'Nome',
            'code' => 'Código',
            'serial_number' => 'Número de série',
            'manufactured_at' => 'Data de fabricação',
            'warranty_expires_at' => 'Data de expiração da garantia',
            'additional_info' => 'Informações adicionais',
            'equipment_type_id' => 'Tipo de equipamento',
        ]
    ],

    'responses' => [
        'create' => [
            'success' => 'O equipamento foi criado.',
        ],
        'update' => [
            'success' => 'O equipamento foi atualizado.',
        ],
        'delete' => [
            'success' => 'O equipamento foi excluído.',
        ],
        'get_from_erp_flex' => [
            'success' => 'Os equipamentos foram obtidos do ERPFlex.',
        ],
    ],

];
