<?php

return [

    'forms' => [
        'fields' => [
            'customer_id' => 'Cliente',
            'code' => 'Código',
            'type' => 'Tipo',
            'status' => 'Status',
            'started_at' => 'Data de início',
            'started_at_from' => 'Data de início de',
            'started_at_to' => 'Data de início até',
            'ended_at' => 'Data de término',
            'ended_at_from' => 'Data de término de',
            'ended_at_to' => 'Data de término até',
            'signed_at' => 'Data de assinatura',
            'created_at' => 'Criado em',
            'created_at_from' => 'Criado em de',
            'created_at_to' => 'Criado em até',
        ],
    ],

    'responses' => [
        'create' => [
            'success' => 'O contrato foi criado.',
        ],
        'update' => [
            'success' => 'O contrato foi atualizado.',
        ],
        'delete' => [
            'success' => 'O contrato foi excluído.',
        ],
        'create_in_erp_flex' => [
            'success' => 'O contrato foi criado no ERPFlex.',
        ],
        'generate_service_orders' => [
            'success' => 'As ordens de serviço foram geradas.',
        ],
    ],

];
