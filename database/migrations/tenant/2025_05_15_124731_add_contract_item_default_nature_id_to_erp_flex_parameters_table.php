<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('erp_flex_parameters', function (Blueprint $table) {
            $table->bigInteger('contract_item_default_nature_id')
                ->nullable()
                ->after('service_order_default_used_product_stock_nature_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('erp_flex_parameters', function (Blueprint $table) {
            $table->dropColumn('contract_item_default_nature_id');
        });
    }
};
