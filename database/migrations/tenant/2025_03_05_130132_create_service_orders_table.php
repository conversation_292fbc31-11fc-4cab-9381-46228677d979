<?php

use App\Enums\ServiceOrderStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_orders', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->foreignId('protocol_id')->nullable()->constrained();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('equipment_id')->constrained();
            $table->foreignId('service_type_id')->constrained();
            $table->foreignId('employee_id')->nullable()->constrained('users');
            $table->foreignId('workflow_id')->constrained();
            $table->foreignId('workflow_state_id')->constrained();
            $table->string('description', 4000)->nullable();
            $table->string('visit_additional_info', 3000)->nullable();
            $table->unsignedSmallInteger('estimated_duration_in_minutes');
            $table->string('status')->default(ServiceOrderStatusEnum::Pending->value);
            $table->dateTime('scheduled_to')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_orders');
    }
};
