<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('third_party_contracts', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('integration_type_id')->unsigned()->index();
            $table->foreignId('contract_id')->nullable()->constrained();
            $table->string('third_party_id')->nullable()->index();
            $table->json('third_party_api_read_data')->nullable();
            $table->json('third_party_db_read_data')->nullable();
            $table->json('third_party_sent_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('third_party_contracts');
    }
};
