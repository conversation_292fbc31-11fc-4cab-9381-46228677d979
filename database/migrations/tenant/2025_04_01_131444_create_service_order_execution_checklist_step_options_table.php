<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_order_execution_checklist_step_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_order_execution_checklist_step_id');
            $table->unsignedTinyInteger('sequence');
            $table->string('value');
            $table->boolean('requires_comment');
            $table->timestamps();

            $table->foreign('service_order_execution_checklist_step_id', 'soecso_soecsi_foreign')
                ->references('id')
                ->on('service_order_execution_checklist_steps');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_order_execution_checklist_step_options');
    }
};
