<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_order_execution_checklist_step_needs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_order_execution_checklist_step_id');
            $table->foreignId('need_id');
            $table->string('need_type');
            $table->decimal('quantity');
            $table->timestamps();

            $table->foreign('service_order_execution_checklist_step_id', 'soecsn_soecsi_foreign')
                ->references('id')
                ->on('service_order_execution_checklist_steps');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_order_execution_checklist_step_needs');
    }
};
