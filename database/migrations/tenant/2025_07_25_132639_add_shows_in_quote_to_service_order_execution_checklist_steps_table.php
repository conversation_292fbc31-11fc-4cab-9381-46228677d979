<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_order_execution_checklist_steps', function (Blueprint $table) {
            $table->boolean('shows_checklist_in_quote')->default(true)->after('has_needs');
            $table->boolean('shows_in_quote')->default(true)->after('shows_checklist_in_quote');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_order_execution_checklist_steps', function (Blueprint $table) {
            $table->dropColumn('shows_checklist_in_quote');
            $table->dropColumn('shows_in_quote');
        });
    }
};
