<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_request_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('model_id');
            $table->string('model_type');
            $table->string('service_name')->index()->nullable();
            $table->string('service_method')->index()->nullable();
            $table->boolean('success')->nullable();
            $table->string('status_code', 3)->nullable();
            $table->text('response_headers')->nullable();
            $table->text('response_body')->nullable();
            $table->string('method', 10)->nullable();
            $table->text('url')->nullable();
            $table->text('request_headers')->nullable();
            $table->text('request_body')->nullable();
            $table->text('error_description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_request_logs');
    }
};
