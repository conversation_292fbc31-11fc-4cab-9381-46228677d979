<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_order_execution_checklist_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_order_id');
            $table->unsignedSmallInteger('sequence');
            $table->string('checklist_step_name');
            $table->string('data_type');
            $table->longText('collected_value')->nullable();
            $table->json('collected_extras')->nullable();
            $table->string('comments', 1000)->nullable();
            $table->boolean('requires_comment')->default(false);
            $table->timestamps();

            $table->foreign('service_order_id', 'soecs_soi_foreign')
                ->references('id')
                ->on('service_orders');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_order_execution_checklist_steps');
    }
};
