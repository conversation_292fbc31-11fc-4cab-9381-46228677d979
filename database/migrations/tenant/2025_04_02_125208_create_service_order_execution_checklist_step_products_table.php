<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_order_execution_checklist_step_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_order_execution_checklist_step_id');
            $table->foreignId('product_id');
            $table->foreignId('origin_warehouse_id');
            $table->foreignId('stock_movement_id');
            $table->decimal('quantity');
            $table->timestamps();

            $table->foreign('service_order_execution_checklist_step_id', 'soecsp_soecsi_foreign')
                ->references('id')
                ->on('service_order_execution_checklist_steps');

            $table->foreign('product_id', 'soecsp_pi_foreign')
                ->references('id')
                ->on('products');

            $table->foreign('origin_warehouse_id', 'soecsp_owi_foreign')
                ->references('id')
                ->on('warehouses');

            $table->foreign('stock_movement_id', 'soecsp_smi_foreign')
                ->references('id')
                ->on('stock_movements');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_order_execution_checklist_step_products');
    }
};
