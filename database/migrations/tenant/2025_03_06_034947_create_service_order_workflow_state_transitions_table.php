<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_order_workflow_state_transitions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_order_id');
            $table->foreignId('origin_workflow_state_id')->nullable();
            $table->foreignId('destination_workflow_state_id');
            $table->foreignId('transitioned_by_user_id');
            $table->string('transitioned_by_user_name');
            $table->timestamps();

            $table->foreign('service_order_id', 'sowst_soi_foreign')
                ->references('id')
                ->on('service_orders');

            $table->foreign('origin_workflow_state_id', 'sowst_owsi_foreign')
                ->references('id')
                ->on('workflow_states');

            $table->foreign('destination_workflow_state_id', 'sowst_dwsi_foreign')
                ->references('id')
                ->on('workflow_states');

            $table->foreign('transitioned_by_user_id', 'sowst_tbui_foreign')
                ->references('id')
                ->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_order_workflow_state_transitions');
    }
};
