<?php

use App\Models\ServiceOrderExecutionChecklistStep;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_order_execution_checklist_steps', function (Blueprint $table) {
            $table->string('checklist_name')
                ->nullable()
                ->after('checklist_id');
        });

        ServiceOrderExecutionChecklistStep::query()
            ->with('checklist:id,name')
            ->get()
            ->each(fn(ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep) => $serviceOrderExecutionChecklistStep->update([
                'checklist_name' => $serviceOrderExecutionChecklistStep->checklist?->name,
            ]));
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_order_execution_checklist_steps', function (Blueprint $table) {
            $table->dropColumn('checklist_name');
        });
    }
};
