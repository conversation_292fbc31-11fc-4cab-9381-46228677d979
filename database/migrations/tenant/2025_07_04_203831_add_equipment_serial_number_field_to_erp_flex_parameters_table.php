<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('erp_flex_parameters', function (Blueprint $table) {
            $table->string('equipment_serial_number_field')
                ->nullable()
                ->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('erp_flex_parameters', function (Blueprint $table) {
            $table->dropColumn('equipment_serial_number_field');
        });
    }
};
