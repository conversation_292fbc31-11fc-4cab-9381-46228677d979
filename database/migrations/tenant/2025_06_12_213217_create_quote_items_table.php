<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quote_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quote_id')->constrained();
            $table->foreignId('service_order_id')->nullable()->constrained();
            $table->foreignId('service_order_execution_checklist_step_id')->nullable();
            $table->foreignId('service_order_execution_checklist_step_need_id')->nullable();
            $table->foreignId('equipment_id')->nullable()->constrained();
            $table->foreignId('service_type_id')->nullable()->constrained();
            $table->foreignId('product_id')->nullable()->constrained();
            $table->decimal('quantity');
            $table->decimal('unit_amount');
            $table->decimal('total_amount', 11, 2);
            $table->timestamps();

            $table->foreign('service_order_execution_checklist_step_id', 'qi_soecsi_foreign')
                ->references('id')
                ->on('service_order_execution_checklist_steps');

            $table->foreign('service_order_execution_checklist_step_need_id', 'qi_soecsni_foreign')
                ->references('id')
                ->on('service_order_execution_checklist_step_needs');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quote_items');
    }
};
