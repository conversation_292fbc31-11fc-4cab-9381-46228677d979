<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('checklist_steps', function (Blueprint $table) {
            $table->boolean('shows_in_quote')
                ->default(true)
                ->after('has_needs');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('checklist_steps', function (Blueprint $table) {
            $table->dropColumn('shows_in_quote');
        });
    }
};
