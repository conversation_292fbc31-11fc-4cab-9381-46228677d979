<?php

namespace Database\Seeders;

use App\Models\ErpFlexParameter;
use App\Models\IntegrationSetting;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        /** @var \App\Models\User $user */
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        Auth::loginUsingId(1);

        IntegrationSetting::create(['integration_type_id' => 1]);

        ErpFlexParameter::create();

        $this->call([
            RoleSeeder::class,
        ]);

        $user->syncRoles('Administrador');
    }
}
