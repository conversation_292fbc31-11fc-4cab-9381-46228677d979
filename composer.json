{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "repositories": [{"type": "composer", "url": "https://filament-kanban.composer.sh"}], "require": {"php": "^8.3", "awcodes/filament-table-repeater": "^3.1", "barryvdh/laravel-dompdf": "^3.1", "dotswan/filament-code-editor": "^1.1", "filament/filament": "^3.2", "heloufir/filament-kanban": "^2.1", "imanghafoori/laravel-masterpass": "^2.2", "laravel/framework": "^11.44", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.29", "leandrocfe/filament-apex-charts": "^3.1", "lorisleiva/laravel-actions": "^2.8", "maatwebsite/excel": "^3.1", "novadaemon/filament-pretty-json": "^2.3", "saade/filament-autograph": "^3.2", "saloonphp/laravel-plugin": "^3.5", "spatie/laravel-permission": "^6.10", "stancl/tenancy": "^3.8"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Support/Functions/datetime.php", "app/Support/Functions/encryption.php", "app/Support/Functions/erp_flex.php", "app/Support/Functions/error.php", "app/Support/Functions/notifications.php", "app/Support/Functions/ouess_tenant.php", "app/Support/Functions/report.php", "app/Support/Functions/route.php", "app/Support/Functions/text.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}