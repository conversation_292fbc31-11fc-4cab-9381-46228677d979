<?php

declare(strict_types=1);

use App\Actions\Reports\GenerateServiceOrdersExecutionReport;
use App\Actions\Reports\GenerateServiceOrdersReport;
use App\Actions\Quote\GenerateQuotePdf;
use App\Actions\ServiceOrder\GenerateServiceOrderPdf;
use App\Filament\Clusters\Services\Resources\ServiceOrderResource\Pages\ExecuteServiceOrder;
use App\Filament\Pages\EditNotificationSettings;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Filament\Clusters\Services\Resources\ServiceOrderResource\Pages\ServiceOrderKanban;
use App\Filament\Pages\Report\LoadServiceOrdersExecutionReport;
use App\Filament\Pages\Report\LoadServiceOrdersReport;
use App\Filament\Pages\UpdatePassword;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::get('/', function () {
        return route('filament.auth.login');
    });

    Route::prefix('app')->group(function () {
        Route::get('change-password', UpdatePassword::class)->name('filament.pages.change-password');
        Route::get('edit-notification-settings', EditNotificationSettings::class)->name('filament.app.pages.edit-notification-settings');

        Route::prefix('service-orders')->group(function () {
            Route::get('kanban', ServiceOrderKanban::class)->name('filament.app.services.pages.service-orders.kanban');
            Route::get('generate-pdf/{token}', GenerateServiceOrderPdf::class)->name('filament.app.services.pages.generate-service-order-pdf');
            Route::prefix('{service_order}')->group(function () {
                Route::get('execute', ExecuteServiceOrder::class)->name('filament.app.services.pages.execute-service-order');
            });
        });

        Route::prefix('quotes')->group(function () {
            Route::get('generate-pdf/{token}', GenerateQuotePdf::class)->name('filament.app.services.pages.generate-quote-pdf');
        });

        Route::prefix('reports')->group(function () {
            Route::get('load-service-orders-report', LoadServiceOrdersReport::class)->name('reports.load_general_service_orders_report');
            Route::get('generate-service-orders-report', GenerateServiceOrdersReport::class)->name('reports.generate_general_service_orders_report');
            Route::get('load-service-orders-execution-report', LoadServiceOrdersExecutionReport::class)->name('reports.load_general_service_orders_execution_report');
            Route::get('generate-service-orders-execution-report', GenerateServiceOrdersExecutionReport::class)->name('reports.generate_general_service_orders_execution_report');
        });
    });
});
