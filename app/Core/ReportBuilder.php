<?php

namespace App\Core;

use App\Actions\Reports\GenerateReportExcel;
use App\Core\Reports\Core\ReportFormat;
use Barryvdh\DomPDF\Facade\Pdf;
use Throwable;

class ReportBuilder
{
    public function __construct(
        protected ?string $format = null,
        protected ?string $reportName = null,
        protected ?array $reportData = null,
        protected ?string $excelFileName = null,
        protected ?string $reportExportClassName = null,
        protected ?string $notificationSubjectReportName = null,
        protected ?string $notificationBodyReportEntity = null,
        protected string $pdfReportPaper = 'a4',
        protected string $pdfReportOrientation = 'landscape'
    ) {}

    public function format(string $format): static
    {
        $this->format = $format;
        return $this;
    }

    public function reportName(string $reportName): static
    {
        $this->reportName = $reportName;
        return $this;
    }

    public function reportData(array $reportData): static
    {
        $this->reportData = $reportData;
        return $this;
    }

    public function excelFileName(string $excelFileName): static
    {
        $this->excelFileName = $excelFileName;
        return $this;
    }

    public function reportExportClassName(?string $reportExportClassName = null): static
    {
        $this->reportExportClassName = $reportExportClassName;
        return $this;
    }

    public function notificationSubjectReportName(string $notificationSubjectReportName): static
    {
        $this->notificationSubjectReportName = $notificationSubjectReportName;
        return $this;
    }

    public function notificationBodyReportEntity(string $notificationBodyReportEntity): static
    {
        $this->notificationBodyReportEntity = $notificationBodyReportEntity;
        return $this;
    }

    public function pdfReportPaper(string $pdfReportPaper): static
    {
        $this->pdfReportPaper = $pdfReportPaper;
        return $this;
    }

    public function pdfReportOrientation(string $pdfReportOrientation): static
    {
        $this->pdfReportOrientation = $pdfReportOrientation;
        return $this;
    }

    public function isExcelFormat(): bool
    {
        return $this->format === ReportFormat::EXCEL->value;
    }

    public function isPdfFormat(): bool
    {
        return $this->format === ReportFormat::PDF->value;
    }

    public function isScreenFormat(): bool
    {
        return $this->format === ReportFormat::SCREEN->value;
    }

    public function buildReport(): mixed
    {
        try {
            switch ($this->format) {
                case ReportFormat::EXCEL->value:
                    GenerateReportExcel::run(
                        auth()->id(),
                        $this->reportData,
                        $this->excelFileName,
                        $this->reportExportClassName,
                        $this->notificationSubjectReportName,
                        $this->notificationBodyReportEntity
                    );

                    success_notification(__('general.responses.report.excel.success'))->send();
                    return redirect()->route('filament.app.pages.list-reports');
                case ReportFormat::PDF->value:
                    return Pdf::loadView("app.reports.{$this->reportName}_report", $this->reportData, encoding: 'utf-8')
                        ->setPaper($this->pdfReportPaper, $this->pdfReportOrientation)
                        ->setOption(['isRemoteEnabled' => true])
                        ->stream();
                default:
                    return view("app.reports.{$this->reportName}_report", $this->reportData);
            }
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
