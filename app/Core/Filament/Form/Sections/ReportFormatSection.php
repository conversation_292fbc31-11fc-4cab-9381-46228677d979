<?php

namespace App\Core\Filament\Form\Sections;

use App\Enums\ReportFormatEnum;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;

class ReportFormatSection extends Section
{
    public static function build(): static
    {
        return self::make('Formato')
            ->compact()
            ->schema([
                Grid::make(4)->schema([
                    Select::make('format')
                        ->hiddenLabel()
                        ->options(ReportFormatEnum::getTranslated())
                ])
            ]);
    }
}
