<?php

namespace App\Core\Filament\Filters;

use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;

class TableActiveFilter extends Filter
{
    public static function build(string $resource): SelectFilter
    {
        return SelectFilter::make('active')
            ->label(__("$resource.forms.fields.active"))
            ->options([
                true => 'Somente ativos',
                false => 'Somente inativos',
            ])
            ->default(true);
    }
}
