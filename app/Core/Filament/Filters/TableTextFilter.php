<?php

namespace App\Core\Filament\Filters;

use Filament\Forms\Components\TextInput;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class TableTextFilter extends Filter
{
    public static function buildLike(string $resource, string $field, ?string $label = null): Filter
    {
        return Filter::make("$field")
            ->form([TextInput::make("$field")->label($label ?? __("$resource.forms.fields.$field"))])
            ->query(function (Builder $query, array $data) use ($field): Builder {
                return $query->when(!is_null($data[$field]) && $data[$field] !== '', function (Builder $query) use ($data, $field): Builder {
                    return $query->where($field, 'like', "%{$data[$field]}%");
                });
            });
    }

    public static function buildRelation(string $resource, string $field, string $relation, string $relationField): Filter
    {
        return Filter::make("$field")
            ->form([TextInput::make("$relationField")->label(__("$resource.forms.fields.$field"))])
            ->query(function (Builder $query, array $data) use ($relationField, $relation): Builder {
                return $query->when(!is_null($data[$relationField]) && $data[$relationField] !== '', function (Builder $query) use ($data, $relationField, $relation): Builder {
                    return $query->whereRelation($relation, $relationField, 'like', "%{$data[$relationField]}%");
                });
            });
    }
}
