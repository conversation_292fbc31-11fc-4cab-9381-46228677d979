<?php

namespace App\Core\Reports\Core;

use Throwable;

abstract class BaseReport
{
    protected string $pdfReportPaper = 'a4';
    protected string $pdfReportOrientation = 'landscape';
    protected string $currentFormat;
    protected string $excelFileName;
    protected string $reportName;
    protected ?string $reportExportClassName = null;
    protected string $notificationSubjectReportName;
    protected string $notificationBodyReportEntity;
    protected string $downloadFileName;
    protected array $reportData;

    /**
     * Get the report.
     *
     * @return mixed
     */
    protected function getReport(): mixed
    {
        try {
            return app_report()
                ->format($this->currentFormat)
                ->reportName($this->reportName)
                ->reportData($this->reportData)
                ->excelFileName($this->excelFileName)
                ->reportExportClassName($this->reportExportClassName)
                ->notificationSubjectReportName($this->notificationSubjectReportName)
                ->notificationBodyReportEntity($this->notificationBodyReportEntity)
                ->pdfReportPaper($this->pdfReportPaper)
                ->pdfReportOrientation($this->pdfReportOrientation)
                ->buildReport();
        } catch (Throwable $th) {
            throw $th;
        }
    }

    /**
     * Check if the report is being generated in a "screenable" (PDF or screen) format.
     *
     * @return bool
     */
    protected function isScreenable(): bool
    {
        return $this->currentFormat !== ReportFormat::EXCEL->value;
    }
}
