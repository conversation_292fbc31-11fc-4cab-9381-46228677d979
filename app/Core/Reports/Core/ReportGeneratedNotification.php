<?php

namespace App\Core\Reports\Core;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReportGeneratedNotification extends Notification
{
    use Queueable;

    public function __construct(
        private string $notificationSubjectReportName,
        private string $notificationBodyReportEntity,
        private string $filename,
        private string $downloadFileName
    ) {}

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage())
            ->subject(config('app.name') . " - $this->notificationSubjectReportName")
            ->greeting("Ol<PERSON>, {$notifiable->name}! Tudo bem?")
            ->line("O arquivo de $this->notificationBodyReportEntity já foi gerado e encontra-se anexado a este email.")
            ->salutation('Agradecemos por utilizar os nossos serviços!')
            ->attach(storage_path("app/$this->filename"), [
                'as' => "$this->downloadFileName.xlsx",
                'mime' => 'application/xlsx'
            ]);
    }
}
