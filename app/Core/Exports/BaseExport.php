<?php

namespace App\Core\Exports;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BaseExport implements ShouldAutoSize, WithStyles
{
    protected bool $useFilterHeadings = false;

    /**
     * Apply styling to columns, cells and rows.
     *
     * @param  \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        if ($this->useFilterHeadings) {
            $sheet->getRowDimension(1)->setRowHeight(26);
            $sheet->getStyle('A1')->getFont()->setBold(true);
            $sheet->mergeCells('B1:K1');
            $sheet->getStyle(1)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            $sheet->getStyle(1)->getFont()->getColor()->setRGB('FFFFFF');
            $sheet->getStyle(1)->getFill()->setFillType(Fill::FILL_SOLID);
            $sheet->getStyle(1)->getFill()->getStartColor()->setRGB('1D5C97');
            $sheet->getStyle(1)->getFill()->getEndColor()->setRGB('1D5C97');

            $sheet->getRowDimension(2)->setRowHeight(26);
            $sheet->getStyle(2)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle(2)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            $sheet->getStyle(2)->getFont()->setBold(true);
            $sheet->getStyle(2)->getFont()->getColor()->setRGB('FFFFFF');
            $sheet->getStyle(2)->getFill()->setFillType(Fill::FILL_SOLID);
            $sheet->getStyle(2)->getFill()->getStartColor()->setRGB('1D5C97');
            $sheet->getStyle(2)->getFill()->getEndColor()->setRGB('1D5C97');

            return;
        }

        $sheet->getRowDimension(1)->setRowHeight(26);
        $sheet->getStyle(1)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle(1)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle(1)->getFont()->setBold(true);
        $sheet->getStyle(1)->getFont()->getColor()->setRGB('FFFFFF');
        $sheet->getStyle(1)->getFill()->setFillType(Fill::FILL_SOLID);
        $sheet->getStyle(1)->getFill()->getStartColor()->setRGB('1D5C97');
        $sheet->getStyle(1)->getFill()->getEndColor()->setRGB('1D5C97');
    }
}
