<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;

class ErpFlexProductVariantWarehouseService extends ErpFlexBaseService
{
    public function getByProductVariantId(int $erpFlexProductVariantId): array
    {
        $selectColumns = [
            'SB2_SBW_ID',
            'SB2_SBW_IDSB1',
            'SB2_SBW_IDSB2',
            'SB2_SBW_IDSBW',
            'SB2_SBW_QAtu',
        ];

        $wheres = [
            "SB2_SBW_IDSB2 = $erpFlexProductVariantId"
        ];

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SB2', $selectColumns, $wheres))
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }
}
