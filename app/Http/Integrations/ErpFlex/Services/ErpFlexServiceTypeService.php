<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;
use Carbon\Carbon;

class ErpFlexServiceTypeService extends ErpFlexBaseService
{
    public function get(int $limit = 1000, int $offset = 0, ?Carbon $lastIntegrationAt = null): array
    {
        $selectColumns = [
            'SB1_ID',
            'SB1_Desc',
        ];

        $wheres = [
            "SB1_Tipo = 'SV'",
            "AND SB1_AutomComercial = 'S'",
        ];

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SB1', $selectColumns, $wheres, $limit, $offset, $lastIntegrationAt))
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }
}
