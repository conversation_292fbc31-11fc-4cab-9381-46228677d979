<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;
use Carbon\Carbon;

class ErpFlexProductCategoryService extends ErpFlexBaseService
{
    public function get(int $limit = 1000, int $offset = 0, ?Carbon $lastIntegrationAt = null): array
    {
        $sql = "
            select
                SBASUB.*
            from
                SBA SBASUB
            where
                exists (
                    select
                        1
                    from
                        SBA SBACAT
                    where
                        SBASUB.SBA_IDPAI = SBACAT.SBA_ID
                        and SBACAT.SBA_AutomComercial = 'S'
                )
        ";

        if ($lastIntegrationAt) {
            $sql .= "AND (SBASUB.SBA_DT_INC >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . "' OR SBASUB.SBA_DT_ALT >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . '\') ';
        }

        $sql .= "limit $limit offset $offset";

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($sql)
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }
}
