<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexCreateContractDto;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexUpdateContractDto;
use App\Http\Integrations\ErpFlex\Requests\Contract\ErpFlexCreateContractRequest;
use App\Http\Integrations\ErpFlex\Requests\Contract\ErpFlexUpdateContractRequest;
use App\Models\Contract;
use App\Models\ThirdPartyContract;

class ErpFlexContractService extends ErpFlexBaseService
{
    public function create(
        Contract $contract,
        ThirdPartyContract &$thirdPartyContract,
        ErpFlexCreateContractDto $erpFlexCreateContractDto,
    ): mixed {
        $request = new ErpFlexCreateContractRequest($erpFlexCreateContractDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $contract,
                $response,
                false,
                'erp_flex_contract.create.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $contract,
            response: $response,
            success: $body->success === 1,
            errorDescription: $body->success === 0
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        $thirdPartyContract->update([
            'third_party_sent_data' => ouess_aes256cbc_encrypt(json_encode($request->defaultBody()))
        ]);

        return $body;
    }

    public function update(ErpFlexUpdateContractDto $erpFlexUpdateContractDto, Contract $contract): mixed
    {
        $request = new ErpFlexUpdateContractRequest($erpFlexUpdateContractDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $contract,
                $response,
                false,
                'erp_flex_contract.update.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $contract,
            response: $response,
            success: $body->success === 1,
            errorDescription: $body->success === 0
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        return $body;
    }
}
