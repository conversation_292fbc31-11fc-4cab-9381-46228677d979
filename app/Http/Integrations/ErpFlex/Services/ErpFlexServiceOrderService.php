<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexServiceOrderDto;
use App\Http\Integrations\ErpFlex\Requests\ServiceOrder\ErpFlexCreateServiceOrderRequest;
use App\Http\Integrations\ErpFlex\Requests\ServiceOrder\ErpFlexUpdateServiceOrderRequest;
use App\Models\ServiceOrder;
use App\Models\ThirdPartyServiceOrder;

class ErpFlexServiceOrderService extends ErpFlexBaseService
{
    public function create(
        ServiceOrder $serviceOrder,
        ThirdPartyServiceOrder &$thirdPartyServiceOrder,
        ErpFlexServiceOrderDto $erpFlexServiceOrderDto
    ): mixed {
        $request = new ErpFlexCreateServiceOrderRequest($erpFlexServiceOrderDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $serviceOrder,
                $response,
                false,
                'erp_flex_order_service.create.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $serviceOrder,
            response: $response,
            success: isset($body->status)
                ? $body->status
                : false,
            errorDescription: isset($body->status) && !$body->status
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        $thirdPartyServiceOrder->update([
            'third_party_sent_data' => ouess_aes256cbc_encrypt(json_encode($request->defaultBody()))
        ]);

        return $body;
    }

    public function update(
        ServiceOrder $serviceOrder,
        ErpFlexServiceOrderDto $erpFlexServiceOrderDto
    ): mixed {
        $request = new ErpFlexUpdateServiceOrderRequest($erpFlexServiceOrderDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $serviceOrder,
                $response,
                false,
                'erp_flex_order_service.update.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $serviceOrder,
            response: $response,
            success: isset($body->status)
                ? $body->status
                : false,
            errorDescription: isset($body->status) && !$body->status
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        return $body;
    }
}
