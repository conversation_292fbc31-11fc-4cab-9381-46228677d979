<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;
use Carbon\Carbon;

class ErpFlexMeasuringUnitService extends ErpFlexBaseService
{
    public function get(int $limit = 1000, int $offset = 0, ?Carbon $lastIntegrationAt = null): array
    {
        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SB1_UM', [], [], $limit, $offset, $lastIntegrationAt))
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }
}
