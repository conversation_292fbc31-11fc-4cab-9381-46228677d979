<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;

class ErpFlexProductVariantService extends ErpFlexBaseService
{
    public function getByProductId(int $erpFlexProductId): object
    {
        $selectColumns = [
            'SB2_ID',
            'SB2_IDSB1',
            'SB2_EAN',
            'SB2_QAtu',
            'SB2_Reserva',
        ];

        $wheres = [
            "SB2_IDSB1 = $erpFlexProductId"
        ];

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SB2', $selectColumns, $wheres, 1))
        );

        $apiObjects = $this->getQueryResultsFromApi(
            json_decode($response->body())
        );

        return $apiObjects[0];
    }
}
