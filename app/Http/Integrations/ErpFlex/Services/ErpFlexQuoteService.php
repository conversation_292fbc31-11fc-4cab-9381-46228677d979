<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Quote\ErpFlexQuoteDto;
use App\Http\Integrations\ErpFlex\Requests\Quote\ErpFlexCreateQuoteRequest;
use App\Models\Quote;
use App\Models\ThirdPartyQuote;

class ErpFlexQuoteService extends ErpFlexBaseService
{
    public function create(
        Quote $quote,
        ThirdPartyQuote &$thirdPartyQuote,
        ErpFlexQuoteDto $erpFlexQuoteDto,
    ): mixed {
        $request = new ErpFlexCreateQuoteRequest($erpFlexQuoteDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $quote,
                $response,
                false,
                'erp_flex_quote.create.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $quote,
            response: $response,
            success: $body->success === 1,
            errorDescription: $body->success === 0
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        $thirdPartyQuote->update([
            'third_party_sent_data' => ouess_aes256cbc_encrypt(json_encode($request->defaultBody()))
        ]);

        return $body;
    }
}
