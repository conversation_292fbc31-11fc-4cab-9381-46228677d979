<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use App\Http\Integrations\ErpFlex\Requests\Customer\ErpFlexCreateCustomerRequest;
use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;
use App\Models\Customer;
use App\Models\ThirdPartyCustomer;
use Carbon\Carbon;

class ErpFlexCustomerService extends ErpFlexBaseService
{
    public function get(int $limit = 1000, int $offset = 0, ?Carbon $lastIntegrationAt = null): array
    {
        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SA1', [], [], $limit, $offset, $lastIntegrationAt))
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body()),
        );
    }

    public function getById(int $id): ?object
    {
        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SA1', [], ["SA1_ID = $id"], 1))
        );

        $apiObjects = $this->getQueryResultsFromApi(
            json_decode($response->body()),
        );

        return $apiObjects[0];
    }

    public function create(
        Customer $customer,
        ThirdPartyCustomer &$thirdPartyCustomer,
        ErpFlexCustomer $erpFlexCustomer
    ): mixed {
        $request = new ErpFlexCreateCustomerRequest($erpFlexCustomer);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $customer,
                $response,
                false,
                'erp_flex_order_service.create.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $customer,
            response: $response,
            success: $body->success === 1,
            errorDescription: $body->success === 0
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        $thirdPartyCustomer->update([
            'third_party_sent_data' => ouess_aes256cbc_encrypt(json_encode($request->defaultBody()))
        ]);

        return !empty($body->data)
            ? $body->data
            : null;
    }
}
