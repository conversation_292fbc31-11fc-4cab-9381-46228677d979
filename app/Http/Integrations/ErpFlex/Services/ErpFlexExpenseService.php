<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;

class ErpFlexExpenseService extends ErpFlexBaseService
{
    public function getByDocumentOrInvoiceNumber(string $invoiceNumber): array
    {
        $sql = "
            select
                SF1.SF1_ID,
                SF1.SF1_Doc,
                SF1.SF1_IDSA1,
                SF1.SF1_NrNFe,
                SF3.SF3_NrNFSe,
                SD1.SD1_ID,
                SD1.SD1_IDSB1,
                SD1.SD1_IDSB2,
                SD1.SD1_IDSBW,
                SD1.SD1_Doc,
                SD1.SD1_Quant,
                SD1.SD1_ValItem,
                SD4.SD4_Quant,
                SD4.SD4_IDSBW,
                SBL.SBL_ID,
                SBL.SBL_Desc,
                SBL.SBL_DtFabricacao
            from
                SF1 SF1
                left join SF3 SF3 on SF1.SF1_ID = SF3.SF3_IDSF1
                join SD1 SD1 on SF1.SF1_ID = SD1.SD1_IDSF1
                join SD3 SD3 on SD1.SD1_ID = SD3.SD3_IDSD1
                left join SD4 SD4 on SD3.SD3_ID = SD4.SD4_IDSD3
                left join SBL SBL on SD4.SD4_IDSBL = SBL.SBL_ID
            where
                SF1.SF1_Doc = '$invoiceNumber'
                or SF1.SF1_NrNFe = '$invoiceNumber'
                or SF3.SF3_NrNFSe = '$invoiceNumber'
        ";

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($sql)
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }
}
