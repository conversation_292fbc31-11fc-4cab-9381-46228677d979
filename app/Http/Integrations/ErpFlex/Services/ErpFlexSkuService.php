<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;

class ErpFlexSkuService extends ErpFlexBaseService
{
    public function getByProductId(int $erpFlexProductId): object
    {
        $selectColumns = [
            'SB2_ID',
            'SB2_IDSB1',
            'SB2_EAN',
        ];

        $wheres = [
            "SB2_IDSB1 = $erpFlexProductId"
        ];

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SB2', $selectColumns, $wheres))
        );

        $apiObjects = $this->getQueryResultsFromApi(
            json_decode($response->body())
        );

        return $apiObjects[0];
    }
}
