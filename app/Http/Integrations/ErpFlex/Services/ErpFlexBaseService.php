<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use Carbon\Carbon;

class ErpFlexBaseService
{
    public ErpFlexConnector $connector;

    public function __construct(
        protected ?string $username = null,
        protected ?string $password = null,
    ) {
        $tenantErpFlexApiCredentials = get_erp_flex_api_credentials();

        $this->username ??= $tenantErpFlexApiCredentials['username'];
        $this->password ??= $tenantErpFlexApiCredentials['password'];

        $this->connector = new ErpFlexConnector($this->username, $this->password);
    }

    public static function make(?string $username = null, ?string $password = null): static
    {
        return new static($username, $password);
    }

    public function buildBaseGetSql(
        string $tableName,
        array $columns = [],
        array $wheres = [],
        int $limit = 1000,
        int $offset = 0,
        ?Carbon $lastIntegrationAt = null
    ): string {
        if (!empty($columns)) {
            $selectColumns = implode(', ', $columns);
        } else {
            $selectColumns = '*';
        }

        $sql = "SELECT $selectColumns FROM $tableName ";

        if (count($wheres) > 0) {
            for ($i = 0; $i < count($wheres); $i++) {
                if ($i === 0) {
                    $sql .= "WHERE {$wheres[$i]} ";
                    continue;
                }

                $sql .= "{$wheres[$i]} ";
            }
        }

        if ($lastIntegrationAt) {
            $sql .= (count($wheres) > 0)
                ? ("WHERE ({$tableName}_DT_INC >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . "' OR {$tableName}_DT_ALT >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . '\') ')
                : ("AND ({$tableName}_DT_INC >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . "' OR {$tableName}_DT_ALT >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . '\') ');
        }

        $sql .= "LIMIT $limit OFFSET $offset";

        return $sql;
    }

    public function getQueryResultsFromApi(object $decodedApiResponse): array
    {
        return $decodedApiResponse->query ?? [];
    }
}
