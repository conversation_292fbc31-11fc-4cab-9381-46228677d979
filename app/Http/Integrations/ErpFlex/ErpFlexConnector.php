<?php

namespace App\Http\Integrations\ErpFlex;

use Saloon\Http\Auth\BasicAuthenticator;
use Saloon\Http\Connector;
use Saloon\Traits\Plugins\AcceptsJson;

class ErpFlexConnector extends Connector
{
    use AcceptsJson;

    public function __construct(
        protected ?string $username = null,
        protected ?string $password = null
    ) {
        $tenantErpFlexApiCredentials = get_erp_flex_api_credentials();

        $this->username ??= $tenantErpFlexApiCredentials['username'];
        $this->password ??= $tenantErpFlexApiCredentials['password'];

        $this->authenticate(new BasicAuthenticator($this->username, $this->password));
    }

    public function resolveBaseUrl(): string
    {
        return tenant('erp_flex')['environment'] === 'production'
            ? config('erp_flex.api.production.url')
            : config('erp_flex.api.development.url');
    }

    protected function defaultHeaders(): array
    {
        return [];
    }

    protected function defaultConfig(): array
    {
        return [];
    }
}
