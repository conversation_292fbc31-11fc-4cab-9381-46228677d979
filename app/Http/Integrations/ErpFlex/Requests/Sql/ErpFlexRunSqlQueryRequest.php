<?php

namespace App\Http\Integrations\ErpFlex\Requests\Sql;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class ErpFlexRunSqlQueryRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected string $sql)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/api_v2/consulta/query';
    }

    public function defaultBody(): array
    {
        return ['query' => $this->sql];
    }
}
