<?php

namespace App\Http\Integrations\ErpFlex\Requests\Contract;

use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexUpdateContractDto;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use <PERSON>oon\Traits\Body\HasJsonBody;

class ErpFlexUpdateContractRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::PUT;

    public function __construct(protected ErpFlexUpdateContractDto $erpFlexUpdateContractDto) {}

    public function resolveEndpoint(): string
    {
        return '/api/contrato';
    }

    public function defaultBody(): array
    {
        return $this->erpFlexUpdateContractDto->toArray();
    }
}
