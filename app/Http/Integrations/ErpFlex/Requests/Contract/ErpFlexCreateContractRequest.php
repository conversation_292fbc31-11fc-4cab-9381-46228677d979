<?php

namespace App\Http\Integrations\ErpFlex\Requests\Contract;

use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexCreateContractDto;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use <PERSON>oon\Traits\Body\HasJsonBody;

class ErpFlexCreateContractRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(private ErpFlexCreateContractDto $erpFlexCreateContractDto) {}

    public function resolveEndpoint(): string
    {
        return '/api/contrato';
    }

    public function defaultBody(): array
    {
        return $this->erpFlexCreateContractDto->toArray();
    }
}
