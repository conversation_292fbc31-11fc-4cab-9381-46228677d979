<?php

namespace App\Http\Integrations\ErpFlex\Requests\ServiceOrder;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexServiceOrderDto;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class ErpFlexCreateServiceOrderRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected ErpFlexServiceOrderDto $erpFlexServiceOrderDto) {}

    public function resolveEndpoint(): string
    {
        return '/api_v2/os';
    }

    public function defaultBody(): array
    {
        return $this->erpFlexServiceOrderDto->toArray();
    }
}
