<?php

namespace App\Http\Integrations\ErpFlex\Requests\Quote;

use App\Http\Integrations\ErpFlex\DataTransferObjects\Quote\ErpFlexQuoteDto;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class ErpFlexCreateQuoteRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected ErpFlexQuoteDto $erpFlexQuoteDto) {}

    public function resolveEndpoint(): string
    {
        return '/api/orcamento';
    }

    public function defaultBody(): array
    {
        return $this->erpFlexQuoteDto->toArray(true);
    }
}
