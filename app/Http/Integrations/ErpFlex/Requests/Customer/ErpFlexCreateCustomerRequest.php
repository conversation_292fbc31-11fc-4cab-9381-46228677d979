<?php

namespace App\Http\Integrations\ErpFlex\Requests\Customer;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class ErpFlexCreateCustomerRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected ErpFlexCustomer $erpFlexCustomer) {}

    public function resolveEndpoint(): string
    {
        return '/api/cliente';
    }

    public function defaultBody(): array
    {
        return $this->erpFlexCustomer->toArray(true);
    }
}
