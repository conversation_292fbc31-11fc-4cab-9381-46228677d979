<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects\Quote;

class ErpFlexQuoteDto
{
    public function __construct(
        public string $documento,
        public string $emissao,
        public string $cliente_id,
        public string $vendedor_id,
        public ?string $historico,
        public string $itens,
        public ?int $tabela_preco = null,
        public ?string $campo1 = null,
        public ?string $campo2 = null,
        public ?string $id = null,
    ) {}

    public function toArray(bool $create = false): array
    {
        $data = array_filter([
            'documento' => $this->documento,
            'emissao' => $this->emissao,
            'cliente_id' => $this->cliente_id,
            'vendedor_id' => $this->vendedor_id,
            'historico' => $this->historico,
            'tabela_preco' => $this->tabela_preco,
            'itens' => $this->itens,
            'campo1' => $this->campo1,
            'campo2' => $this->campo2,
            'id' => $this->id,
        ]);

        if ($create) {
            unset($data['id']);
        }

        return $data;
    }
}
