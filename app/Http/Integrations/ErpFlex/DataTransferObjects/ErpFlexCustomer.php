<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects;

class ErpFlexCustomer
{
    /**
     * Create a new instance.
     *
     * @param  string|null nome
     * @param  string|null fantasia
     * @param  string|null cpf_cnpj
     * @param  string|null id
     * @param  string|null pessoa_classe
     * @param  string|null inscricao_estadual
     * @param  string|null inscricao_municipal
     * @param  string|null email
     * @param  string|null ddi
     * @param  string|null ddd_residencial
     * @param  string|null telefone_residencial
     * @param  string|null ddd_celular
     * @param  string|null telefone_celular
     * @param  string|null ddd_comercial
     * @param  string|null telefone_comercial
     * @param  string|null cep
     * @param  string|null endereco
     * @param  string|null numero
     * @param  string|null complemento
     * @param  string|null bairro
     * @param  string|null municipio
     * @param  string|null estado
     * @param  string|null pais
     * @param  string|null sexo
     * @param  string|null situacao_icms
     * @param  string|null status
     * @param  string|null parcelas
     * @param  string|null tabela
     * @param  string|null grupo_tensao
     * @param  string|null adicional_1
     * @param  string|null adicional_2
     * @param  string|null adicional_3
     * @param  string|null adicional_4
     * @param  string|null adicional_5
     * @param  string|null adicional_6
     */
    public function __construct(
        public ?string $nome,
        public ?string $cpf_cnpj,
        public ?int $id = null,
        public ?string $fantasia = null,
        public ?string $pessoa_classe = null,
        public ?string $inscricao_estadual = null,
        public ?string $inscricao_municipal = null,
        public ?string $email = null,
        public ?string $ddi = null,
        public ?string $ddd_residencial = null,
        public ?string $telefone_residencial = null,
        public ?string $ddd_celular = null,
        public ?string $telefone_celular = null,
        public ?string $ddd_comercial = null,
        public ?string $telefone_comercial = null,
        public ?string $cep = null,
        public ?string $endereco = null,
        public ?string $numero = null,
        public ?string $complemento = null,
        public ?string $bairro = null,
        public ?string $municipio = null,
        public ?string $estado = null,
        public ?string $pais = null,
        public ?string $sexo = null,
        public ?string $situacao_icms = null,
        public ?string $status = null,
        public ?string $parcelas = null,
        public ?string $tabela = null,
        public ?string $grupo_tensao = null,
        public ?string $adicional_1 = null,
        public ?string $adicional_2 = null,
        public ?string $adicional_3 = null,
        public ?string $adicional_4 = null,
        public ?string $adicional_5 = null,
        public ?string $adicional_6 = null
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(bool $create = false): array
    {
        $data = array_filter([
            'nome' => $this->nome,
            'cpf_cnpj' => $this->cpf_cnpj,
            'id' => $this->id,
            'fantasia' => $this->fantasia,
            'pessoa_classe' => $this->pessoa_classe,
            'inscricao_estadual' => $this->inscricao_estadual,
            'inscricao_municipal' => $this->inscricao_municipal,
            'email' => $this->email,
            'ddi' => $this->ddi,
            'ddd_residencial' => $this->ddd_residencial,
            'telefone_residencial' => $this->telefone_residencial,
            'ddd_celular' => $this->ddd_celular,
            'telefone_celular' => $this->telefone_celular,
            'ddd_comercial' => $this->ddd_comercial,
            'telefone_comercial' => $this->telefone_comercial,
            'cep' => $this->cep,
            'endereco' => $this->endereco,
            'numero' => $this->numero,
            'complemento' => $this->complemento,
            'bairro' => $this->bairro,
            'municipio' => $this->municipio,
            'estado' => $this->estado,
            'pais' => $this->pais,
            'sexo' => $this->sexo,
            'situacao_icms' => $this->situacao_icms,
            'status' => $this->status,
            'parcelas' => $this->parcelas,
            'tabela' => $this->tabela,
            'grupo_tensao' => $this->grupo_tensao,
            'adicional_1' => $this->adicional_1,
            'adicional_2' => $this->adicional_2,
            'adicional_3' => $this->adicional_3,
            'adicional_4' => $this->adicional_4,
            'adicional_5' => $this->adicional_5,
            'adicional_6' => $this->adicional_6,
        ]);

        if ($create) {
            unset($data['id']);
        }

        return $data;
    }
}
