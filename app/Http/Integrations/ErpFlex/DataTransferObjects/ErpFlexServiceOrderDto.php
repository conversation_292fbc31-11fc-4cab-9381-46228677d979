<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects;

class ErpFlexServiceOrderDto
{
    public function __construct(
        public string $estrutura_serv,
        public string $documento,
        public float $quantidade,
        public string $emissao,
        public string $data_producao,
        public string $cliente_id,
        public string $servico_id,
        public string $variante_id,
        public array $requisicao_prod,
        public string $orcamento_id = '0',
        public string $item_orcamento_id = '0',
        public ?float $valor = null,
        public ?string $saldo_anterior = null,
        public ?string $saldo_atual = null,
        public ?string $previsao = null,
        public ?string $campo_1 = null,
        public ?int $os_id = null,
    ) {}

    public function toArray(): array
    {
        $data = [
            'estrutura_serv' => $this->estrutura_serv,
            'documento' => $this->documento,
            'quantidade' => $this->quantidade,
            'valor' => $this->valor,
            'saldo_anterior' => $this->saldo_anterior,
            'saldo_atual' => $this->saldo_atual,
            'emissao' => $this->emissao,
            'previsao' => $this->previsao,
            'data_producao' => $this->data_producao,
            'cliente_id' => $this->cliente_id,
            'servico_id' => $this->servico_id,
            'variante_id' => $this->variante_id,
            'requisicao_prod' => $this->requisicao_prod,
            'orcamento_id' => $this->orcamento_id,
            'item_orcamento_id' => $this->item_orcamento_id,
            'campo_1' => $this->campo_1,
            'os_id' => $this->os_id,
        ];

        if (is_null($this->os_id)) {
            unset($data['os_id']);
        }

        return $data;
    }
}
