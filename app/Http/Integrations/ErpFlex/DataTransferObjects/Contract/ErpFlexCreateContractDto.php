<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects\Contract;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexArrayable;

class ErpFlexCreateContractDto implements ErpFlexArrayable
{
    public function __construct(
        private string $documento,
        private string $id_cliente,
        private int $status,
        private string $vigenciade,
        private string $faturamento,
        private string $tipo,
        private string $tipo_geracao,
        private string $global_individual,
        private array $item,
        private ?string $assinatura = null,
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'documento' => $this->documento,
            'id_cliente' => $this->id_cliente,
            'status' => $this->status,
            'vigenciade' => $this->vigenciade,
            'faturamento' => $this->faturamento,
            'tipo' => $this->tipo,
            'tipo_geracao' => $this->tipo_geracao,
            'global_individual' => $this->global_individual,
            'item' => json_encode(array_map(fn(ErpFlexCreateContractItemDto $erpFlexCreateContractItemDto): array => $erpFlexCreateContractItemDto->toArray(), $this->item)),
            'assinatura' => $this->assinatura,
        ]);
    }
}
