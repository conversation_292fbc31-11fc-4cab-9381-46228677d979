<?php

namespace App\Http\Integrations\Receita\Services;

use App\Http\Integrations\Receita\ReceitaConnector;
use App\Http\Integrations\Receita\Requests\Cnpj\ReceitaCnpjGetCompanyDetailsRequest;
use App\Http\Integrations\Receita\Responses\Cnpj\ReceitaCnpjGetCompanyDetailsResponse;

class ReceitaCnpjService extends ReceitaBaseService
{
    /**
     * Get the company's details.
     *
     * @param  string $taxIdNumber
     * @return \App\Http\Integrations\Receita\Responses\Cnpj\ReceitaCnpjGetCompanyDetailsResponse
     */
    public function getCompanyDetails(string $taxIdNumber): ReceitaCnpjGetCompanyDetailsResponse
    {
        $connector = new ReceitaConnector();

        $request = new ReceitaCnpjGetCompanyDetailsRequest($taxIdNumber);

        $response = $connector->send($request);

        $decodedResponse = json_decode($response->body());

        return new ReceitaCnpjGetCompanyDetailsResponse(
            nome: $decodedResponse->nome,
            fantasia: $decodedResponse->fantasia,
            email: $decodedResponse->email,
            cep: $decodedResponse->cep,
            logradouro: $decodedResponse->logradouro,
            numero: $decodedResponse->numero,
            complemento: $decodedResponse->complemento,
            bairro: $decodedResponse->bairro,
            municipio: $decodedResponse->municipio,
            uf: $decodedResponse->uf,
            status: $decodedResponse->status
        );
    }
}
