<?php

namespace App\Http\Integrations\Receita\Requests\Cnpj;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class ReceitaCnpjGetCompanyDetailsRequest extends Request
{
    /**
     * The HTTP method of the request
     */
    protected Method $method = Method::GET;

    /**
     * Create a new instance.
     *
     * @param  string $taxIdNumber
     */
    public function __construct(protected string $taxIdNumber)
    {
    }

    /**
     * The endpoint for the request
     */
    public function resolveEndpoint(): string
    {
        return '/v1/cnpj/' . preg_replace('/[^0-9]+/', '', $this->taxIdNumber);
    }
}
