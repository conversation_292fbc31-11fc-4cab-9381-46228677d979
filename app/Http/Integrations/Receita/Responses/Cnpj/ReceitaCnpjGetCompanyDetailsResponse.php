<?php

namespace App\Http\Integrations\Receita\Responses\Cnpj;

class ReceitaCnpjGetCompanyDetailsResponse
{
    /**
     * Create a new instance.
     *
     * @param  string|null $nome
     * @param  string|null $fantasia
     * @param  string|null $email
     * @param  string|null $cep
     * @param  string|null $logradouro
     * @param  string|null $numero
     * @param  string|null $complemento
     * @param  string|null $bairro
     * @param  string|null $municipio
     * @param  string|null $uf
     * @param  string|null $status
     */
    public function __construct(
        public ?string $nome,
        public ?string $fantasia,
        public ?string $email,
        public ?string $cep,
        public ?string $logradouro,
        public ?string $numero,
        public ?string $complemento,
        public ?string $bairro,
        public ?string $municipio,
        public ?string $uf,
        public ?string $status,
    ) {
    }
}
