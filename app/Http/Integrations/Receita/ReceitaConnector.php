<?php

namespace App\Http\Integrations\Receita;

use Saloon\Http\Connector;
use Saloon\Traits\Plugins\AcceptsJson;

class ReceitaConnector extends Connector
{
    use AcceptsJson;

    /**
     * The Base URL of the API
     */
    public function resolveBaseUrl(): string
    {
        return config('services.receita.api.url');
    }

    /**
     * Default headers for every request
     */
    protected function defaultHeaders(): array
    {
        return [];
    }

    /**
     * Default HTTP client options
     */
    protected function defaultConfig(): array
    {
        return [];
    }
}
