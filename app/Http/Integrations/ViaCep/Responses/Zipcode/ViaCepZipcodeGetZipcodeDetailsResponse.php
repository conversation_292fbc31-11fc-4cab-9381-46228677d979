<?php

namespace App\Http\Integrations\ViaCep\Responses\Zipcode;

class ViaCepZipcodeGetZipcodeDetailsResponse
{
    /**
     * Create a new instance.
     *
     * @param  string|null $cep
     * @param  string|null $logradouro
     * @param  string|null $complemento
     * @param  string|null $bairro
     * @param  string|null $localidade
     * @param  string|null $uf
     * @param  string|null $ibge
     * @param  string|null $gia
     * @param  string|null $ddd
     * @param  string|null $siafi
     * @param  string|null $erro
     */
    public function __construct(
        public ?string $cep,
        public ?string $logradouro,
        public ?string $complemento,
        public ?string $bairro,
        public ?string $localidade,
        public ?string $uf,
        public ?string $ibge,
        public ?string $gia,
        public ?string $ddd,
        public ?string $siafi,
        public ?string $erro,
    ) {
    }
}
