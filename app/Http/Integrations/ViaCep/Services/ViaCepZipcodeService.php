<?php

namespace App\Http\Integrations\ViaCep\Services;

use App\Http\Integrations\ViaCep\ViaCepConnector;
use App\Http\Integrations\ViaCep\Requests\Zipcode\ViaCepZipcodeGetZipcodeDetailsRequest;
use App\Http\Integrations\ViaCep\Responses\Zipcode\ViaCepZipcodeGetZipcodeDetailsResponse;

class ViaCepZipcodeService extends ViaCepBaseService
{
    /**
     * Get the company's details.
     *
     * @param  string $zipcode
     * @return \App\Http\Integrations\ViaCep\Responses\Zipcode\ViaCepZipcodeGetZipcodeDetailsResponse
     */
    public function getZipcodeDetails(string $zipcode): ViaCepZipcodeGetZipcodeDetailsResponse
    {
        $connector = new ViaCepConnector();

        $request = new ViaCepZipcodeGetZipcodeDetailsRequest($zipcode);

        $response = $connector->send($request);

        $decodedResponse = json_decode($response->body());

        return new ViaCepZipcodeGetZipcodeDetailsResponse(
            cep: $decodedResponse->cep,
            logradouro: $decodedResponse->logradouro,
            complemento: $decodedResponse->complemento,
            bairro: $decodedResponse->bairro,
            localidade: $decodedResponse->localidade,
            uf: $decodedResponse->uf,
            ibge: $decodedResponse->ibge,
            gia: $decodedResponse->gia,
            ddd: $decodedResponse->ddd,
            siafi: $decodedResponse->siafi,
            erro: $decodedResponse->erro ?? null,
        );
    }
}
