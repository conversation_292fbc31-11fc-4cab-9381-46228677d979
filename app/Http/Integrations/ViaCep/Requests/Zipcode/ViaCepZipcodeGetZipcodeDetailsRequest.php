<?php

namespace App\Http\Integrations\ViaCep\Requests\Zipcode;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class ViaCepZipcodeGetZipcodeDetailsRequest extends Request
{
    /**
     * The HTTP method of the request
     */
    protected Method $method = Method::GET;

    /**
     * Create a new instance.
     *
     * @param  string $zipcode
     */
    public function __construct(protected string $zipcode)
    {
    }

    /**
     * The endpoint for the request
     */
    public function resolveEndpoint(): string
    {
        return "/$this->zipcode/json";
    }
}
