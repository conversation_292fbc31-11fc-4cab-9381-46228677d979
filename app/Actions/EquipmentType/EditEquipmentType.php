<?php

namespace App\Actions\EquipmentType;

use App\Models\EquipmentType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditEquipmentType
{
    use AsAction;

    public function handle(EquipmentType $equipmentType, array $data): EquipmentType
    {
        try {
            $equipmentType->update($data);
            return $equipmentType;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
