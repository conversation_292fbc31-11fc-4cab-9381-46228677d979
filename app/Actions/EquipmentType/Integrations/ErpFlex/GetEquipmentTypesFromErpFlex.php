<?php

namespace App\Actions\EquipmentType\Integrations\ErpFlex;

use App\Actions\EquipmentType\CreateEquipmentType;
use App\Actions\EquipmentType\EditEquipmentType;
use App\Actions\ThirdPartyEquipmentType\CreateThirdPartyEquipmentType;
use App\Actions\ThirdPartyEquipmentType\EditThirdPartyEquipmentType;
use App\Actions\ThirdPartyEquipmentType\Queries\GetThirdPartyEquipmentTypeByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductCategoryService;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\EquipmentType;
use App\Models\ThirdPartyEquipmentType;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetEquipmentTypesFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexProductCategoryService $erpFlexProductCategoryService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.equipment.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexProductCategoryService = new ErpFlexProductCategoryService();

        $this->integrateEquipmentTypes();

        if ($userId) {
            success_database_notification($userId, __('equipment_types.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateEquipmentTypes(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_equipment_types_received_at'])
                ? carbon($this->integrationSetting->settings['last_equipment_types_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiEquipmentTypes = $this->erpFlexProductCategoryService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiEquipmentTypes as $erpFlexEquipmentType) {
                try {
                    $this->processSingleEquipmentType($erpFlexEquipmentType);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiEquipmentTypes) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_equipment_types_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleEquipmentType(object $erpFlexEquipmentType): void
    {
        $this->createOrUpdateEquipmentType(
            $this->createOrUpdateThirdPartyEquipmentType($erpFlexEquipmentType),
            $erpFlexEquipmentType
        );
    }

    private function createOrUpdateThirdPartyEquipmentType(object $erpFlexEquipmentType): ThirdPartyEquipmentType
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexEquipmentType->SBA_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexEquipmentType), true),
        ];

        /** @var \App\Models\ThirdPartyEquipmentType|null $thirdPartyEquipmentType */
        $thirdPartyEquipmentType = GetThirdPartyEquipmentTypeByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexEquipmentType->SBA_ID);

        return is_null($thirdPartyEquipmentType)
            ? CreateThirdPartyEquipmentType::run($data)
            : EditThirdPartyEquipmentType::run($thirdPartyEquipmentType, $data);
    }

    private function createOrUpdateEquipmentType(ThirdPartyEquipmentType $thirdPartyEquipmentType, object $erpFlexEquipmentType): EquipmentType
    {
        $data = [
            'name' => $erpFlexEquipmentType->SBA_Desc,
        ];

        if (is_null($thirdPartyEquipmentType->equipment_type_id)) {
            /** @var \App\Models\EquipmentType $equipmentType */
            $equipmentType = CreateEquipmentType::run($data);

            EditThirdPartyEquipmentType::run($thirdPartyEquipmentType, ['equipment_type_id' => $equipmentType->id]);

            return $equipmentType;
        }

        return EditEquipmentType::run($thirdPartyEquipmentType->equipmentType, $data);
    }
}
