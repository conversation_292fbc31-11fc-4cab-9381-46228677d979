<?php

namespace App\Actions\EquipmentType\Queries;

use App\Models\EquipmentType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetEquipmentTypes
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string|null $orderBy
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function handle(?string $orderBy = null): Collection
    {
        return EquipmentType::query()
            ->when($orderBy, fn(Builder $query): Builder => $query->orderBy($orderBy))
            ->get();
    }
}
