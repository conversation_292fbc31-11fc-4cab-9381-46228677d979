<?php

namespace App\Actions\ServiceType\Integrations\ErpFlex;

use App\Actions\ServiceType\CreateServiceType;
use App\Actions\ServiceType\EditServiceType;
use App\Actions\ThirdPartyServiceType\CreateThirdPartyServiceType;
use App\Actions\ThirdPartyServiceType\EditThirdPartyServiceType;
use App\Actions\ThirdPartyServiceType\Queries\GetThirdPartyServiceTypeByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexServiceTypeService;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\ServiceType;
use App\Models\ThirdPartyServiceType;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetServiceTypesFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexServiceTypeService $erpFlexServiceTypeService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.service_types.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexServiceTypeService = new ErpFlexServiceTypeService();

        $this->integrateServiceTypes();

        if ($userId) {
            success_database_notification($userId, __('service_types.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateServiceTypes(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_service_types_received_at'])
                ? carbon($this->integrationSetting->settings['last_service_types_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiServiceTypes = $this->erpFlexServiceTypeService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiServiceTypes as $erpFlexApiServiceType) {
                try {
                    $this->processSingleServiceType($erpFlexApiServiceType);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiServiceTypes) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_service_types_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleServiceType(object $erpFlexApiServiceType): void
    {
        $this->createOrUpdateServiceType(
            $this->createOrUpdateThirdPartyServiceType($erpFlexApiServiceType),
            $erpFlexApiServiceType
        );
    }

    private function createOrUpdateThirdPartyServiceType(object $erpFlexApiServiceType): ThirdPartyServiceType
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexApiServiceType->SB1_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexApiServiceType), true),
        ];

        /** @var \App\Models\ThirdPartyServiceType|null $thirdPartyServiceType */
        $thirdPartyServiceType = GetThirdPartyServiceTypeByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexApiServiceType->SB1_ID);

        return is_null($thirdPartyServiceType)
            ? CreateThirdPartyServiceType::run($data)
            : EditThirdPartyServiceType::run($thirdPartyServiceType, $data);
    }

    private function createOrUpdateServiceType(ThirdPartyServiceType $thirdPartyServiceType, object $erpFlexApiServiceType): ServiceType
    {
        $data = [
            'name' => $erpFlexApiServiceType->SB1_Desc,
            'estimated_duration_in_minutes' => 60,
        ];

        if (is_null($thirdPartyServiceType->service_type_id)) {
            /** @var \App\Models\ServiceType $serviceType */
            $serviceType = CreateServiceType::run($data);

            EditThirdPartyServiceType::run($thirdPartyServiceType, ['service_type_id' => $serviceType->id]);

            return $serviceType;
        }

        return EditServiceType::run($thirdPartyServiceType->serviceType, $data);
    }
}
