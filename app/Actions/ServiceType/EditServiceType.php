<?php

namespace App\Actions\ServiceType;

use App\Models\ServiceType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditServiceType
{
    use AsAction;

    public function handle(ServiceType $serviceType, array $data): ServiceType
    {
        try {
            $serviceType->update($data);
            return $serviceType;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
