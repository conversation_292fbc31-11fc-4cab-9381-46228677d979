<?php

namespace App\Actions\ServiceType\Queries;

use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetServiceTypeByName
{
    use AsAction;

    public function handle(string $search, bool $orderByName = false): Collection
    {
        return ServiceType::query()
            ->where('name', 'like', "%$search%")
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
