<?php

namespace App\Actions\ServiceType\Queries;

use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetServiceTypes
{
    use AsAction;

    public function handle(bool $orderByName = false): Collection
    {
        return ServiceType::query()
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
