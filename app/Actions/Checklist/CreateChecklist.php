<?php

namespace App\Actions\Checklist;

use App\Models\Checklist;
use Illuminate\Support\Facades\DB;
use Lori<PERSON><PERSON>\Actions\Concerns\AsAction;
use Throwable;

class CreateChecklist
{
    use AsAction;

    public function handle(array $data): Checklist
    {
        try {
            return DB::transaction(function () use ($data): Checklist {
                /** @var \App\Models\Checklist $checklist */
                $checklist = Checklist::create($data);

                if (isset($data['checklist_steps'])) {
                    foreach ($data['checklist_steps'] as $checklistStepData) {
                        /** @var \App\Models\ChecklistStep $checklistStep */
                        $checklistStep = $checklist->checklistSteps()->create($checklistStepData);

                        if (isset($checklistStepData['checklist_step_options'])) {
                            $checklistStepOptionsData = collect($checklistStepData['checklist_step_options'])
                                ->filter(fn (array $checklistStepOptionData): bool => isset($checklistStepOptionData['sequence']) && !is_null($checklistStepOptionData['sequence']))
                                ->toArray();

                            foreach ($checklistStepOptionsData as $checklistStepOptionData) {
                                $checklistStep->checklistStepOptions()->create($checklistStepOptionData);
                            }
                        }
                    }
                }

                if ($data['use_report_template']) {
                    $checklist->checklistReportTemplates()->create([
                        'template' => $data['template'],
                        'orientation' => $data['orientation'],
                    ]);
                }

                return $checklist;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
