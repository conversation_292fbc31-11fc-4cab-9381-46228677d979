<?php

namespace App\Actions\Checklist;

use App\Models\Checklist;
use App\Models\ChecklistStep;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditChecklist
{
    use AsAction;

    private Checklist $checklist;

    public function handle(Checklist $checklist, array $data): Checklist
    {
        $this->checklist = $checklist;

        try {
            return DB::transaction(function () use ($data): Checklist {
                $checklistSteps = $data['checklist_steps'];
                unset($data['checklist_steps']);

                $this->checklist->update($data);

                $this->syncChecklistSteps($checklistSteps);

                if ($data['use_report_template']) {
                    $this->syncChecklistReportTemplate($data);
                }

                return $this->checklist;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    private function syncChecklistSteps(array $checklistSteps): void
    {
        $existingSteps = $this->checklist->checklistSteps->keyBy('id');

        $processedItemIds = [];

        collect($checklistSteps)->each(function (array $stepData) use (&$processedItemIds, $existingSteps) {
            if (isset($stepData['id']) && $existingSteps->has($stepData['id'])) {
                $checklistStep = $existingSteps->get($stepData['id']);
                $checklistStep->update($stepData);

                $this->syncChecklistStepOptions($checklistStep, $stepData);

                $processedItemIds[] = $stepData['id'];
            } else {
                /** @var \App\Models\ChecklistStep $checklistStep */
                $checklistStep = $this->checklist->checklistSteps()->create($stepData);

                if (isset($stepData['checklist_step_options'])) {
                    $checklistStepOptionsData = collect($stepData['checklist_step_options'])
                        ->filter(fn (array $checklistStepOptionData): bool => isset($checklistStepOptionData['sequence']) && !is_null($checklistStepOptionData['sequence']))
                        ->toArray();

                    foreach ($checklistStepOptionsData as $checklistStepOptionData) {
                        $checklistStep->checklistStepOptions()->create($checklistStepOptionData);
                    }
                }

                $processedItemIds[] = $checklistStep->id;
            }
        });

        $this->checklist->checklistSteps()
            ->whereNotIn('id', $processedItemIds)
            ->delete();
    }

    private function syncChecklistStepOptions(ChecklistStep $checklistStep, array $stepData): void
    {
        $existingOptions = $checklistStep->checklistStepOptions->keyBy('id');

        $processedItemIds = [];

        collect($stepData['checklist_step_options'])->each(function (array $optionData) use (&$processedItemIds, $existingOptions, $checklistStep) {
            if (isset($optionData['id']) && $existingOptions->has($optionData['id'])) {
                $checklistStepOption = $existingOptions->get($optionData['id']);
                $checklistStepOption->update($optionData);

                $processedItemIds[] = $optionData['id'];
            } else {
                /** @var \App\Models\ChecklistStepOption $checklistStepOption */
                $checklistStepOption = $checklistStep->checklistStepOptions()->create($optionData);

                $processedItemIds[] = $checklistStepOption->id;
            }
        });

        $checklistStep->checklistStepOptions()
            ->whereNotIn('id', $processedItemIds)
            ->delete();
    }

    private function syncChecklistReportTemplate(array $data): void
    {
        $latestTemplate = $this->checklist->checklistReportTemplates()->latest()->first();

        $newTemplateData = [
            'template' => $data['template'],
            'orientation' => $data['orientation'],
        ];

        if (
            !$latestTemplate ||
            $latestTemplate->template !== $newTemplateData['template'] ||
            $latestTemplate->orientation !== $newTemplateData['orientation']
        ) {
            $this->checklist->checklistReportTemplates()->create($newTemplateData);
        }
    }
}
