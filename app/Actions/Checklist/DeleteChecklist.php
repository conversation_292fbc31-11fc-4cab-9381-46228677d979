<?php

namespace App\Actions\Checklist;

use App\Models\Checklist;
use App\Models\ChecklistStep;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteChecklist
{
    use AsAction;

    public function handle(Checklist $checklist): void
    {
        try {
            DB::transaction(function () use ($checklist): void {
                $checklist->checklistSteps->each(fn(ChecklistStep $checklistStep) => $checklistStep->checklistStepOptions()->delete());
                $checklist->checklistSteps()->delete();
                $checklist->checklistReportTemplates()->delete();
                $checklist->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
