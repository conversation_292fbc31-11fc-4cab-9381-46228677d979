<?php

namespace App\Actions\Checklist\Queries;

use App\Models\Checklist;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetActiveChecklistsByName
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $search
     * @return \Illuminate\Support\Collection
     */
    public function handle(string $search): Collection
    {
        return Checklist::query()
            ->select(['id', 'name'])
            ->where('name', 'like', "%$search%")
            ->where('active', true)
            ->get();
    }
}
