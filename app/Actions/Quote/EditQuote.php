<?php

namespace App\Actions\Quote;

use App\Models\Product;
use App\Models\Quote;
use App\Models\ServiceType;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditQuote
{
    use AsAction;

    public function handle(Quote $quote, array $data): Quote
    {
        try {
            return DB::transaction(function () use ($quote, $data): Quote {
                $quoteItems = $data['quote_items'] ?? [];
                unset($data['quote_items']);

                $subtotal = collect($quoteItems)
                    ->filter(fn(array $quoteItem): bool => $quoteItem['approved'])
                    ->sum(fn(array $quoteItem): float => unmask_money($quoteItem['total_amount']));

                $discountPercentage = unmask_percentage($data['discount_percentage'] ?? '0');
                $discountAmount = $subtotal * ($discountPercentage / 100);

                $data['discount_subtotal'] = $discountAmount;
                $data['amount'] = $subtotal - $discountAmount;

                $quote->update($data);

                $this->syncQuoteItems($quote, $quoteItems);

                return $quote;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    private function syncQuoteItems(Quote $quote, array $quoteItems): void
    {
        $existingItems = $quote->quoteItems()->get()->keyBy('id');
        $submittedItemIds = collect($quoteItems)->pluck('id')->filter();

        foreach ($quoteItems as $itemData) {
            $itemData = $this->normalizeQuoteItemData($itemData);

            if (isset($itemData['id']) && $existingItems->has($itemData['id'])) {
                $existingItem = $existingItems->get($itemData['id']);

                $updateData = collect($itemData)
                    ->except('id')
                    ->filter(function ($value) {
                        return !is_null($value);
                    })
                    ->toArray();

                if (!empty($updateData)) {
                    $existingItem->update($updateData);
                }
            } else {
                unset($itemData['id']);
                $quote->quoteItems()->create($itemData);
            }
        }

        $itemsToDelete = $existingItems->keys()->diff($submittedItemIds);

        if ($itemsToDelete->isNotEmpty()) {
            $quote->quoteItems()->whereIn('id', $itemsToDelete)->delete();
        }
    }

    private function normalizeQuoteItemData(array $quoteItem): array
    {
        if (isset($quoteItem['type']) && isset($quoteItem['item_id'])) {
            if ($quoteItem['type'] === Product::class) {
                $quoteItem['product_id'] = $quoteItem['item_id'];

                if (!isset($quoteItem['id'])) {
                    $quoteItem['service_type_id'] = null;
                }
            } elseif ($quoteItem['type'] === ServiceType::class) {
                $quoteItem['service_type_id'] = $quoteItem['item_id'];

                if (!isset($quoteItem['id'])) {
                    $quoteItem['product_id'] = null;
                }
            }

            unset($quoteItem['type'], $quoteItem['item_id']);
        }

        return $quoteItem;
    }
}
