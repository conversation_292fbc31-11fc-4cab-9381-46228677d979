<?php

namespace App\Actions\Quote\Integrations\ErpFlex;

use App\Actions\Customer\Integrations\ErpFlex\CreateCustomerInErpFlex;
use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByCustomerId;
use App\Actions\ThirdPartyProduct\Queries\GetThirdPartyProductByProductId;
use App\Actions\ThirdPartyQuote\Queries\GetThirdPartyQuoteByQuoteId;
use App\Actions\ThirdPartyServiceType\Queries\GetThirdPartyServiceTypeByServiceTypeId;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Quote\ErpFlexQuoteDto;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductVariantService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexQuoteService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexSkuService;
use App\Models\ErpFlexParameter;
use App\Models\IntegrationType;
use App\Models\Quote;
use App\Models\QuoteItem;
use App\Models\ThirdPartyQuote;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class CreateQuoteInErpFlex
{
    use AsAction;

    private Quote $quote;
    private ErpFlexParameter $erpFlexParameter;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.quotes.send'));
    }

    public function handle(Quote $quote, ?int $userId = null, bool $throwWarnings = false): ?Quote
    {
        $this->quote = $quote;
        $this->erpFlexParameter = GetErpFlexParameter::run();

        if (!$this->erpFlexParameter) {
            throw new Exception('Não foi possível carregar os parâmetros de integração do ERPFlex.');
        }

        /** @var \App\Models\ThirdPartyQuote $thirdPartyQuote */
        $thirdPartyQuote = GetThirdPartyQuoteByQuoteId::run(IntegrationType::TYPE_ERP_FLEX, $this->quote->id);

        if ($thirdPartyQuote && $thirdPartyQuote->third_party_id) {
            if ($userId) {
                if ($throwWarnings) {
                    warning_notification('O orçamento já foi criado anteriormente no ERPFlex.')->send();
                } else {
                    warning_database_notification($userId, 'O orçamento já foi criado anteriormente no ERPFlex.', true);
                }
            }

            return null;
        }

        /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $this->quote->customer_id);

        if (!$thirdPartyCustomer->third_party_id) {
            CreateCustomerInErpFlex::run($this->quote->customer);

            /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
            $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $this->quote->customer_id);
        }

        $erpFlexQuoteDto = new ErpFlexQuoteDto(
            documento: $this->quote->id,
            cliente_id: $thirdPartyCustomer->third_party_id,
            emissao: carbon($this->quote->issued_at)->format('d/m/Y'),
            vendedor_id: $this->erpFlexParameter->quote_default_salesman_id,
            historico: 'Integração FieldFlex - Orçamento #' . $this->quote->id,
            itens: json_encode($this->buildItems()),
        );

        /** @var \App\Models\ThirdPartyQuote $thirdPartyQuote */
        $thirdPartyQuote = ThirdPartyQuote::create([
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'quote_id' => $this->quote->id,
        ]);

        $thirdPartyQuote->update([
            'third_party_id' => (new ErpFlexQuoteService())
                ->create($this->quote, $thirdPartyQuote, $erpFlexQuoteDto)
                ->data,
        ]);

        if ($userId) {
            success_database_notification($userId, __('quotes.responses.create_in_erp_flex.success'), true);
        }

        return $this->quote;
    }

    private function buildItems(): array
    {
        return array_values(
            $this->quote->quoteItems
                ->filter(fn(QuoteItem $quoteItem): bool => $quoteItem->approved)
                ->map(function (QuoteItem $quoteItem): array {
                    /** @var \App\Models\ThirdPartyProduct|\App\Models\ThirdPartyServiceType $thirdPartyItem */
                    $thirdPartyItem = $quoteItem->product_id
                        ? GetThirdPartyProductByProductId::run(IntegrationType::TYPE_ERP_FLEX, $quoteItem->product_id)
                        : GetThirdPartyServiceTypeByServiceTypeId::run(IntegrationType::TYPE_ERP_FLEX, $quoteItem->service_type_id);

                    $thirdPartyVariantId = $quoteItem->product_id
                        ? (new ErpFlexProductVariantService())->getByProductId($thirdPartyItem->third_party_id)->SB2_ID
                        : (new ErpFlexSkuService())->getByProductId($thirdPartyItem->third_party_id)->SB2_ID;

                    return array_filter([
                        'produto_id' => $thirdPartyItem->third_party_id,
                        'variante_id' => $thirdPartyVariantId,
                        'quantidade' => $quoteItem->quantity,
                        'preco_unitario' => $quoteItem->unit_amount,
                    ]);
                })
                ->toArray()
        );
    }
}
