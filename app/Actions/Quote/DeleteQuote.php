<?php

namespace App\Actions\Quote;

use App\Models\Quote;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteQuote
{
    use AsAction;

    public function handle(Quote $quote): void
    {
        try {
            DB::transaction(function () use ($quote): void {
                $quote->quoteItems()->delete();
                $quote->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
