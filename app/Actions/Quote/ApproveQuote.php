<?php

namespace App\Actions\Quote;

use App\Enums\QuoteStatusEnum;
use App\Models\Quote;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ApproveQuote
{
    use AsAction;

    public function handle(Quote $quote): Quote
    {
        if ($quote->status !== QuoteStatusEnum::Pending->value) {
            throw new Exception('O orçamento não pode ser aprovado pois não está pendente.');
        }

        try {
            $quote->update(['status' => QuoteStatusEnum::Approved->value]);
            return $quote;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
