<?php

namespace App\Actions\Quote;

use App\Enums\QuoteStatusEnum;
use App\Models\Quote;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class RejectQuote
{
    use AsAction;

    public function handle(Quote $quote): Quote
    {
        if ($quote->status !== QuoteStatusEnum::Pending->value) {
            throw new Exception('O orçamento não pode ser rejeitado pois não está pendente.');
        }

        try {
            $quote->update(['status' => QuoteStatusEnum::Rejected->value]);
            return $quote;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
