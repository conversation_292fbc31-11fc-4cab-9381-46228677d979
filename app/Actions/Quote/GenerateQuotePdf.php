<?php

namespace App\Actions\Quote;

use App\Enums\ChecklistStepDataTypeEnum;
use App\Models\Quote;
use App\Models\QuoteItem;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepOption;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateQuotePdf
{
    use AsAction;

    public function handle(string $token, bool $toOutput = false): mixed
    {
        $decodedToken = base64_decode($token);

        /** @var \App\Models\Quote $quote */
        $quote = Quote::find(explode(';', $decodedToken)[0]);

        if (!$quote) {
            throw new Exception('O orçamento não foi encontrado.');
        }

        $logoSrc = ouess_tenant()->getLogoSrc();

        $layoutData = [
            'logoSrc' => $logoSrc,
            'quote' => $quote,
            'quoteItems' => $this->buildQuoteItems($quote),
            'tenantId' => tenant('id'),
        ];

        if ($toOutput) {
            return Pdf::loadView('app.services.quotes.quote_layout', $layoutData, encoding: 'utf-8')
                ->setOption(['isRemoteEnabled' => true])
                ->output();
        }

        return Pdf::loadView('app.services.quotes.quote_layout', $layoutData, encoding: 'utf-8')
            ->setOption(['isRemoteEnabled' => true])
            ->stream();
    }

    private function buildQuoteItems(Quote $quote): array
    {
        $quoteItems = [];

        $quote->quoteItems
            ->filter(fn(QuoteItem $quoteItem): bool => $quoteItem->approved)
            ->groupBy('service_order_id')
            ->each(function (\Illuminate\Support\Collection $serviceOrderQuoteItems, int $serviceOrderId) use (&$quoteItems): void {
                /** @var \App\Models\QuoteItem $mainEquipmentWithServiceType */
                $mainEquipmentWithServiceType = $serviceOrderQuoteItems->first()->load(['equipment', 'serviceType']);

                $quoteItems[] = [
                    'service_order_code' => $mainEquipmentWithServiceType->serviceOrder->code,
                    'equipment_id' => $mainEquipmentWithServiceType->equipment_id,
                    'equipment_name' => $mainEquipmentWithServiceType->equipment->name,
                    'service_type_id' => $mainEquipmentWithServiceType->service_type_id,
                    'service_type_name' => $mainEquipmentWithServiceType->serviceType->name,
                    'type' => $mainEquipmentWithServiceType->equipment_id
                        ? 'Material'
                        : 'Serviço',
                    'additional_info' => $mainEquipmentWithServiceType->additional_info,
                    'quantity' => $mainEquipmentWithServiceType->quantity,
                    'unit_amount' => $mainEquipmentWithServiceType->unit_amount,
                    'total_amount' => $mainEquipmentWithServiceType->total_amount,
                    'execution_steps' => $mainEquipmentWithServiceType->serviceOrder->serviceOrderExecutionChecklistSteps
                        ->filter(fn(ServiceOrderExecutionChecklistStep $executionStep): bool => !is_null($executionStep->collected_value) && $executionStep->shows_checklist_in_quote && $executionStep->shows_in_quote)
                        ->map(fn(ServiceOrderExecutionChecklistStep $executionStep): array => [
                            'checklist_name' => $executionStep->checklist_name,
                            'name' => $executionStep->checklist_step_name,
                            'data_type' => $executionStep->data_type,
                            'collected_value' => match ($executionStep->data_type) {
                                ChecklistStepDataTypeEnum::Image->value => 'Em anexo',
                                ChecklistStepDataTypeEnum::Signature->value => '<img src="' . $executionStep->collected_value . '" alt="Imagem" width="100px" />',
                                ChecklistStepDataTypeEnum::SingleChoice->value => ServiceOrderExecutionChecklistStepOption::find($executionStep->collected_value)?->value,
                                ChecklistStepDataTypeEnum::MultipleChoice->value => collect(json_decode($executionStep->collected_value ?? "{}", true))->map(fn(int $optionId) => ServiceOrderExecutionChecklistStepOption::find($optionId)->value)->implode(', '),
                                default => $executionStep->collected_value,
                            },
                            'additional_info' => $executionStep->collected_value,
                        ])
                        ->groupBy('checklist_name')
                        ->toArray(),
                    'needs' => $serviceOrderQuoteItems
                        ->filter(fn(QuoteItem $quoteItem): bool => !is_null($quoteItem->service_order_execution_checklist_step_id))
                        ->map(fn(QuoteItem $quoteItem): array => [
                            'type' => $quoteItem->product_id
                                ? 'Material'
                                : 'Serviço',
                            'name' => $quoteItem->product_id
                                ? $quoteItem->product->name
                                : $quoteItem->serviceType->name,
                            'quantity' => $quoteItem->quantity,
                            'unit_amount' => $quoteItem->unit_amount,
                            'total_amount' => $quoteItem->total_amount,
                            'additional_info' => $quoteItem->additional_info,
                        ])
                        ->toArray(),
                ];
            });

        return $quoteItems;
    }
}
