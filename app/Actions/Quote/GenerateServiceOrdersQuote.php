<?php

namespace App\Actions\Quote;

use App\Enums\QuoteStatusEnum;
use App\Models\Product;
use App\Models\Quote;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepNeed;
use App\Models\ServiceType;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateServiceOrdersQuote
{
    use AsAction;

    private Quote $quote;

    public function handle(array $serviceOrderIds): Quote
    {
        /** @var \Illuminate\Support\Collection $serviceOrders */
        $serviceOrders = ServiceOrder::query()
            ->with([
                'serviceOrderExecutionChecklistSteps',
                'serviceOrderExecutionChecklistSteps.serviceOrderExecutionChecklistStepNeeds',
                'serviceType',
            ])
            ->whereIn('id', $serviceOrderIds)
            ->get();

        if ($serviceOrders->pluck('customer_id')->unique()->count() > 1) {
            throw new Exception('Não é possível gerar um orçamento para ordens de serviço de clientes diferentes.');
        }

        try {
            return DB::transaction(function () use ($serviceOrders): Quote {
                $this->quote = Quote::create([
                    'customer_id' => $serviceOrders->first()->customer_id,
                    'issued_at' => now()->setTimezone('-3:00'),
                    'status' => QuoteStatusEnum::Pending->value,
                ]);

                $this->createQuoteItems($serviceOrders);

                return $this->quote;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    private function createQuoteItems(Collection $serviceOrders): void
    {
        /** @var \App\Models\ServiceOrder $serviceOrder */
        foreach ($serviceOrders as $serviceOrder) {
            $this->quote->quoteItems()->create([
                'service_order_id' => $serviceOrder->id,
                'service_type_id' => $serviceOrder->service_type_id,
                'equipment_id' => $serviceOrder->equipment_id,
                'quantity' => 1,
                'unit_amount' => $serviceOrder->serviceType->default_amount,
                'total_amount' => $serviceOrder->serviceType->default_amount,
            ]);

            $serviceOrder->serviceOrderExecutionChecklistSteps->each(function (ServiceOrderExecutionChecklistStep $step): void {
                $step->serviceOrderExecutionChecklistStepNeeds->each(function (ServiceOrderExecutionChecklistStepNeed $need): void {
                    $this->quote->quoteItems()->create([
                        'service_order_id' => $need->serviceOrderExecutionChecklistStep->service_order_id,
                        'service_order_execution_checklist_step_id' => $need->serviceOrderExecutionChecklistStep->id,
                        'service_order_execution_checklist_step_need_id' => $need->id,
                        'service_type_id' => $need->need_type === ServiceType::class
                            ? $need->need_id
                            : null,
                        'product_id' => $need->need_type === Product::class
                            ? $need->need_id
                            : null,
                        'quantity' => $need->quantity,
                        'unit_amount' => $need->need->default_amount,
                        'total_amount' => $need->quantity * $need->need->default_amount,
                    ]);
                });
            });
        }
    }
}
