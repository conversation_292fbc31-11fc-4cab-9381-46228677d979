<?php

namespace App\Actions\ServiceOrderChecklist\Queries;

use App\Models\ServiceOrderChecklist;
use Lorisleiva\Actions\Concerns\AsAction;

class GetServiceOrderChecklistByServiceOrderIdAndChecklistId
{
    use AsAction;

    public function handle(int $serviceOrderId, int $checklistId): ?ServiceOrderChecklist
    {
        return ServiceOrderChecklist::query()
            ->where('service_order_id', $serviceOrderId)
            ->where('checklist_id', $checklistId)
            ->first();
    }
}
