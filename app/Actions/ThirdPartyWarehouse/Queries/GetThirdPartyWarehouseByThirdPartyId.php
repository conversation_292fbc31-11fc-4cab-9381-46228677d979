<?php

namespace App\Actions\ThirdPartyWarehouse\Queries;

use App\Models\ThirdPartyWarehouse;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyWarehouseByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyWarehouse
    {
        return ThirdPartyWarehouse::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
