<?php

namespace App\Actions\ThirdPartyWarehouse\Queries;

use App\Models\ThirdPartyWarehouse;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyWarehouseByWarehouseId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $warehouseId): ?ThirdPartyWarehouse
    {
        return ThirdPartyWarehouse::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('warehouse_id', $warehouseId)
            ->first();
    }
}
