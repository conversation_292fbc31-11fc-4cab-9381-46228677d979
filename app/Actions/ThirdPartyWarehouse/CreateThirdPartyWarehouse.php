<?php

namespace App\Actions\ThirdPartyWarehouse;

use App\Models\ThirdPartyWarehouse;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyWarehouse
{
    use AsAction;

    public function handle(array $data): ThirdPartyWarehouse
    {
        try {
            return ThirdPartyWarehouse::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
