<?php

namespace App\Actions\ThirdPartyWarehouse;

use App\Models\ThirdPartyWarehouse;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyWarehouse
{
    use AsAction;

    public function handle(ThirdPartyWarehouse $thirdPartyWarehouse, array $data): ThirdPartyWarehouse
    {
        try {
            $thirdPartyWarehouse->update($data);
            return $thirdPartyWarehouse;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
