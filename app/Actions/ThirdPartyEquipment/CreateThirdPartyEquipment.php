<?php

namespace App\Actions\ThirdPartyEquipment;

use App\Models\ThirdPartyEquipment;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyEquipment
{
    use AsAction;

    public function handle(array $data): ThirdPartyEquipment
    {
        try {
            return ThirdPartyEquipment::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
