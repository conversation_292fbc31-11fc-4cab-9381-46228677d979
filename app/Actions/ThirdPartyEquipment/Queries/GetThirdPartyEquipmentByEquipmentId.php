<?php

namespace App\Actions\ThirdPartyEquipment\Queries;

use App\Models\ThirdPartyEquipment;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyEquipmentByEquipmentId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $equipmentId): ?ThirdPartyEquipment
    {
        return ThirdPartyEquipment::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('equipment_id', $equipmentId)
            ->first();
    }
}
