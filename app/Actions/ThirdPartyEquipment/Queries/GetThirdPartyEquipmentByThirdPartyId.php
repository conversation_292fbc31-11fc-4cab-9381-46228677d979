<?php

namespace App\Actions\ThirdPartyEquipment\Queries;

use App\Models\ThirdPartyEquipment;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyEquipmentByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyEquipment
    {
        return ThirdPartyEquipment::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
