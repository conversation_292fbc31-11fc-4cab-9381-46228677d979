<?php

namespace App\Actions\ThirdPartyEquipment;

use App\Models\ThirdPartyEquipment;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyEquipment
{
    use AsAction;

    public function handle(ThirdPartyEquipment $thirdPartyEquipment, array $data): ThirdPartyEquipment
    {
        try {
            $thirdPartyEquipment->update($data);
            return $thirdPartyEquipment;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
