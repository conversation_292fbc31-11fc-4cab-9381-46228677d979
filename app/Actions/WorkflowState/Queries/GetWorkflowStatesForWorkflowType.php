<?php

namespace App\Actions\WorkflowState\Queries;

use App\Models\WorkflowState;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetWorkflowStatesForWorkflowType
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $workflowType
     * @return \Illuminate\Support\Collection
     */
    public function handle(string $workflowType): Collection
    {
        return WorkflowState::query()
            ->whereRelation('workflow', 'type', $workflowType)
            ->orderBy('sequence')
            ->get();
    }
}
