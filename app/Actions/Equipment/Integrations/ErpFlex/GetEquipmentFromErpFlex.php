<?php

namespace App\Actions\Equipment\Integrations\ErpFlex;

use App\Actions\Equipment\CreateEquipment;
use App\Actions\Equipment\EditEquipment;
use App\Actions\EquipmentType\Integrations\ErpFlex\GetEquipmentTypesFromErpFlex;
use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\ThirdPartyEquipment\CreateThirdPartyEquipment;
use App\Actions\ThirdPartyEquipment\EditThirdPartyEquipment;
use App\Actions\ThirdPartyEquipment\Queries\GetThirdPartyEquipmentByThirdPartyId;
use App\Actions\ThirdPartyEquipmentType\Queries\GetThirdPartyEquipmentTypeByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexEquipmentService;
use App\Models\ErpFlexParameter;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\Equipment;
use App\Models\ThirdPartyEquipment;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetEquipmentFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexEquipmentService $erpFlexEquipmentService;
    private ErpFlexParameter $erpFlexParameter;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.equipment.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
        ?int $erpFlexEquipmentId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexEquipmentService = new ErpFlexEquipmentService();
        $this->erpFlexParameter = GetErpFlexParameter::run();

        if ($erpFlexEquipmentId) {
            GetEquipmentTypesFromErpFlex::run($this->integrationSetting, true, $userId);

            $this->processSingleEquipment(
                $this->erpFlexEquipmentService->getById($erpFlexEquipmentId)
            );

            return;
        }

        $this->integrateEquipment();

        if ($userId) {
            success_database_notification($userId, __('equipment.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateEquipment(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_equipment_received_at'])
                ? carbon($this->integrationSetting->settings['last_equipment_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiEquipment = $this->erpFlexEquipmentService->get(1000, $i, $lastIntegrationAt, null, $this->erpFlexParameter->equipment_serial_number_field);

            foreach ($erpFlexApiEquipment as $erpFlexSingleEquipment) {
                try {
                    $this->processSingleEquipment($erpFlexSingleEquipment);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiEquipment) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_equipment_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleEquipment(object $erpFlexSingleEquipment): void
    {
        $this->createOrUpdateEquipment(
            $this->createOrUpdateThirdPartyEquipment($erpFlexSingleEquipment),
            $erpFlexSingleEquipment
        );
    }

    private function createOrUpdateThirdPartyEquipment(object $erpFlexSingleEquipment): ThirdPartyEquipment
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexSingleEquipment->SB1_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexSingleEquipment), true),
        ];

        /** @var \App\Models\ThirdPartyEquipment|null $thirdPartyEquipment */
        $thirdPartyEquipment = GetThirdPartyEquipmentByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexSingleEquipment->SB1_ID);

        return is_null($thirdPartyEquipment)
            ? CreateThirdPartyEquipment::run($data)
            : EditThirdPartyEquipment::run($thirdPartyEquipment, $data);
    }

    private function createOrUpdateEquipment(ThirdPartyEquipment $thirdPartyEquipment, object $erpFlexSingleEquipment): Equipment
    {
        $data = [
            'equipment_type_id' => GetThirdPartyEquipmentTypeByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexSingleEquipment->SB1_IDSBA)->equipment_type_id,
            'code' => $erpFlexSingleEquipment->SB1_Codigo,
            'name' => $erpFlexSingleEquipment->SB1_Desc,
            'serial_number' => !is_null($this->erpFlexParameter->equipment_serial_number_field)
                ? $erpFlexSingleEquipment->{$this->erpFlexParameter->equipment_serial_number_field}
                : null,
            'additional_info' => $erpFlexSingleEquipment->SB1_Caracteristicas,
        ];

        if (is_null($thirdPartyEquipment->equipment_id)) {
            /** @var \App\Models\Equipment $equipment */
            $equipment = CreateEquipment::run($data);

            EditThirdPartyEquipment::run($thirdPartyEquipment, ['equipment_id' => $equipment->id]);

            return $equipment;
        }

        return EditEquipment::run($thirdPartyEquipment->equipment, $data);
    }
}
