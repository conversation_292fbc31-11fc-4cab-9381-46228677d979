<?php

namespace App\Actions\Equipment\Integrations;

use App\Actions\IntegrationSetting\Queries\GetActiveIntegrationSettings;
use App\Actions\Equipment\Integrations\ErpFlex\GetEquipmentFromErpFlex;
use App\Models\IntegrationType;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyEquipment
{
    use AsAction;

    public string $commandSignature = 'ouess:get-third-party-equipment {tenant?}';
    public string $commandDescription = 'Get all equipment from the integrations.';

    public function asCommand(Command $command): void
    {
        $tenants = Tenant::query()
            ->when($command->argument('tenant'), function (Builder $query) use ($command): Builder {
                return $query->where('id', $command->argument('tenant'));
            })
            ->get();

        tenancy()->runForMultiple(
            $tenants,
            fn() => $this->handle()
        );
    }

    public function handle(bool $force = false): void
    {
        $integrationSettings = GetActiveIntegrationSettings::run();

        foreach ($integrationSettings as $integrationSetting) {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            switch ($integrationSetting->integration_type_id) {
                case IntegrationType::TYPE_ERP_FLEX:
                    GetEquipmentFromErpFlex::dispatch($integrationSetting, $force, auth()->check() ? auth()->id() : null, null);
                    break;
                default:
                    break;
            }
        }
    }
}
