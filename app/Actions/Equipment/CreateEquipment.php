<?php

namespace App\Actions\Equipment;

use App\Models\Equipment;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateEquipment
{
    use AsAction;

    public function handle(array $data): Equipment
    {
        try {
            return DB::transaction(function () use ($data): Equipment {
                /** @var \App\Models\Equipment $equipment */
                $equipment = Equipment::create($data);

                if (isset($data['customer_id'])) {
                    $equipment->equipmentCustomers()->create(['customer_id' => $data['customer_id']]);
                }

                return $equipment;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
