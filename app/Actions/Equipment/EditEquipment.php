<?php

namespace App\Actions\Equipment;

use App\Models\Equipment;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditEquipment
{
    use AsAction;

    public function handle(Equipment $equipment, array $data): Equipment
    {
        try {
            $equipment->update($data);
            return $equipment;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
