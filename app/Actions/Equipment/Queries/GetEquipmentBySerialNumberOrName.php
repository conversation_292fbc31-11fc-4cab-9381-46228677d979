<?php

namespace App\Actions\Equipment\Queries;

use App\Models\Equipment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetEquipmentBySerialNumberOrName
{
    use AsAction;

    public function handle(string $search, bool $orderByName = false): Collection
    {
        return Equipment::query()
            ->where(function (Builder $query) use ($search): Builder {
                return $query
                    ->where('name', 'like', "%$search%")
                    ->orWhere('serial_number', 'like', "%$search%");
            })
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
