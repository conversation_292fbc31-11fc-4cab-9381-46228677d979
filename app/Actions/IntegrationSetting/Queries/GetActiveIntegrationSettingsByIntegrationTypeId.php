<?php

namespace App\Actions\IntegrationSetting\Queries;

use App\Models\IntegrationSetting;
use Lorisleiva\Actions\Concerns\AsAction;

class GetActiveIntegrationSettingsByIntegrationTypeId
{
    use AsAction;

    public function handle(int $integrationTypeId): ?IntegrationSetting
    {
        return IntegrationSetting::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('active', true)
            ->first();
    }
}
