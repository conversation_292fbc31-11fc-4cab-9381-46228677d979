<?php

namespace App\Actions\ThirdPartyServiceOrder\Queries;

use App\Models\ThirdPartyServiceOrder;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyServiceOrderByServiceOrderId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $serviceOrderId): ?ThirdPartyServiceOrder
    {
        return ThirdPartyServiceOrder::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('service_order_id', $serviceOrderId)
            ->first();
    }
}
