<?php

namespace App\Actions\ThirdPartyMeasuringUnit;

use App\Models\ThirdPartyMeasuringUnit;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyMeasuringUnit
{
    use AsAction;

    public function handle(array $data): ThirdPartyMeasuringUnit
    {
        try {
            return ThirdPartyMeasuringUnit::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
