<?php

namespace App\Actions\ThirdPartyMeasuringUnit\Queries;

use App\Models\ThirdPartyMeasuringUnit;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyMeasuringUnitByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyMeasuringUnit
    {
        return ThirdPartyMeasuringUnit::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
