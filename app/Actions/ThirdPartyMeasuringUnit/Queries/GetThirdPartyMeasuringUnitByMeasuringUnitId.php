<?php

namespace App\Actions\ThirdPartyMeasuringUnit\Queries;

use App\Models\ThirdPartyMeasuringUnit;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyMeasuringUnitByMeasuringUnitId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $measuringUnitId): ?ThirdPartyMeasuringUnit
    {
        return ThirdPartyMeasuringUnit::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('measuring_unit_id', $measuringUnitId)
            ->first();
    }
}
