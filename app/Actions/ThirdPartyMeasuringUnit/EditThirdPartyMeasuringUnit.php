<?php

namespace App\Actions\ThirdPartyMeasuringUnit;

use App\Models\ThirdPartyMeasuringUnit;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyMeasuringUnit
{
    use AsAction;

    public function handle(ThirdPartyMeasuringUnit $thirdPartyMeasuringUnit, array $data): ThirdPartyMeasuringUnit
    {
        try {
            $thirdPartyMeasuringUnit->update($data);
            return $thirdPartyMeasuringUnit;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
