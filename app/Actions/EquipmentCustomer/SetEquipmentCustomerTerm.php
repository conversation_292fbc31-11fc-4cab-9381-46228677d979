<?php

namespace App\Actions\EquipmentCustomer;

use App\Models\EquipmentCustomer;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class SetEquipmentCustomerTerm
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\EquipmentCustomer $equipmentCustomer
     * @param  \Carbon\Carbon $termStartedAt
     * @param  \Carbon\Carbon $termEndedAt
     * @return \App\Models\EquipmentCustomer
     */
    public function handle(EquipmentCustomer $equipmentCustomer, Carbon $termStartedAt, Carbon $termEndedAt): EquipmentCustomer
    {
        try {
            $equipmentCustomer->update([
                'term_started_at' => $termStartedAt,
                'term_ended_at' => $termEndedAt,
            ]);

            return $equipmentCustomer;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
