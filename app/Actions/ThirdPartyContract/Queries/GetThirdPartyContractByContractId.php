<?php

namespace App\Actions\ThirdPartyContract\Queries;

use App\Models\ThirdPartyContract;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyContractByContractId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $contractId): ?ThirdPartyContract
    {
        return ThirdPartyContract::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('contract_id', $contractId)
            ->first();
    }
}
