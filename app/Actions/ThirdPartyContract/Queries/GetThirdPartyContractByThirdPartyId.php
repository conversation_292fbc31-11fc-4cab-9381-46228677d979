<?php

namespace App\Actions\ThirdPartyContract\Queries;

use App\Models\ThirdPartyContract;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyContractByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyContract
    {
        return ThirdPartyContract::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
