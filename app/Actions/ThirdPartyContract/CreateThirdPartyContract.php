<?php

namespace App\Actions\ThirdPartyContract;

use App\Models\ThirdPartyContract;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyContract
{
    use AsAction;

    public function handle(array $data): ThirdPartyContract
    {
        try {
            return ThirdPartyContract::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
