<?php

namespace App\Actions\ThirdPartyContract;

use App\Models\ThirdPartyContract;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyContract
{
    use AsAction;

    public function handle(ThirdPartyContract $thirdPartyContract, array $data): ThirdPartyContract
    {
        try {
            $thirdPartyContract->update($data);
            return $thirdPartyContract;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
