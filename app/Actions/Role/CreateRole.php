<?php

namespace App\Actions\Role;

use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\Permission\Models\Role;
use Throwable;

class CreateRole
{
    use AsAction;

    public function handle(array $data): Role
    {
        try {
            return DB::transaction(function () use ($data): Role {
                /** @var \Spatie\Permission\Models\Role $role */
                $role = Role::create($data);
                $role->givePermissionTo(array_keys(array_filter(DefinePermissions::run($data))));

                return $role;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
