<?php

namespace App\Actions\Role;

use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\Permission\Models\Role;
use Throwable;

class UpdateRole
{
    use AsAction;

    public function handle(Role $role, array $data): Role
    {
        try {
            return DB::transaction(function () use ($role, $data): Role {
                $role->update($data);
                $role->syncPermissions(array_keys(array_filter(DefinePermissions::run($data))));

                return $role;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
