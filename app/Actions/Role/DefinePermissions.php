<?php

namespace App\Actions\Role;

use App\Models\Permission;
use Lorisleiva\Actions\Concerns\AsAction;

class DefinePermissions
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return array
     */
    public function handle(array $data): array
    {
        $permissions = [];
        $reflectionPermissions = Permission::getAvailablePermissions();

        foreach ($reflectionPermissions as $permission) {
            $permissions[$permission] = $data[$permission];
        }

        return $permissions;
    }
}
