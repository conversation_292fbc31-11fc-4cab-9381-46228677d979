<?php

namespace App\Actions\Role;

use App\Models\Permission;
use Lorisleiva\Actions\Concerns\AsAction;
use ReflectionClass;
use Spatie\Permission\Models\Role;

class CreateBasicRoles
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        Role::create(['name' => 'Administrador']);

        $databasePermissions = Permission::query()
            ->pluck('name')
            ->toArray();

        $permissionReflectionClass = new ReflectionClass(Permission::class);

        $reflectionPermissions = array_values($permissionReflectionClass->getConstants());

        $permissionsToBeAdded = array_filter($reflectionPermissions, function ($item) use ($databasePermissions) {
            return !in_array(
                $item,
                array_merge($databasePermissions, ['created_at', 'updated_at'])
            );
        });

        foreach ($permissionsToBeAdded as $permission) {
            Permission::create(['name' => $permission]);
        }

        Role::query()
            ->where('name', 'Administrador')
            ->first()
            ->givePermissionTo($permissionsToBeAdded);

        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
}
