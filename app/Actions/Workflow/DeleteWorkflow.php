<?php

namespace App\Actions\Workflow;

use App\Models\Workflow;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteWorkflow
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Workflow $workflow
     * @return void
     */
    public function handle(Workflow $workflow): void
    {
        try {
            DB::transaction(function () use ($workflow): void {
                $workflow->workflowStates()->delete();
                $workflow->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
