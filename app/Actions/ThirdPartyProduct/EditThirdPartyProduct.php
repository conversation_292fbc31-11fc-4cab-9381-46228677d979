<?php

namespace App\Actions\ThirdPartyProduct;

use App\Models\ThirdPartyProduct;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyProduct
{
    use AsAction;

    public function handle(ThirdPartyProduct $thirdPartyProduct, array $data): ThirdPartyProduct
    {
        try {
            $thirdPartyProduct->update($data);
            return $thirdPartyProduct;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
