<?php

namespace App\Actions\ThirdPartyProduct;

use App\Models\ThirdPartyProduct;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyProduct
{
    use AsAction;

    public function handle(array $data): ThirdPartyProduct
    {
        try {
            return ThirdPartyProduct::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
