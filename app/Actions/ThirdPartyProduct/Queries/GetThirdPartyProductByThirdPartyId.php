<?php

namespace App\Actions\ThirdPartyProduct\Queries;

use App\Models\ThirdPartyProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyProductByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyProduct
    {
        return ThirdPartyProduct::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
