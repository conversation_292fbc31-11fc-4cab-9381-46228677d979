<?php

namespace App\Actions\ThirdPartyProduct\Queries;

use App\Models\ThirdPartyProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyProductByProductId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $productId): ?ThirdPartyProduct
    {
        return ThirdPartyProduct::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('product_id', $productId)
            ->first();
    }
}
