<?php

namespace App\Actions\TicketType;

use App\Models\TicketType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTicketTypes
{
    use AsAction;

    public function handle(bool $orderByName = false): Collection
    {
        return TicketType::query()
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
