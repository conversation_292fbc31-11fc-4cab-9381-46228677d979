<?php

namespace App\Actions\ThirdPartyWarehouseProduct\Queries;

use App\Models\ThirdPartyWarehouseProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyWarehouseProductByWarehouseProductId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $warehouseProductId): ?ThirdPartyWarehouseProduct
    {
        return ThirdPartyWarehouseProduct::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('warehouse_product_id', $warehouseProductId)
            ->first();
    }
}
