<?php

namespace App\Actions\ThirdPartyWarehouseProduct\Queries;

use App\Models\ThirdPartyWarehouseProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyWarehouseProductByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyWarehouseProduct
    {
        return ThirdPartyWarehouseProduct::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
