<?php

namespace App\Actions\ThirdPartyWarehouseProduct;

use App\Models\ThirdPartyWarehouseProduct;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyWarehouseProduct
{
    use AsAction;

    public function handle(array $data): ThirdPartyWarehouseProduct
    {
        try {
            return ThirdPartyWarehouseProduct::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
