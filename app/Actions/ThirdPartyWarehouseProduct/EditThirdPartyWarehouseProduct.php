<?php

namespace App\Actions\ThirdPartyWarehouseProduct;

use App\Models\ThirdPartyWarehouseProduct;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyWarehouseProduct
{
    use AsAction;

    public function handle(ThirdPartyWarehouseProduct $thirdPartyWarehouseProduct, array $data): ThirdPartyWarehouseProduct
    {
        try {
            $thirdPartyWarehouseProduct->update($data);
            return $thirdPartyWarehouseProduct;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
