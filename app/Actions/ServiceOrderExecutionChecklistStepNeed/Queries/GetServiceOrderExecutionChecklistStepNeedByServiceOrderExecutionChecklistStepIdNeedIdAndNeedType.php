<?php

namespace App\Actions\ServiceOrderExecutionChecklistStepNeed\Queries;

use App\Models\ServiceOrderExecutionChecklistStepNeed;
use Lorisleiva\Actions\Concerns\AsAction;

class GetServiceOrderExecutionChecklistStepNeedByServiceOrderExecutionChecklistStepIdNeedIdAndNeedType
{
    use AsAction;

    public function handle(int $serviceOrderExecutionChecklistStepId, int $needId, string $needType): ?ServiceOrderExecutionChecklistStepNeed
    {
        return ServiceOrderExecutionChecklistStepNeed::query()
            ->where('service_order_execution_checklist_step_id', $serviceOrderExecutionChecklistStepId)
            ->where('need_id', $needId)
            ->where('need_type', $needType)
            ->first();
    }
}
