<?php

namespace App\Actions\ProductType;

use App\Models\ProductType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditProductType
{
    use AsAction;

    public function handle(ProductType $productType, array $data): ProductType
    {
        try {
            $productType->update($data);
            return $productType;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
