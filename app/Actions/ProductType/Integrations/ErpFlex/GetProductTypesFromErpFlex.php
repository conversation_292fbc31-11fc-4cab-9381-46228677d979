<?php

namespace App\Actions\ProductType\Integrations\ErpFlex;

use App\Actions\ProductType\CreateProductType;
use App\Actions\ProductType\EditProductType;
use App\Actions\ThirdPartyProductType\CreateThirdPartyProductType;
use App\Actions\ThirdPartyProductType\EditThirdPartyProductType;
use App\Actions\ThirdPartyProductType\Queries\GetThirdPartyProductTypeByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductCategoryService;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\ProductType;
use App\Models\ThirdPartyProductType;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetProductTypesFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexProductCategoryService $erpFlexProductCategoryService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.products.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexProductCategoryService = new ErpFlexProductCategoryService();

        $this->integrateProductTypes();

        if ($userId) {
            success_database_notification($userId, __('product_types.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateProductTypes(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_product_types_received_at'])
                ? carbon($this->integrationSetting->settings['last_product_types_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiProductTypes = $this->erpFlexProductCategoryService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiProductTypes as $erpFlexProductType) {
                try {
                    $this->processSingleProductType($erpFlexProductType);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiProductTypes) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_product_types_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleProductType(object $erpFlexProductType): void
    {
        $this->createOrUpdateProductType(
            $this->createOrUpdateThirdPartyProductType($erpFlexProductType),
            $erpFlexProductType
        );
    }

    private function createOrUpdateThirdPartyProductType(object $erpFlexProductType): ThirdPartyProductType
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexProductType->SBA_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexProductType), true),
        ];

        /** @var \App\Models\ThirdPartyProductType|null $thirdPartyProductType */
        $thirdPartyProductType = GetThirdPartyProductTypeByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexProductType->SBA_ID);

        return is_null($thirdPartyProductType)
            ? CreateThirdPartyProductType::run($data)
            : EditThirdPartyProductType::run($thirdPartyProductType, $data);
    }

    private function createOrUpdateProductType(ThirdPartyProductType $thirdPartyProductType, object $erpFlexProductType): ProductType
    {
        $data = [
            'name' => $erpFlexProductType->SBA_Desc,
        ];

        if (is_null($thirdPartyProductType->product_type_id)) {
            /** @var \App\Models\ProductType $productType */
            $productType = CreateProductType::run($data);

            EditThirdPartyProductType::run($thirdPartyProductType, ['product_type_id' => $productType->id]);

            return $productType;
        }

        return EditProductType::run($thirdPartyProductType->productType, $data);
    }
}
