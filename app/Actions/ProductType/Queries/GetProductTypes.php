<?php

namespace App\Actions\ProductType\Queries;

use App\Models\ProductType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProductTypes
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  bool $orderByName
     * @return \Illuminate\Support\Collection
     */
    public function handle(bool $orderByName = false): Collection
    {
        return ProductType::query()
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
