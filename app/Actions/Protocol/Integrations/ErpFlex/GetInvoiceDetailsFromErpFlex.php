<?php

namespace App\Actions\Protocol\Integrations\ErpFlex;

use App\Actions\Customer\Integrations\ErpFlex\GetCustomersFromErpFlex;
use App\Actions\IntegrationSetting\Queries\GetActiveIntegrationSettingsByIntegrationTypeId;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexExpenseService;
use App\Models\IntegrationType;
use App\Models\ThirdPartyCustomer;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoiceDetailsFromErpFlex
{
    use AsAction;

    private ?ThirdPartyCustomer $thirdPartyCustomer = null;
    private ErpFlexExpenseService $erpFlexExpenseService;

    public function handle(string $documentNumber): array
    {
        $this->erpFlexExpenseService = new ErpFlexExpenseService();

        $apiInvoiceItems = $this->erpFlexExpenseService->getByDocumentOrInvoiceNumber($documentNumber);

        $thirdPartyCustomer = null;

        return array_map(function (object $apiInvoiceItem) use ($thirdPartyCustomer): array {
            if (!is_null($apiInvoiceItem->SF3_NrNFSe) && trim($apiInvoiceItem->SF3_NrNFSe) !== '') {
                $invoiceNumber = trim($apiInvoiceItem->SF3_NrNFSe);
            } elseif (!is_null($apiInvoiceItem->SF1_NrNFe) && trim($apiInvoiceItem->SF1_NrNFe) !== '') {
                $invoiceNumber = trim($apiInvoiceItem->SF1_NrNFe);
            } else {
                $invoiceNumber = trim($apiInvoiceItem->SF1_Doc);
            }

            $thirdPartyCustomer = $this->resolveThirdPartyCustomer($apiInvoiceItem);

            return [
                'invoice_number' => $invoiceNumber,
                'customer_id' => $thirdPartyCustomer->customer_id,
                'protocol_equipment' => $apiInvoiceItem,
            ];
        }, $apiInvoiceItems);
    }

    private function resolveThirdPartyCustomer(object $apiInvoiceItem): ThirdPartyCustomer
    {
        if ($this->thirdPartyCustomer && $this->thirdPartyCustomer->customer_id) {
            return $this->thirdPartyCustomer;
        }

        $this->thirdPartyCustomer = GetThirdPartyCustomerByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $apiInvoiceItem->SF1_IDSA1);

        if ($this->thirdPartyCustomer && $this->thirdPartyCustomer->customer_id) {
            return $this->thirdPartyCustomer;
        }

        GetCustomersFromErpFlex::run(
            GetActiveIntegrationSettingsByIntegrationTypeId::run(IntegrationType::TYPE_ERP_FLEX),
            false,
            null,
            $apiInvoiceItem->SF1_IDSA1
        );

        $this->thirdPartyCustomer = GetThirdPartyCustomerByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $apiInvoiceItem->SF1_IDSA1);

        return $this->thirdPartyCustomer;
    }
}
