<?php

namespace App\Actions\Protocol;

use App\Actions\ServiceOrder\CreateServiceOrder;
use App\Actions\ServiceType\Queries\GetServiceTypeById;
use App\Models\Protocol;
use App\Models\ServiceType;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateProtocolServiceOrders
{
    use AsAction;

    private ServiceType $serviceType;

    public function handle(Protocol $protocol, int $serviceTypeId, array $checklistIds): Protocol
    {
        $this->serviceType = GetServiceTypeById::run($serviceTypeId);

        try {
            return DB::transaction(function () use ($protocol, $checklistIds): Protocol {
                for ($i = 1; $i <= $protocol->protocolEquipment->count(); $i++) {
                    $protocol->protocolServiceOrders()->create([
                        'service_order_id' => CreateServiceOrder::run([
                            'code' => $protocol->code . '-' . $i,
                            'protocol_id' => $protocol->id,
                            'customer_id' => $protocol->customer_id,
                            'equipment_id' => $protocol->protocolEquipment[$i - 1]->equipment_id,
                            'service_type_id' => $this->serviceType->id,
                            'estimated_duration_in_minutes' => $this->serviceType->estimated_duration_in_minutes,
                            'checklist_ids' => $checklistIds,
                        ])->id
                    ]);
                }

                return $protocol;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
