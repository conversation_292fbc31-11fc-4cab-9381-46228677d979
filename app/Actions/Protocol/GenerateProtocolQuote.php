<?php

namespace App\Actions\Protocol;

use App\Actions\Quote\CreateQuote;
use App\Models\Protocol;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateProtocolQuote
{
    use AsAction;

    public function handle(Protocol $protocol): Protocol
    {
        try {
            return DB::transaction(function () use ($protocol): Protocol {
                CreateQuote::run([
                    'customer_id' => $protocol->customer_id,
                    'protocol_id' => $protocol->id,
                    'issued_at' => now()->setTimezone('-3:00'),
                    'amount' => 0,
                    'quote_items' => [],
                ]);

                return $protocol;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
