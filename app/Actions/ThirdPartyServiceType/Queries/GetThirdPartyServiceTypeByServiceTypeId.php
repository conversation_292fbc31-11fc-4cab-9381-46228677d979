<?php

namespace App\Actions\ThirdPartyServiceType\Queries;

use App\Models\ThirdPartyServiceType;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyServiceTypeByServiceTypeId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $serviceTypeId): ?ThirdPartyServiceType
    {
        return ThirdPartyServiceType::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('service_type_id', $serviceTypeId)
            ->first();
    }
}
