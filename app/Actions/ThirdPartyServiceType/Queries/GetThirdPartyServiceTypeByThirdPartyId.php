<?php

namespace App\Actions\ThirdPartyServiceType\Queries;

use App\Models\ThirdPartyServiceType;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyServiceTypeByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyServiceType
    {
        return ThirdPartyServiceType::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
