<?php

namespace App\Actions\ThirdPartyServiceType;

use App\Models\ThirdPartyServiceType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyServiceType
{
    use AsAction;

    public function handle(array $data): ThirdPartyServiceType
    {
        try {
            return ThirdPartyServiceType::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
