<?php

namespace App\Actions\ThirdPartyServiceType;

use App\Models\ThirdPartyServiceType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyServiceType
{
    use AsAction;

    public function handle(ThirdPartyServiceType $thirdPartyServiceType, array $data): ThirdPartyServiceType
    {
        try {
            $thirdPartyServiceType->update($data);
            return $thirdPartyServiceType;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
