<?php

namespace App\Actions\Ticket;

use App\Actions\ServiceOrder\CreateServiceOrder;
use App\Models\Ticket;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateTicketServiceOrder
{
    use AsAction;

    public function handle(Ticket $ticket): Ticket
    {
        try {
            return DB::transaction(function () use ($ticket): Ticket {
                CreateServiceOrder::run([
                    'code' => $ticket->id . '-1',
                    'ticket_id' => $ticket->id,
                    'customer_id' => $ticket->customer_id,
                    'equipment_id' => $ticket->equipment_id,
                    'service_type_id' => $ticket->ticketType->service_type_id,
                    'estimated_duration_in_minutes' => $ticket->ticketType->serviceType->estimated_duration_in_minutes,
                    'checklist_ids' => [],
                ]);

                return $ticket;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
