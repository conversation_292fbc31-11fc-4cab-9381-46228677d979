<?php

namespace App\Actions\WarehouseProduct\Queries;

use App\Models\WarehouseProduct;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetWarehouseProductByWarehouseIdAndProductId
{
    use AsAction;

    public function handle(int $warehouseId, int $productId): Collection
    {
        return WarehouseProduct::query()
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->get();
    }
}
