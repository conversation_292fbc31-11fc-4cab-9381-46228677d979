<?php

namespace App\Actions\Tenant;

use App\Actions\Region\PopulateRegions;
use App\Actions\Role\CreateBasicRoles;
use App\Integrations\IntegrationService;
use App\Models\ErpFlexParameter;
use App\Models\Tenant;
use App\Models\TenantSettings;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateBasicTenantEnvironment
{
    use AsAction;

    public function handle(array $data): Tenant
    {
        try {
            /** @var \App\Models\Tenant $tenant */
            $tenant = Tenant::create(['id' => $data['id']]);

            $tenant->update([
                'erp_flex' => [
                    'api' => [
                        'username' => ouess_aes256cbc_encrypt($data['erp_flex_api_username']),
                        'password' => ouess_aes256cbc_encrypt($data['erp_flex_api_password']),
                        'consolidator' => [
                            'username' => ouess_aes256cbc_encrypt($data['erp_flex_consolidator_api_username']),
                            'password' => ouess_aes256cbc_encrypt($data['erp_flex_consolidator_api_password']),
                        ],
                    ],
                    'customer_id' => (int)$data['erp_flex_tenant_id'],
                    'consolidator_customer_id' => (int)$data['erp_flex_consolidator_tenant_id'],
                    'environment' => $data['erp_flex_environment'],
                ],
                'logo_url' => $data['logo_url'],
                'max_administrators_count' => $data['max_administrators_count'],
                'max_technicians_count' => $data['max_technicians_count'],
            ]);

            $tenant->domains()->create(['domain' => $data['id'] . '.ouess.com.br']);

            $tenant->run(function () use ($data) {
                $user = User::create([
                    'name' => 'Admin',
                    'email' => $data['main_user_email'],
                    'password' => $data['main_user_password'],
                    'active' => true,
                ]);

                CreateBasicRoles::run();

                $user->syncRoles('Administrador');

                IntegrationService::integration_settings();
                TenantSettings::create();
                ErpFlexParameter::create();

                $storage_path = storage_path();
                mkdir("$storage_path/framework/cache", 0777, true);
                mkdir("$storage_path/app/livewire-tmp", 0777, true);
            });

            return $tenant;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
