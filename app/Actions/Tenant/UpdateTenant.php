<?php

namespace App\Actions\Tenant;

use App\Models\Tenant;
use Lori<PERSON><PERSON>\Actions\Concerns\AsAction;

class UpdateTenant
{
    use AsAction;

    public function handle(Tenant $tenant, array $data): Tenant
    {
        $tenant->update([
            'erp_flex' => [
                'api' => [
                    'username' => ouess_aes256cbc_encrypt($data['erp_flex_api_username']),
                    'password' => ouess_aes256cbc_encrypt($data['erp_flex_api_password']),
                    'consolidator' => [
                        'username' => ouess_aes256cbc_encrypt($data['erp_flex_consolidator_api_username']),
                        'password' => ouess_aes256cbc_encrypt($data['erp_flex_consolidator_api_password']),
                    ],
                ],
                'customer_id' => (int)$data['erp_flex_tenant_id'],
                'consolidator_customer_id' => (int)$data['erp_flex_consolidator_tenant_id'],
                'environment' => $data['erp_flex_environment'],
            ],
            'logo_url' => $data['logo_url'],
            'max_administrators_count' => $data['max_administrators_count'],
            'max_technicians_count' => $data['max_technicians_count'],
        ]);

        return $tenant;
    }
}
