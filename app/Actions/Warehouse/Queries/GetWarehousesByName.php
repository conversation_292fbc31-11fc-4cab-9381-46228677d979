<?php

namespace App\Actions\Warehouse\Queries;

use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetWarehousesByName
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  bool $orderByName
     * @return \Illuminate\Support\Collection
     */
    public function handle(bool $orderByName = false): Collection
    {
        return Warehouse::query()
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
