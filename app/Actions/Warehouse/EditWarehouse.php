<?php

namespace App\Actions\Warehouse;

use App\Models\Warehouse;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditWarehouse
{
    use AsAction;

    public function handle(Warehouse $warehouse, array $data): Warehouse
    {
        try {
            $warehouse->update($data);
            return $warehouse;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
