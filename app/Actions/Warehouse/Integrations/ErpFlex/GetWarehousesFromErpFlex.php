<?php

namespace App\Actions\Warehouse\Integrations\ErpFlex;

use App\Actions\Warehouse\CreateWarehouse;
use App\Actions\Warehouse\EditWarehouse;
use App\Actions\ThirdPartyWarehouse\CreateThirdPartyWarehouse;
use App\Actions\ThirdPartyWarehouse\EditThirdPartyWarehouse;
use App\Actions\ThirdPartyWarehouse\Queries\GetThirdPartyWarehouseByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductCategoryService;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\Warehouse;
use App\Models\ThirdPartyWarehouse;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetWarehousesFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexProductCategoryService $erpFlexProductCategoryService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.equipment.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexProductCategoryService = new ErpFlexProductCategoryService();

        $this->integrateWarehouses();

        if ($userId) {
            success_database_notification($userId, __('warehouses.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateWarehouses(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_warehouses_received_at'])
                ? carbon($this->integrationSetting->settings['last_warehouses_received_at'])
                : now()->startOfMillennium();
        }

        $this->createOrUpdateWarehouse(
            $this->createOrUpdateThirdPartyWarehouse((object) ['SBW_ID' => 0, 'SBW_Desc' => 'Padrão']),
            (object) ['SBW_ID' => 0, 'SBW_Desc' => 'Padrão']
        );

        $i = 0;

        while (true) {
            $erpFlexApiWarehouses = $this->erpFlexProductCategoryService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiWarehouses as $erpFlexWarehouse) {
                try {
                    $this->processSingleWarehouse($erpFlexWarehouse);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiWarehouses) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_warehouses_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleWarehouse(object $erpFlexWarehouse): void
    {
        $this->createOrUpdateWarehouse(
            $this->createOrUpdateThirdPartyWarehouse($erpFlexWarehouse),
            $erpFlexWarehouse
        );
    }

    private function createOrUpdateThirdPartyWarehouse(object $erpFlexWarehouse): ThirdPartyWarehouse
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexWarehouse->SBW_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexWarehouse), true),
        ];

        /** @var \App\Models\ThirdPartyWarehouse|null $thirdPartyWarehouse */
        $thirdPartyWarehouse = GetThirdPartyWarehouseByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexWarehouse->SBW_ID);

        return is_null($thirdPartyWarehouse)
            ? CreateThirdPartyWarehouse::run($data)
            : EditThirdPartyWarehouse::run($thirdPartyWarehouse, $data);
    }

    private function createOrUpdateWarehouse(ThirdPartyWarehouse $thirdPartyWarehouse, object $erpFlexWarehouse): Warehouse
    {
        $data = [
            'name' => $erpFlexWarehouse->SBW_Desc,
        ];

        if (is_null($thirdPartyWarehouse->warehouse_id)) {
            /** @var \App\Models\Warehouse $warehouse */
            $warehouse = CreateWarehouse::run($data);

            EditThirdPartyWarehouse::run($thirdPartyWarehouse, ['warehouse_id' => $warehouse->id]);

            return $warehouse;
        }

        return EditWarehouse::run($thirdPartyWarehouse->warehouse, $data);
    }
}
