<?php

namespace App\Actions\ApiRequestLog;

use App\Models\ApiRequestLog;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Saloon\Http\Response;
use Throwable;

class CreateApiRequestLog
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  mixed $model
     * @param  \Saloon\Http\Response $response
     * @param  bool|null $success
     * @param  string|null $errorDescription
     * @param  array|null $requestBody
     * @return \App\Models\ApiRequestLog
     */
    public function handle(
        mixed $model,
        Response $response,
        ?bool $success = null,
        ?string $errorDescription = null,
        ?array $requestBody = null
    ): ApiRequestLog {
        try {
            $request = $response->getPsrRequest();

            if (is_null($success)) {
                $success = $response->successful();
            }

            $requestHeaders = ouess_aes256cbc_encrypt(json_encode(['data' => $request->getHeaders()], true));

            $requestBody = ouess_aes256cbc_encrypt(json_encode($requestBody));

            $responseHeaders = ouess_aes256cbc_encrypt(json_encode($response->headers()));

            $responseBody = is_null(json_decode($response->body(), true))
                ? ouess_aes256cbc_encrypt(json_encode(['data' => $response->body()]))
                : ouess_aes256cbc_encrypt($response->body());

            $debugBacktrace = debug_backtrace();

            return ApiRequestLog::create([
                'model_id' => $model->id,
                'model_type' => get_class($model),
                'service_name' => $debugBacktrace[2]['class'] ?? null,
                'service_method' => $debugBacktrace[2]['function'] ?? null,
                'success' => $success,
                'status_code' => $response->status(),
                'response_headers' => $responseHeaders,
                'response_body' => $responseBody,
                'method' => $request->getMethod(),
                'url' => $request->getUri(),
                'request_headers' => $requestHeaders,
                'request_body' => $requestBody,
                'error_description' => $errorDescription,
            ]);
        } catch (Throwable $th) {
            error($th);
        }
    }
}
