<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Http\Requests\ReportActionRequest;
use App\Core\Reports\Core\BaseReport;
use App\Enums\ReportEnum;
use App\Enums\ServiceOrderStatusEnum;
use App\Exports\Reports\ServiceOrdersExport;
use App\Models\Customer;
use App\Models\Equipment;
use App\Models\ServiceOrder;
use App\Models\ServiceType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateServiceOrdersReport extends BaseReport implements Reportable
{
    use AsAction;

    public function asController(ReportActionRequest $request): mixed
    {
        $token = $request->input('token');
        $decodedToken = base64_decode($token);
        $decodedParameters = explode(';', $decodedToken);

        $this->currentFormat = $decodedParameters[9];
        $this->excelFileName = 'ordens de serviço';
        $this->reportName = ReportEnum::ServiceOrders->value;
        $this->reportExportClassName = ServiceOrdersExport::class;
        $this->notificationSubjectReportName = 'relatório de ordens de serviço';
        $this->notificationBodyReportEntity = 'ordens de serviço';

        try {
            $this->reportData = $this->handle(
                dateFrom: !is_null($decodedParameters[0])
                    ? carbon($decodedParameters[0])
                    : null,
                dateTo: !is_null($decodedParameters[1])
                    ? carbon($decodedParameters[1])
                    : null,
                protocolCode: $decodedParameters[2],
                protocolInvoiceNumber: $decodedParameters[3],
                customerId: !is_null($decodedParameters[4])
                    ? (int) $decodedParameters[4]
                    : null,
                equipmentId: !is_null($decodedParameters[5])
                    ? (int) $decodedParameters[5]
                    : null,
                serviceTypeId: !is_null($decodedParameters[6])
                    ? (int) $decodedParameters[6]
                    : null,
                employeeId: !is_null($decodedParameters[7])
                    ? (int) $decodedParameters[7]
                    : null,
                status: $decodedParameters[8],
            );
        } catch (Throwable $th) {
            error($th);
            return redirect_report_error();
        }

        return $this->getReport();
    }

    public function handle(
        ?Carbon $dateFrom = null,
        ?Carbon $dateTo = null,
        ?string $protocolCode = '',
        ?string $protocolInvoiceNumber = '',
        ?int $customerId = null,
        ?int $equipmentId = null,
        ?int $serviceTypeId = null,
        ?int $employeeId = null,
        ?string $status = '',
    ): array {
        if (is_null($dateFrom) && is_null($dateTo)) {
            $dateFrom = now()->startOfMonth();
            $dateTo = now()->endOfMonth();
        }

        return $this->isScreenable()
            ? $this->buildPDFData($dateFrom, $dateTo, $protocolCode, $protocolInvoiceNumber, $customerId, $equipmentId, $serviceTypeId, $employeeId, $status)
            : $this->buildExcelData($dateFrom, $dateTo, $protocolCode, $protocolInvoiceNumber, $customerId, $equipmentId, $serviceTypeId, $employeeId, $status);
    }

    public function buildPDFData(mixed ...$params): array
    {
        $serviceOrders = $this->buildReportData($params[0], $params[1], $params[2], $params[3], $params[4], $params[5], $params[6], $params[7], $params[8]);

        $data = [
            'serviceOrders' => $serviceOrders,
            'dateFrom' => format_date($params[0]),
            'dateTo' => format_date($params[1]),
            'protocolCode' => $params[2],
            'protocolInvoiceNumber' => $params[3],
            'status' => $params[8] !== ''
                ? ServiceOrderStatusEnum::getTranslated()[$params[8]]
                : 'Todos',
        ];

        if (!is_null($params[4]) && $params[4] !== '' && $params[4] !== 0) {
            /** @var \App\Models\Customer $customer */
            $customer = Customer::find($params[4]);

            $data = array_merge($data, [
                'customerId' => $params[4],
                'customerName' => $customer->name,
                'customerTradingName' => $customer->trading_name,
                'customerTaxIdNumber' => $customer->friendly_tax_id_number
            ]);
        }

        if (!is_null($params[5]) && $params[5] !== '' && $params[5] !== 0) {
            /** @var \App\Models\Equipment $equipment */
            $equipment = Equipment::find($params[5]);

            $data = array_merge($data, [
                'equipmentId' => $params[5],
                'equipmentName' => $equipment->name,
                'equipmentSerialNumber' => $equipment->serial_number
            ]);
        }

        if (!is_null($params[6]) && $params[6] !== '' && $params[6] !== 0) {
            /** @var \App\Models\ServiceType $serviceType */
            $serviceType = ServiceType::find($params[6]);

            $data = array_merge($data, [
                'serviceTypeId' => $params[6],
                'serviceTypeName' => $serviceType->name
            ]);
        }

        if (!is_null($params[7]) && $params[7] !== '' && $params[7] !== 0) {
            /** @var \App\Models\User $user */
            $user = User::find($params[7]);

            $data = array_merge($data, [
                'employeeId' => $params[7],
                'employeeName' => $user->name
            ]);
        }

        return $data;
    }

    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData($params[0], $params[1], $params[2], $params[3], $params[4], $params[5], $params[6], $params[7], $params[8]);
    }

    public function buildReportData(
        ?Carbon $dateFrom,
        ?Carbon $dateTo,
        ?string $protocolCode,
        ?string $protocolInvoiceNumber,
        ?int $customerId,
        ?int $equipmentId,
        ?int $serviceTypeId,
        ?int $employeeId,
        ?string $status,
    ): array {
        return ServiceOrder::query()
            ->with([
                'customer:id,name,trading_name,tax_id_number',
                'employee:id,name',
                'equipment:id,name,serial_number',
                'serviceType:id,name',
                'protocol:id,code,invoice_number',
            ])
            ->when($dateFrom, fn(Builder $query): Builder => $query->whereDate('created_at', '>=', $dateFrom->format('Y-m-d')))
            ->when($dateTo, fn(Builder $query): Builder => $query->whereDate('created_at', '<=', $dateTo->format('Y-m-d')))
            ->when($protocolCode && trim($protocolCode) !== '', fn(Builder $query): Builder => $query->whereRelation('protocol', 'code', $protocolCode))
            ->when($protocolInvoiceNumber && trim($protocolInvoiceNumber) !== '', fn(Builder $query): Builder => $query->whereRelation('protocol', 'invoice_number', $protocolInvoiceNumber))
            ->when($customerId, fn(Builder $query): Builder => $query->where('customer_id', $customerId))
            ->when($equipmentId, fn(Builder $query): Builder => $query->where('equipment_id', $equipmentId))
            ->when($serviceTypeId, fn(Builder $query): Builder => $query->where('service_type_id', $serviceTypeId))
            ->when($employeeId, fn(Builder $query): Builder => $query->where('employee_id', $employeeId))
            ->when($status, fn(Builder $query): Builder => $query->where('status', $status))
            ->get()
            ->map(fn(ServiceOrder $serviceOrder): array => [
                'code' => $serviceOrder->code,
                'protocol_invoice_number' => $serviceOrder->protocol?->invoice_number ?? '',
                'customer_name' => $serviceOrder->customer?->name ?? '',
                'customer_trading_name' => $serviceOrder->customer?->trading_name ?? '',
                'customer_tax_id_number' => $serviceOrder->customer?->friendly_tax_id_number ?? '',
                'equipment_name' => $serviceOrder->equipment?->name ?? '',
                'service_type_name' => $serviceOrder->serviceType?->name ?? '',
                'employee_name' => $serviceOrder->employee?->name ?? '',
                'status' => $serviceOrder->friendly_status,
                'started_at' => format_datetime($serviceOrder->started_at),
                'finished_at' => format_datetime($serviceOrder->finished_at),
                'created_at' => format_datetime($serviceOrder->created_at),
            ])
            ->toArray();
    }
}
