<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Http\Requests\ReportActionRequest;
use App\Core\Reports\Core\BaseReport;
use App\Enums\ReportEnum;
use App\Models\Checklist;
use App\Models\Customer;
use App\Models\Equipment;
use App\Models\ServiceOrder;
use App\Models\ServiceType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateServiceOrdersExecutionReport extends BaseReport implements Reportable
{
    use AsAction;

    public function asController(ReportActionRequest $request): mixed
    {
        $token = $request->input('token');
        $decodedToken = base64_decode($token);
        $decodedParameters = explode(';', $decodedToken);

        $this->currentFormat = $decodedParameters[7];
        $this->excelFileName = 'execução de ordens de serviço';
        $this->reportName = ReportEnum::ServiceOrdersExecution->value;
        $this->notificationSubjectReportName = 'relatório de execução de ordens de serviço';
        $this->notificationBodyReportEntity = 'execução de ordens de serviço';

        try {
            $this->reportData = $this->handle(
                dateFrom: !is_null($decodedParameters[0])
                    ? carbon($decodedParameters[0])
                    : null,
                dateTo: !is_null($decodedParameters[1])
                    ? carbon($decodedParameters[1])
                    : null,
                checklistId: $decodedParameters[2],
                customerId: !is_null($decodedParameters[3])
                    ? (int) $decodedParameters[3]
                    : null,
                equipmentId: !is_null($decodedParameters[4])
                    ? (int) $decodedParameters[4]
                    : null,
                serviceTypeId: !is_null($decodedParameters[5])
                    ? (int) $decodedParameters[5]
                    : null,
                employeeId: !is_null($decodedParameters[6])
                    ? (int) $decodedParameters[6]
                    : null,
            );
        } catch (Throwable $th) {
            error($th);
            return redirect_report_error();
        }

        return $this->getReport();
    }

    public function handle(
        ?Carbon $dateFrom = null,
        ?Carbon $dateTo = null,
        ?int $checklistId = null,
        ?int $customerId = null,
        ?int $equipmentId = null,
        ?int $serviceTypeId = null,
        ?int $employeeId = null,
    ): array {
        if (is_null($dateFrom) && is_null($dateTo)) {
            $dateFrom = now()->startOfMonth();
            $dateTo = now()->endOfMonth();
        }

        return $this->isScreenable()
            ? $this->buildPDFData($dateFrom, $dateTo, $checklistId, $customerId, $equipmentId, $serviceTypeId, $employeeId)
            : $this->buildExcelData($dateFrom, $dateTo, $checklistId, $customerId, $equipmentId, $serviceTypeId, $employeeId);
    }

    public function buildPDFData(mixed ...$params): array
    {
        $serviceOrders = $this->buildReportData($params[0], $params[1], $params[2], $params[3], $params[4], $params[5], $params[6]);

        $data = [
            'serviceOrders' => $serviceOrders,
            'dateFrom' => format_date($params[0]),
            'dateTo' => format_date($params[1]),
            'checklistId' => $params[2],
            'checklistName' => Checklist::find($params[2])?->name ?? '',
        ];

        if (!is_null($params[3]) && $params[3] !== '' && $params[3] !== 0) {
            /** @var \App\Models\Customer $customer */
            $customer = Customer::find($params[3]);

            $data = array_merge($data, [
                'customerId' => $params[3],
                'customerName' => $customer->name,
                'customerTradingName' => $customer->trading_name,
                'customerTaxIdNumber' => $customer->friendly_tax_id_number
            ]);
        }

        if (!is_null($params[4]) && $params[4] !== '' && $params[4] !== 0) {
            /** @var \App\Models\Equipment $equipment */
            $equipment = Equipment::find($params[4]);

            $data = array_merge($data, [
                'equipmentId' => $params[4],
                'equipmentName' => $equipment->name,
                'equipmentSerialNumber' => $equipment->serial_number
            ]);
        }

        if (!is_null($params[5]) && $params[5] !== '' && $params[5] !== 0) {
            /** @var \App\Models\ServiceType $serviceType */
            $serviceType = ServiceType::find($params[5]);

            $data = array_merge($data, [
                'serviceTypeId' => $params[5],
                'serviceTypeName' => $serviceType->name
            ]);
        }

        if (!is_null($params[6]) && $params[6] !== '' && $params[6] !== 0) {
            /** @var \App\Models\User $user */
            $user = User::find($params[6]);

            $data = array_merge($data, [
                'employeeId' => $params[6],
                'employeeName' => $user->name
            ]);
        }

        return $data;
    }

    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData($params[0], $params[1], $params[2], $params[3], $params[4], $params[5], $params[6]);
    }

    public function buildReportData(
        ?Carbon $dateFrom,
        ?Carbon $dateTo,
        ?int $checklistId,
        ?int $customerId,
        ?int $equipmentId,
        ?int $serviceTypeId,
        ?int $employeeId,
    ): array {
        return ServiceOrder::query()
            ->with([
                'serviceOrderExecutionChecklistSteps',
                'customer:id,name,trading_name,tax_id_number',
                'employee:id,name',
                'equipment:id,name,serial_number',
                'serviceType:id,name',
                'protocol:id,code,invoice_number',
            ])
            ->whereRelation('serviceOrderExecutionChecklistSteps', 'checklist_id', $checklistId)
            ->when($dateFrom, fn(Builder $query): Builder => $query->whereDate('created_at', '>=', $dateFrom->format('Y-m-d')))
            ->when($dateTo, fn(Builder $query): Builder => $query->whereDate('created_at', '<=', $dateTo->format('Y-m-d')))
            ->when($customerId, fn(Builder $query): Builder => $query->where('customer_id', $customerId))
            ->when($equipmentId, fn(Builder $query): Builder => $query->where('equipment_id', $equipmentId))
            ->when($serviceTypeId, fn(Builder $query): Builder => $query->where('service_type_id', $serviceTypeId))
            ->when($employeeId, fn(Builder $query): Builder => $query->where('employee_id', $employeeId))
            ->get()
            ->map(fn(ServiceOrder $serviceOrder): array => $serviceOrder->toArray())
            ->toArray();
    }
}
