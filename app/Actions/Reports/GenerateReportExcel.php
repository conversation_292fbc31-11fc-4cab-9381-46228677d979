<?php

namespace App\Actions\Reports;

use App\Core\Reports\Core\ReportGeneratedNotification;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Maatwebsite\Excel\Facades\Excel;

class GenerateReportExcel
{
    use AsAction;

    public function configureJob(JobDecorator $job)
    {
        $job->onQueue(config('queue.custom_names.ouess.reports'));
    }

    public function handle(
        int $userId,
        array $reportData,
        string $filename,
        string $reportExportClassName,
        string $notificationSubjectReportName,
        string $notificationBodyReportEntity,
        bool $download = false
    ): mixed {
        if ($download) {
            return Excel::download(new $reportExportClassName($reportData), date('YmdHis') . "_{$filename}.xlsx");
        }

        $reportFullPath = "reports/" . date('YmdHis') . "_{$filename}.xlsx";

        Excel::store(new $reportExportClassName($reportData), $reportFullPath);

        $notification = new ReportGeneratedNotification(
            $notificationSubjectReportName,
            $notificationBodyReportEntity,
            $reportFullPath,
            $filename
        );

        User::query()
            ->find($userId)
            ->notify($notification);

        return true;
    }
}
