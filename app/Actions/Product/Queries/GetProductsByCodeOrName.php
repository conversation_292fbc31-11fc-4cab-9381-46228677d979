<?php

namespace App\Actions\Product\Queries;

use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProductsByCodeOrName
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string|null $search
     * @param  bool $orderByName
     * @return \Illuminate\Support\Collection
     */
    public function handle(?string $search, bool $orderByName = false): Collection
    {
        return Product::query()
            ->where(function (Builder $query) use ($search): Builder {
                return $query
                    ->where('code', 'like', "%$search%")
                    ->orWhere('name', 'like', "%$search%");
            })
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
