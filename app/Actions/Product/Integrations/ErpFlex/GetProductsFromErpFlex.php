<?php

namespace App\Actions\Product\Integrations\ErpFlex;

use App\Actions\Product\CreateProduct;
use App\Actions\Product\EditProduct;
use App\Actions\ThirdPartyMeasuringUnit\Queries\GetThirdPartyMeasuringUnitByThirdPartyId;
use App\Actions\ThirdPartyProduct\CreateThirdPartyProduct;
use App\Actions\ThirdPartyProduct\EditThirdPartyProduct;
use App\Actions\ThirdPartyProduct\Queries\GetThirdPartyProductByThirdPartyId;
use App\Actions\ThirdPartyProductType\Queries\GetThirdPartyProductTypeByThirdPartyId;
use App\Actions\ThirdPartyWarehouse\Queries\GetThirdPartyWarehouseByThirdPartyId;
use App\Actions\ThirdPartyWarehouseProduct\CreateThirdPartyWarehouseProduct;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductVariantService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductVariantWarehouseService;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\Product;
use App\Models\ThirdPartyProduct;
use App\Models\WarehouseProduct;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetProductsFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexProductService $erpFlexProductService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.products.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexProductService = new ErpFlexProductService();

        $this->integrateProducts();

        if ($userId) {
            success_database_notification($userId, __('products.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateProducts(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_products_received_at'])
                ? carbon($this->integrationSetting->settings['last_products_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiProducts = $this->erpFlexProductService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiProducts as $erpFlexProduct) {
                try {
                    $this->processSingleProduct($erpFlexProduct);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiProducts) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_product_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleProduct(object $erpFlexProduct): void
    {
        $this->createOrUpdateProduct(
            $this->createOrUpdateThirdPartyProduct($erpFlexProduct),
            $erpFlexProduct
        );
    }

    private function createOrUpdateThirdPartyProduct(object $erpFlexProduct): ThirdPartyProduct
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexProduct->SB1_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexProduct), true),
        ];

        /** @var \App\Models\ThirdPartyProduct|null $thirdPartyProduct */
        $thirdPartyProduct = GetThirdPartyProductByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexProduct->SB1_ID);

        return is_null($thirdPartyProduct)
            ? CreateThirdPartyProduct::run($data)
            : EditThirdPartyProduct::run($thirdPartyProduct, $data);
    }

    private function createOrUpdateProduct(ThirdPartyProduct $thirdPartyProduct, object $erpFlexProduct): Product
    {
        $productVariant = ErpFlexProductVariantService::make()->getByProductId($erpFlexProduct->SB1_ID);

        $data = [
            'product_type_id' => GetThirdPartyProductTypeByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexProduct->SB1_IDSBA)->product_type_id,
            'measuring_unit_id' => GetThirdPartyMeasuringUnitByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexProduct->SB1_IDSB1_UM)->measuring_unit_id,
            'code' => $erpFlexProduct->SB1_Codigo,
            'name' => $erpFlexProduct->SB1_Desc,
            'serial_number' => $productVariant->SB2_EAN,
            'additional_info' => $erpFlexProduct->SB1_Caracteristicas,
            'default_amount' => $erpFlexProduct->SB1_Preco,
            'available_quantity' => ((float) $productVariant->SB2_QAtu) - ((float) $productVariant->SB2_Reserva),
            'active' => true,
        ];

        if (is_null($thirdPartyProduct->product_id)) {
            /** @var \App\Models\Product $product */
            $product = CreateProduct::run($data);

            EditThirdPartyProduct::run($thirdPartyProduct, ['product_id' => $product->id]);

            WarehouseProduct::query()
                ->whereRelation('warehouse', 'name', 'Padrão')
                ->updateOrCreate([
                    'product_id' => $product->id,
                ], [
                    'warehouse_id' => 1,
                    'available_amount' => $data['available_quantity'],
                    'minimum_amount' => 0,
                ]);

            return $product;
        }

        WarehouseProduct::query()
            ->whereRelation('warehouse', 'name', 'Padrão')
            ->updateOrCreate([
                'product_id' => $thirdPartyProduct->product_id,
            ], [
                'warehouse_id' => 1,
                'available_amount' => $data['available_quantity'],
                'minimum_amount' => 0,
            ]);

        return EditProduct::run($thirdPartyProduct->product, $data);
    }
}
