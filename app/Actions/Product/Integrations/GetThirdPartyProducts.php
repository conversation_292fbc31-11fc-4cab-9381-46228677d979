<?php

namespace App\Actions\Product\Integrations;

use App\Actions\IntegrationSetting\Queries\GetActiveIntegrationSettings;
use App\Actions\Product\Integrations\ErpFlex\GetProductsFromErpFlex;
use App\Models\IntegrationType;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyProducts
{
    use AsAction;

    public string $commandSignature = 'ouess:get-third-party-products {tenant?}';
    public string $commandDescription = 'Get all products from the integrations.';

    public function asCommand(Command $command): void
    {
        $tenants = Tenant::query()
            ->when($command->argument('tenant'), function (Builder $query) use ($command): Builder {
                return $query->where('id', $command->argument('tenant'));
            })
            ->get();

        tenancy()->runForMultiple(
            $tenants,
            fn() => $this->handle()
        );
    }

    public function handle(bool $force = false): void
    {
        $integrationSettings = GetActiveIntegrationSettings::run();

        foreach ($integrationSettings as $integrationSetting) {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            switch ($integrationSetting->integration_type_id) {
                case IntegrationType::TYPE_ERP_FLEX:
                    GetProductsFromErpFlex::dispatch($integrationSetting, $force, auth()->check() ? auth()->id() : null);
                    break;
                default:
                    break;
            }
        }
    }
}
