<?php

namespace App\Actions\Product;

use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteProduct
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Product $product
     * @return void
     */
    public function handle(Product $product)
    {
        try {
            DB::transaction(function () use ($product): void {
                $product->productWarehouses()->delete();
                $product->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
