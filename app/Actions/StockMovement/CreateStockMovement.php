<?php

namespace App\Actions\StockMovement;

use App\Models\StockMovement;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateStockMovement
{
    use AsAction;

    public function handle(array $data): StockMovement
    {
        try {
            return DB::transaction(function () use ($data): StockMovement {
                /** @var \App\Models\StockMovement $stockMovement */
                $stockMovement = StockMovement::create($data);

                PerformStockMovement::run($stockMovement);

                return $stockMovement;
            });
        } catch (Throwable $th) {
            throw_error($th, $th->getMessage());
        }
    }
}
