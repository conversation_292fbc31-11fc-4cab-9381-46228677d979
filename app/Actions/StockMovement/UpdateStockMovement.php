<?php

namespace App\Actions\StockMovement;

use App\Models\StockMovement;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class UpdateStockMovement
{
    use AsAction;

    public function handle(StockMovement $stockMovement, array $data): StockMovement
    {
        try {
            return DB::transaction(function () use ($stockMovement, $data): StockMovement {
                ReverseStockMovement::run($stockMovement);

                $stockMovement->update($data);
                $stockMovement->refresh();

                PerformStockMovement::run($stockMovement);

                return $stockMovement;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
