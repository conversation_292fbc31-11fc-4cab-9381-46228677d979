<?php

namespace App\Actions\StockMovement;

use App\Actions\WarehouseProduct\Queries\GetWarehouseProductByWarehouseIdAndProductId;
use App\Models\StockMovement;
use Lorisleiva\Actions\Concerns\AsAction;

class ReverseStockMovement
{
    use AsAction;

    public function handle(StockMovement $stockMovement): void
    {
        if ($stockMovement->origin_warehouse_id) {
            GetWarehouseProductByWarehouseIdAndProductId::run($stockMovement->origin_warehouse_id, $stockMovement->item_id)
                ->first()
                ->increment('available_amount', $stockMovement->quantity);
        }

        if ($stockMovement->destination_warehouse_id) {
            GetWarehouseProductByWarehouseIdAndProductId::run($stockMovement->destination_warehouse_id, $stockMovement->item_id)
                ->first()
                ->decrement('available_amount', $stockMovement->quantity);
        }
    }
}
