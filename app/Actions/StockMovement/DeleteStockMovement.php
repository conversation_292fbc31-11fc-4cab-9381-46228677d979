<?php

namespace App\Actions\StockMovement;

use App\Models\StockMovement;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteStockMovement
{
    use AsAction;

    public function handle(StockMovement $stockMovement): void
    {
        try {
            DB::transaction(function () use ($stockMovement): void {
                ReverseStockMovement::run($stockMovement);

                $stockMovement->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
