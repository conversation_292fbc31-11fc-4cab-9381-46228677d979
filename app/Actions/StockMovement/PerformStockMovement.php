<?php

namespace App\Actions\StockMovement;

use App\Actions\WarehouseProduct\Queries\GetWarehouseProductByWarehouseIdAndProductId;
use App\Models\StockMovement;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;

class PerformStockMovement
{
    use AsAction;

    public function handle(StockMovement $stockMovement): void
    {
        if ($stockMovement->origin_warehouse_id) {
            /** @var \App\Models\WarehouseProduct $originWarehouseProduct */
            $originWarehouseProduct = GetWarehouseProductByWarehouseIdAndProductId::run($stockMovement->origin_warehouse_id, $stockMovement->item_id)->first();

            if ($originWarehouseProduct->available_amount - $stockMovement->quantity < 0) {
                throw_error(
                    new Exception('Não foi possível realizar a movimentação de estoque pois a diferença do movimento geraria um estoque negativo.'),
                    'Não foi possível realizar a movimentação de estoque pois a diferença do movimento geraria um estoque negativo.',
                );
            }

            $originWarehouseProduct->decrement('available_amount', $stockMovement->quantity);
        }

        if ($stockMovement->destination_warehouse_id) {
            /** @var \App\Models\WarehouseProduct $destinationWarehouseProduct */
            $destinationWarehouseProduct = GetWarehouseProductByWarehouseIdAndProductId::run($stockMovement->destination_warehouse_id, $stockMovement->item_id)->first();

            if (!$destinationWarehouseProduct) {
                $stockMovement->destinationWarehouse->warehouseProducts()->create([
                    'product_id' => $stockMovement->item_id,
                    'available_amount' => $stockMovement->quantity,
                    'minimum_amount' => 0,
                ]);
            } else {
                $destinationWarehouseProduct->increment('available_amount', $stockMovement->quantity);
            }
        }
    }
}
