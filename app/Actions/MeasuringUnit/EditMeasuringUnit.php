<?php

namespace App\Actions\MeasuringUnit;

use App\Models\MeasuringUnit;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditMeasuringUnit
{
    use AsAction;

    public function handle(MeasuringUnit $measuringUnit, array $data): MeasuringUnit
    {
        try {
            $measuringUnit->update($data);
            return $measuringUnit;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
