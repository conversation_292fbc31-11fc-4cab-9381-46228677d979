<?php

namespace App\Actions\MeasuringUnit\Integrations\ErpFlex;

use App\Actions\MeasuringUnit\CreateMeasuringUnit;
use App\Actions\MeasuringUnit\EditMeasuringUnit;
use App\Actions\ThirdPartyMeasuringUnit\CreateThirdPartyMeasuringUnit;
use App\Actions\ThirdPartyMeasuringUnit\EditThirdPartyMeasuringUnit;
use App\Actions\ThirdPartyMeasuringUnit\Queries\GetThirdPartyMeasuringUnitByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexMeasuringUnitService;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\MeasuringUnit;
use App\Models\ThirdPartyMeasuringUnit;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetMeasuringUnitsFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexMeasuringUnitService $erpFlexMeasuringUnitService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.measuring_units.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexMeasuringUnitService = new ErpFlexMeasuringUnitService();

        $this->integrateMeasuringUnits();

        if ($userId) {
            success_database_notification($userId, __('measuring_units.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateMeasuringUnits(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_measuring_units_received_at'])
                ? carbon($this->integrationSetting->settings['last_measuring_units_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiMeasuringUnits = $this->erpFlexMeasuringUnitService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiMeasuringUnits as $erpFlexMeasuringUnit) {
                try {
                    $this->processSingleMeasuringUnit($erpFlexMeasuringUnit);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiMeasuringUnits) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_measuring_units_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleMeasuringUnit(object $erpFlexMeasuringUnit): void
    {
        $this->createOrUpdateMeasuringUnit(
            $this->createOrUpdateThirdPartyMeasuringUnit($erpFlexMeasuringUnit),
            $erpFlexMeasuringUnit
        );
    }

    private function createOrUpdateThirdPartyMeasuringUnit(object $erpFlexMeasuringUnit): ThirdPartyMeasuringUnit
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexMeasuringUnit->SB1_UM_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexMeasuringUnit), true),
        ];

        /** @var \App\Models\ThirdPartyMeasuringUnit|null $thirdPartyMeasuringUnit */
        $thirdPartyMeasuringUnit = GetThirdPartyMeasuringUnitByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexMeasuringUnit->SB1_UM_ID);

        return is_null($thirdPartyMeasuringUnit)
            ? CreateThirdPartyMeasuringUnit::run($data)
            : EditThirdPartyMeasuringUnit::run($thirdPartyMeasuringUnit, $data);
    }

    private function createOrUpdateMeasuringUnit(ThirdPartyMeasuringUnit $thirdPartyMeasuringUnit, object $erpFlexMeasuringUnit): MeasuringUnit
    {
        $data = [
            'name' => $erpFlexMeasuringUnit->SB1_UM_Desc,
        ];

        if (is_null($thirdPartyMeasuringUnit->measuring_unit_id)) {
            /** @var \App\Models\MeasuringUnit $measuringUnit */
            $measuringUnit = CreateMeasuringUnit::run($data);

            EditThirdPartyMeasuringUnit::run($thirdPartyMeasuringUnit, ['measuring_unit_id' => $measuringUnit->id]);

            return $measuringUnit;
        }

        return EditMeasuringUnit::run($thirdPartyMeasuringUnit->measuringUnit, $data);
    }
}
