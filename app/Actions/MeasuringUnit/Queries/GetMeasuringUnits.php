<?php

namespace App\Actions\MeasuringUnit\Queries;

use App\Models\MeasuringUnit;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetMeasuringUnits
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  bool $orderByName
     * @return \Illuminate\Support\Collection
     */
    public function handle(bool $orderByName = false): Collection
    {
        return MeasuringUnit::query()
            ->when($orderByName, fn(Builder $query): Builder => $query->orderBy('name'))
            ->get();
    }
}
