<?php

namespace App\Actions\Region;

use App\Integrations\Api\Ibge\Services\IbgeLocationService;
use App\Models\City;
use App\Models\Region;
use App\Models\State;
use Lorisleiva\Actions\Concerns\AsAction;

class PopulateRegions
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        $ibgeLocationService = new IbgeLocationService();

        foreach ($ibgeLocationService->getRegions() as $region) {
            Region::updateOrCreate([
                'id' => $region->id,
            ], [
                'abbreviation' => $region->sigla,
                'name' => $region->nome
            ]);
        }

        foreach ($ibgeLocationService->getStates() as $state) {
            State::updateOrCreate([
                'id' => $state->id,
            ], [
                'region_id' => $state->regiao->id,
                'abbreviation' => $state->sigla,
                'name' => $state->nome
            ]);
        }

        foreach ($ibgeLocationService->getCities() as $city) {
            City::updateOrCreate([
                'id' => $city->id,
            ], [
                'state_id' => $city->microrregiao->mesorregiao->UF->id,
                'name' => $city->nome
            ]);
        }
    }
}
