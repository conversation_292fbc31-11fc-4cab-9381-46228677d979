<?php

namespace App\Actions\ThirdPartyProductType\Queries;

use App\Models\ThirdPartyProductType;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyProductTypeByProductTypeId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $productTypeId): ?ThirdPartyProductType
    {
        return ThirdPartyProductType::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('product_type_id', $productTypeId)
            ->first();
    }
}
