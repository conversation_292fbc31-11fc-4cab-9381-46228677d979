<?php

namespace App\Actions\ThirdPartyProductType\Queries;

use App\Models\ThirdPartyProductType;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyProductTypeByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyProductType
    {
        return ThirdPartyProductType::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
