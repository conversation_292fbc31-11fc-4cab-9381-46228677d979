<?php

namespace App\Actions\ThirdPartyProductType;

use App\Models\ThirdPartyProductType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyProductType
{
    use AsAction;

    public function handle(array $data): ThirdPartyProductType
    {
        try {
            return ThirdPartyProductType::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
