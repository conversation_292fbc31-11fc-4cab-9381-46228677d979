<?php

namespace App\Actions\ThirdPartyProductType;

use App\Models\ThirdPartyProductType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyProductType
{
    use AsAction;

    public function handle(ThirdPartyProductType $thirdPartyProductType, array $data): ThirdPartyProductType
    {
        try {
            $thirdPartyProductType->update($data);
            return $thirdPartyProductType;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
