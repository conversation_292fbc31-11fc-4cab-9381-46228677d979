<?php

namespace App\Actions\Core;

use App\Models\SmtpConfiguration;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Email;
use Throwable;

class SendEmail
{
    use AsAction;

    public function handle(
        array $toAddresses,
        string $subject,
        string $message,
        SmtpConfiguration $smtpConfiguration,
        array $attachments = []
    ): void {
        try {
            $authData = "$smtpConfiguration->username:$smtpConfiguration->password";
            $fullHost = "$smtpConfiguration->host:$smtpConfiguration->port";

            $mailer = new Mailer(Transport::fromDsn("smtp://$authData@$fullHost?verify_peer=false"));

            $email = (new Email())
                ->from($smtpConfiguration->from_address)
                ->subject($subject)
                ->html($message);

            foreach ($toAddresses as $toAddress) {
                $email->addTo(trim($toAddress));
            }

            foreach ($attachments as $attachment) {
                $email->attachFromPath(storage_path("app/{$attachment['filename']}"), $attachment['download_filename'], 'application/pdf');
            }

            $mailer->send($email);
        } catch (Throwable $th) {
            error($th);
        }
    }
}
