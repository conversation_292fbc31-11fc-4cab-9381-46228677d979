<?php

namespace App\Actions\Core;

use App\Models\Permission;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;
use ReflectionClass;
use Spatie\Permission\Models\Role;

class SyncMissingPermissions
{
    use AsAction;

    public string $commandSignature = 'app:sync-missing-permissions';

    /**
     * Handle the action.
     *
     * @return int
     */
    public function handle(): int
    {
        tenancy()->runForMultiple(Tenant::all(), function () {
            $databasePermissions = Permission::query()
                ->pluck('name')
                ->toArray();

            $permissionReflectionClass = new ReflectionClass(Permission::class);

            $reflectionPermissions = array_values($permissionReflectionClass->getConstants());

            $permissionsToBeAdded = array_filter($reflectionPermissions, function ($item) use ($databasePermissions) {
                return !in_array(
                    $item,
                    array_merge($databasePermissions, ['created_at', 'updated_at'])
                );
            });

            foreach ($permissionsToBeAdded as $permission) {
                Permission::create(['name' => $permission]);
            }

            Role::query()
                ->where('name', 'Administrador')
                ->first()
                ->givePermissionTo($permissionsToBeAdded);

            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        });

        return Command::SUCCESS;
    }
}
