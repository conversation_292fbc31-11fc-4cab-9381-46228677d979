<?php

namespace App\Actions\ThirdPartyCustomer\Queries;

use App\Models\ThirdPartyCustomer;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyCustomerByCustomerId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $customerId): ?ThirdPartyCustomer
    {
        return ThirdPartyCustomer::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('customer_id', $customerId)
            ->first();
    }
}
