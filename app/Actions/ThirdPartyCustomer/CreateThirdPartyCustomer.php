<?php

namespace App\Actions\ThirdPartyCustomer;

use App\Models\ThirdPartyCustomer;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyCustomer
{
    use AsAction;

    public function handle(array $data): ThirdPartyCustomer
    {
        try {
            return ThirdPartyCustomer::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
