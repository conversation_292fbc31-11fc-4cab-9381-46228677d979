<?php

namespace App\Actions\ThirdPartyCustomer;

use App\Models\ThirdPartyCustomer;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyCustomer
{
    use AsAction;

    public function handle(ThirdPartyCustomer $thirdPartyCustomer, array $data): ThirdPartyCustomer
    {
        try {
            $thirdPartyCustomer->update($data);
            return $thirdPartyCustomer;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
