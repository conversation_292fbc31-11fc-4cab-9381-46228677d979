<?php

namespace App\Actions\ServiceOrderHistory;

use App\Models\ServiceOrder;
use App\Models\ServiceOrderHistory;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateServiceOrderHistory
{
    use AsAction;

    public function handle(ServiceOrder $serviceOrder, string $action, ?array $oldAdditionalAttributes = null): ServiceOrderHistory
    {
        return ServiceOrderHistory::create([
            'service_order_id' => $serviceOrder->id,
            'action' => $action,
            'old_additional_attributes' => $oldAdditionalAttributes,
        ]);
    }
}
