<?php

namespace App\Actions\Contract\Queries;

use App\Models\Contract;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetContractsForTerminationNotification
{
    use AsAction;

    public function handle(Carbon $date): Collection
    {
        return Contract::query()
            ->where('ended_at', $date->addDays(60)->format('Y-m-d'))
            ->get();
    }
}
