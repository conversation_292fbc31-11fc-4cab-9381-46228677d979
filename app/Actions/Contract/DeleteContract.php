<?php

namespace App\Actions\Contract;

use App\Models\Contract;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteContract
{
    use AsAction;

    public function handle(Contract $contract): void
    {
        try {
            DB::transaction(function () use ($contract): void {
                $contract->contractItems()->delete();
                $contract->contractOperators()->delete();
                $contract->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
