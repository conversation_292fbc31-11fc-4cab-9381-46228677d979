<?php

namespace App\Actions\Contract;

use App\Actions\ServiceOrder\CreateServiceOrder;
use App\Enums\ContractItemPeriodEnum;
use App\Models\Contract;
use App\Models\ContractItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateContractServiceOrders
{
    use AsAction;

    public function handle(Contract $contract): Contract
    {
        try {
            return DB::transaction(function () use ($contract): Contract {
                $contract->contractItems->each(function (ContractItem $contractItem): void {
                    $this->generateServiceOrdersForContractItem($contractItem);
                });

                return $contract;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    private function generateServiceOrdersForContractItem(ContractItem $contractItem): void
    {
        $startDate = carbon($contractItem->started_at);
        $endDate = carbon($contractItem->ended_at);
        $interval = $contractItem->interval;
        $period = $contractItem->period;
        $quantity = $contractItem->quantity;

        $currentDate = $contractItem->generate_service_order_for_starting_date
            ? $startDate->copy()
            : $this->calculateNextServiceOrderDate($startDate, $interval, $period);

        $serviceOrderCounter = 1;

        while ($currentDate->lte($endDate)) {
            $this->createServiceOrderForContractItem($contractItem, $currentDate, $serviceOrderCounter, $quantity);

            $currentDate = $this->calculateNextServiceOrderDate($currentDate, $interval, $period);
            $serviceOrderCounter++;
        }
    }

    private function calculateNextServiceOrderDate(Carbon $currentDate, int $interval, string $period): Carbon
    {
        $nextDate = $currentDate->copy();

        switch ($period) {
            case ContractItemPeriodEnum::Days->value:
                $nextDate->addDays($interval);
                break;
            case ContractItemPeriodEnum::Weeks->value:
                $nextDate->addWeeks($interval);
                break;
            case ContractItemPeriodEnum::Months->value:
                $nextDate->addMonths($interval);
                break;
            case ContractItemPeriodEnum::Years->value:
                $nextDate->addYears($interval);
                break;
        }

        return $nextDate;
    }

    private function createServiceOrderForContractItem(ContractItem $contractItem, Carbon $scheduledDate, int $serviceOrderCounter, int $quantity): void
    {
        $serviceOrderCode = $this->generateServiceOrderCode($contractItem->contract->code, $contractItem->id, $serviceOrderCounter);

        $serviceOrder = CreateServiceOrder::run([
            'code' => $serviceOrderCode,
            'contract_item_id' => $contractItem->id,
            'customer_id' => $contractItem->contract->customer_id,
            'equipment_id' => $contractItem->equipment_id,
            'service_type_id' => $contractItem->service_type_id,
            'quantity' => $quantity,
            'estimated_duration_in_minutes' => $contractItem->serviceType->estimated_duration_in_minutes,
            'scheduled_to' => $scheduledDate,
            'description' => $contractItem->additional_info,
            'checklist_ids' => [$contractItem->checklist_id],
        ]);
    }

    private function generateServiceOrderCode(string $contractCode, int $contractItemId, int $serviceOrderCounter): string
    {
        return sprintf(
            '%s-CI%d-%d',
            $contractCode,
            $contractItemId,
            $serviceOrderCounter
        );
    }
}
