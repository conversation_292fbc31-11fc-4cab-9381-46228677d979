<?php

namespace App\Actions\Contract;

use App\Actions\Contract\Queries\GetContractsForTerminationNotification;
use App\Actions\User\Queries\GetUsersForNotifications;
use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class SendContractTerminationNotifications
{
    use AsAction;

    public string $commandSignature = 'ouess:send-contract-termination-notifications {date?} {tenant?}';
    public string $commandDescription = 'Send contract termination notifications.';

    public function asCommand(Command $command): void
    {
        $tenants = Tenant::query()
            ->when($command->argument('tenant'), function (Builder $query) use ($command): Builder {
                return $query->where('id', $command->argument('tenant'));
            })
            ->get();

        $date = !is_null($command->argument('date'))
            ? carbon($command->argument('date'))
            : null;

        tenancy()->runForMultiple($tenants, fn() => $this->handle($date));
    }

    public function handle(?Carbon $date = null): void
    {
        $date ??= now();

        $contracts = GetContractsForTerminationNotification::run($date);

        if ($contracts->isEmpty()) {
            return;
        }

        $users = GetUsersForNotifications::run(['contract_termination']);

        $message = 'Os contratos com código [' . $contracts->pluck('code')->implode(', ') . '] estão a 60 dias do seu encerramento.';

        foreach ($users as $user) {
            warning_database_notification($user->id, $message, true);
        }
    }
}
