<?php

namespace App\Actions\ThirdPartyQuote\Queries;

use App\Models\ThirdPartyQuote;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyQuoteByQuoteId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $quoteId): ?ThirdPartyQuote
    {
        return ThirdPartyQuote::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('quote_id', $quoteId)
            ->first();
    }
}
