<?php

namespace App\Actions\ThirdPartyQuote\Queries;

use App\Models\ThirdPartyQuote;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class GetThirdPartyQuoteByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyQuote
    {
        return ThirdPartyQuote::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
