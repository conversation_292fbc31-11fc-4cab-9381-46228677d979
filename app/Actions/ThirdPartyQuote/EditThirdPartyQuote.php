<?php

namespace App\Actions\ThirdPartyQuote;

use App\Models\ThirdPartyQuote;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyQuote
{
    use AsAction;

    public function handle(ThirdPartyQuote $thirdPartyQuote, array $data): ThirdPartyQuote
    {
        try {
            $thirdPartyQuote->update($data);
            return $thirdPartyQuote;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
