<?php

namespace App\Actions\ThirdPartyEquipmentType\Queries;

use App\Models\ThirdPartyEquipmentType;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyEquipmentTypeByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyEquipmentType
    {
        return ThirdPartyEquipmentType::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
