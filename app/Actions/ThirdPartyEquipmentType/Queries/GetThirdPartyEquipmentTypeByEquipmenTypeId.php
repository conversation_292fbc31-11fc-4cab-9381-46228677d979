<?php

namespace App\Actions\ThirdPartyEquipmentType\Queries;

use App\Models\ThirdPartyEquipmentType;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyEquipmentTypeByEquipmentTypeId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $equipmentTypeId): ?ThirdPartyEquipmentType
    {
        return ThirdPartyEquipmentType::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('equipment_type_id', $equipmentTypeId)
            ->first();
    }
}
