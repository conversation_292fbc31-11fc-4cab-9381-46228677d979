<?php

namespace App\Actions\ThirdPartyEquipmentType;

use App\Models\ThirdPartyEquipmentType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditThirdPartyEquipmentType
{
    use AsAction;

    public function handle(ThirdPartyEquipmentType $thirdPartyEquipmentType, array $data): ThirdPartyEquipmentType
    {
        try {
            $thirdPartyEquipmentType->update($data);
            return $thirdPartyEquipmentType;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
