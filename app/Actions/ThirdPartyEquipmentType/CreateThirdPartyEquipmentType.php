<?php

namespace App\Actions\ThirdPartyEquipmentType;

use App\Models\ThirdPartyEquipmentType;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateThirdPartyEquipmentType
{
    use AsAction;

    public function handle(array $data): ThirdPartyEquipmentType
    {
        try {
            return ThirdPartyEquipmentType::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
