<?php

namespace App\Actions\ServiceOrder;

use App\Actions\ServiceOrderHistory\CreateServiceOrderHistory;
use App\Enums\ServiceOrderStatusEnum;
use App\Models\ServiceOrder;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ReopenServiceOrder
{
    use AsAction;

    public function handle(ServiceOrder $serviceOrder): ServiceOrder
    {
        try {
            return DB::transaction(function () use ($serviceOrder): ServiceOrder {
                $oldAttributes = [
                    'status' => $serviceOrder->status,
                    'started_at' => $serviceOrder->started_at,
                    'finished_at' => $serviceOrder->finished_at,
                    'cancelled_at' => $serviceOrder->cancelled_at,
                    'cancellation_additional_info' => $serviceOrder->cancellation_additional_info,
                ];

                $serviceOrder->update([
                    'status' => ServiceOrderStatusEnum::Executing->value,
                    'started_at' => null,
                    'finished_at' => null,
                    'cancelled_at' => null,
                    'cancellation_additional_info' => null,
                ]);

                CreateServiceOrderHistory::run($serviceOrder, 'reopen', $oldAttributes);

                return $serviceOrder;
            });

        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
