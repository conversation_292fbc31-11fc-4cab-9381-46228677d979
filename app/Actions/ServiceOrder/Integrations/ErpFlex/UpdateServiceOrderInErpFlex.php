<?php

namespace App\Actions\ServiceOrder\Integrations\ErpFlex;

use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByCustomerId;
use App\Actions\ThirdPartyEquipment\Queries\GetThirdPartyEquipmentByEquipmentId;
use App\Actions\ThirdPartyServiceOrder\Queries\GetThirdPartyServiceOrderByServiceOrderId;
use App\Actions\ThirdPartyServiceType\Queries\GetThirdPartyServiceTypeByServiceTypeId;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexServiceOrderDto;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductVariantService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexServiceOrderService;
use App\Models\ErpFlexParameter;
use App\Models\IntegrationType;
use App\Models\ServiceOrder;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class UpdateServiceOrderInErpFlex
{
    use AsAction;

    private array $structureItems = [];
    private array $newStepProducts = [];
    private array $deletedStepProducts = [];

    private ServiceOrder $serviceOrder;
    private ErpFlexParameter $erpFlexParameter;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.service_orders.send'));
    }

    public function handle(
        ServiceOrder $serviceOrder,
        array $newStepProducts = [],
        array $deletedStepProducts = [],
        ?int $userId = null
    ): ServiceOrder {
        $this->serviceOrder = $serviceOrder;
        $this->newStepProducts = $newStepProducts;
        $this->deletedStepProducts = $deletedStepProducts;

        $this->erpFlexParameter = GetErpFlexParameter::run();

        /** @var \App\Models\ThirdPartyServiceType|null $thirdPartyServiceType */
        $thirdPartyServiceType = GetThirdPartyServiceTypeByServiceTypeId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->service_type_id);

        if (!$thirdPartyServiceType) {
            throw new Exception('O tipo de serviço utilizado na ordem de serviço não possui vínculo com o ERPFlex.');
        }

        /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->customer_id);

        $this->buildServiceOrderItems();

        /** @var \App\Models\ThirdPartyServiceOrder $thirdPartyServiceOrder */
        $thirdPartyServiceOrder = GetThirdPartyServiceOrderByServiceOrderId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->id);

        $erpFlexServiceOrderDto = new ErpFlexServiceOrderDto(
            os_id: $thirdPartyServiceOrder->third_party_id,
            estrutura_serv: 'N',
            documento: $serviceOrder->code,
            quantidade: 1,
            valor: 0,
            saldo_anterior: 0,
            saldo_atual: 0,
            emissao: carbon($serviceOrder->created_at)->format('d/m/Y'),
            previsao: carbon($serviceOrder->created_at)->format('d/m/Y'),
            data_producao: carbon($serviceOrder->created_at)->addMinutes($serviceOrder->estimated_duration_in_minutes)->format('d/m/Y'),
            cliente_id: GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->customer_id)->third_party_id,
            servico_id: $thirdPartyServiceType->third_party_id,
            variante_id: ErpFlexProductVariantService::make()
                ->getByProductId($thirdPartyServiceType->third_party_id)
                ->SB2_ID,
            requisicao_prod: $this->structureItems,
            campo_1: $serviceOrder->protocol?->code ?? null
        );

        (new ErpFlexServiceOrderService())->update($serviceOrder, $erpFlexServiceOrderDto);

        if ($userId) {
            success_database_notification($userId, __('service_orders.responses.update_in_erp_flex.success'), true);
        }

        return $serviceOrder;
    }

    private function buildServiceOrderItems(): void
    {
        collect($this->newStepProducts)->each(function (array $item): void {
            /** @var \App\Models\ThirdPartyEquipment|null $thirdPartyEquipment */
            $thirdPartyEquipment = GetThirdPartyEquipmentByEquipmentId::run(IntegrationType::TYPE_ERP_FLEX, $item['product_id']);

            $this->structureItems[] = [
                'item_id' => 0,
                'operacao' => 'I',
                'produto_id' => $thirdPartyEquipment->third_party_id,
                'variante_id' => ErpFlexProductVariantService::make()
                    ->getByProductId($thirdPartyEquipment->third_party_id)
                    ->SB2_ID,
                'documento' => $this->serviceOrder->code,
                'emissao' => now()->format('d/m/Y'),
                'tipo' => 'R',
                'quantidade' => $item['quantity'],
                'natureza_id' => $this->erpFlexParameter->service_order_default_used_product_stock_nature_id,
            ];
        });
    }
}
