<?php

namespace App\Actions\ServiceOrder\Integrations\ErpFlex;

use App\Actions\Customer\Integrations\ErpFlex\CreateCustomerInErpFlex;
use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByCustomerId;
use App\Actions\ThirdPartyEquipment\Queries\GetThirdPartyEquipmentByEquipmentId;
use App\Actions\ThirdPartyServiceOrder\Queries\GetThirdPartyServiceOrderByServiceOrderId;
use App\Actions\ThirdPartyServiceType\Queries\GetThirdPartyServiceTypeByServiceTypeId;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexServiceOrderDto;
use App\Http\Integrations\ErpFlex\Services\ErpFlexProductVariantService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexServiceOrderService;
use App\Models\ErpFlexParameter;
use App\Models\IntegrationType;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepProduct;
use App\Models\ThirdPartyServiceOrder;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class CreateServiceOrderInErpFlex
{
    use AsAction;

    private array $structureItems = [];
    private ErpFlexParameter $erpFlexParameter;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.service_orders.send'));
    }

    public function handle(ServiceOrder $serviceOrder, ?int $userId = null): ServiceOrder
    {
        /** @var \App\Models\ThirdPartyServiceOrder $thirdPartyServiceOrder */
        $thirdPartyServiceOrder = GetThirdPartyServiceOrderByServiceOrderId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->id);

        if ($thirdPartyServiceOrder && $thirdPartyServiceOrder->third_party_id) {
            return $serviceOrder;
        }

        $this->erpFlexParameter = GetErpFlexParameter::run();

        /** @var \App\Models\ThirdPartyServiceType|null $thirdPartyServiceType */
        $thirdPartyServiceType = GetThirdPartyServiceTypeByServiceTypeId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->service_type_id);

        if (!$thirdPartyServiceType) {
            throw new Exception('O tipo de serviço utilizado na ordem de serviço não possui vínculo com o ERPFlex.');
        }

        /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->customer_id);

        if (!$thirdPartyCustomer->third_party_id) {
            CreateCustomerInErpFlex::run($serviceOrder->customer);

            /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
            $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->customer_id);
        }

        $serviceOrder->serviceOrderExecutionChecklistSteps->each(function (ServiceOrderExecutionChecklistStep $step): void {
            $step->serviceOrderExecutionChecklistStepProducts->each(function (ServiceOrderExecutionChecklistStepProduct $stepProduct): void {
                /** @var \App\Models\ThirdPartyEquipment|null $thirdPartyEquipment */
                $thirdPartyEquipment = GetThirdPartyEquipmentByEquipmentId::run(IntegrationType::TYPE_ERP_FLEX, $stepProduct->product_id);

                $this->structureItems[] = [
                    'produto_id' => $thirdPartyEquipment->third_party_id,
                    'variante_id' => ErpFlexProductVariantService::make()
                        ->getByProductId($thirdPartyEquipment->third_party_id)
                        ->SB2_ID,
                    'documento' => $stepProduct->serviceOrderExecutionChecklistStep->serviceOrder->code,
                    'emissao' => carbon($stepProduct->created_at)->format('d/m/Y'),
                    'tipo' => 'R',
                    'quantidade' => $stepProduct->quantity,
                    'natureza_id' => $this->erpFlexParameter->service_order_default_used_product_stock_nature_id,
                ];
            });
        });

        $erpFlexServiceOrderDto = new ErpFlexServiceOrderDto(
            estrutura_serv: 'N',
            documento: $serviceOrder->code,
            quantidade: 1,
            emissao: carbon($serviceOrder->created_at)->format('d/m/Y'),
            previsao: carbon($serviceOrder->created_at)->format('d/m/Y'),
            data_producao: carbon($serviceOrder->created_at)->addMinutes($serviceOrder->estimated_duration_in_minutes)->format('d/m/Y'),
            cliente_id: GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $serviceOrder->customer_id)->third_party_id,
            servico_id: $thirdPartyServiceType->third_party_id,
            variante_id: ErpFlexProductVariantService::make()
                ->getByProductId($thirdPartyServiceType->third_party_id)
                ->SB2_ID,
            requisicao_prod: $this->structureItems,
            campo_1: $serviceOrder->protocol?->code ?? null
        );

        /** @var \App\Models\ThirdPartyServiceOrder $thirdPartyServiceOrder */
        $thirdPartyServiceOrder = ThirdPartyServiceOrder::create([
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'service_order_id' => $serviceOrder->id,
        ]);

        $thirdPartyServiceOrder->update([
            'third_party_id' => (new ErpFlexServiceOrderService())
                ->create($serviceOrder, $thirdPartyServiceOrder, $erpFlexServiceOrderDto)
                ->os_id,
        ]);

        if ($userId) {
            success_database_notification($userId, __('service_orders.responses.create_in_erp_flex.success'), true);
        }

        return $serviceOrder;
    }
}
