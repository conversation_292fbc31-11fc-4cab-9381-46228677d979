<?php

namespace App\Actions\ServiceOrder;

use App\Actions\ServiceOrderChecklist\Queries\GetServiceOrderChecklistByServiceOrderIdAndChecklistId;
use App\Models\ServiceOrder;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class AttachChecklistToServiceOrder
{
    use AsAction;

    public function handle(ServiceOrder $serviceOrder, array $checklistIds): ServiceOrder
    {
        try {
            return DB::transaction(function () use ($serviceOrder, $checklistIds) {
                collect($checklistIds)->each(function (int $checklistId) use ($serviceOrder) {
                    /** @var \App\Models\ServiceOrderChecklist $serviceOrderChecklist */
                    $serviceOrderChecklist = GetServiceOrderChecklistByServiceOrderIdAndChecklistId::run($serviceOrder->id, $checklistId);

                    if ($serviceOrderChecklist) {
                        return;
                    }

                    CreateServiceOrderExecutionChecklistSteps::run(
                        $serviceOrder,
                        $serviceOrder->serviceOrderChecklists()->create(['checklist_id' => $checklistId])
                    );
                });

                return $serviceOrder;
            });
        } catch (Throwable $th) {
            throw $th;
        }
    }
}
