<?php

namespace App\Actions\ServiceOrder;

use App\Models\ServiceOrder;
use App\Models\WorkflowState;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessServiceOrderWorkflowTransition
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\ServiceOrder $serviceOrder
     * @param  \App\Models\WorkflowState $oldWorkflowState
     * @param  \App\Models\WorkflowState $newWorkflowState
     * @return \App\Models\ServiceOrder
     */
    public function handle(ServiceOrder $serviceOrder, WorkflowState $oldWorkflowState, WorkflowState $newWorkflowState): ServiceOrder
    {
        $serviceOrder->serviceOrderWorkflowStateTransitions()->create([
            'origin_workflow_state_id' => $oldWorkflowState->id,
            'destination_workflow_state_id' => $newWorkflowState->id,
            'transitioned_by_user_id' => auth()->id(),
            'transitioned_by_user_name' => auth()->user()->name,
        ]);

        $serviceOrder->update([
            'workflow_id' => $newWorkflowState->workflow_id,
            'workflow_state_id' => $newWorkflowState->id,
        ]);

        return $serviceOrder;
    }
}
