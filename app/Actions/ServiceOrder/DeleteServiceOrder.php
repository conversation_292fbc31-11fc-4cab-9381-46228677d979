<?php

namespace App\Actions\ServiceOrder;

use App\Models\ServiceOrder;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteServiceOrder
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\ServiceOrder $serviceOrder
     * @return void
     */
    public function handle(ServiceOrder $serviceOrder)
    {
        try {
            DB::transaction(function () use ($serviceOrder): void {
                $serviceOrder->serviceOrderAttachments()->delete();
                $serviceOrder->serviceOrderChecklists()->delete();
                $serviceOrder->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
