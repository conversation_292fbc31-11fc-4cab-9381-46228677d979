<?php

namespace App\Actions\ServiceOrder;

use App\Actions\Core\SendEmail;
use App\Models\TenantSettings;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class SendServiceOrdersEmail
{
    use AsAction;

    public function handle(string $encodedRecordsIds, string $toEmail = '<EMAIL>'): void
    {
        /** @var \App\Models\TenantSettings $tenantSettings */
        $tenantSettings = TenantSettings::first();

        $decodedRecordsIds = explode(',', base64_decode($encodedRecordsIds));
        $serviceOrdersData = [];

        try {
            foreach ($decodedRecordsIds as $recordId) {
                $file = GenerateServiceOrderPdf::run(base64_encode($recordId . ';0'), true);
                $downloadFileName = base64_encode($recordId);
                Storage::put('service_orders/' . "$downloadFileName.pdf", $file);

                $serviceOrdersData[] = [
                    'filename' => "service_orders/$downloadFileName.pdf",
                    'download_filename' => $downloadFileName,
                ];
            }

            $ouessTenant = ouess_tenant();

            $message = view('mail.service-orders.service-orders-email', [
                'logoHref' => $ouessTenant->getLogoHref(),
                'logoSrc' => $ouessTenant->getLogoSrc(),
            ])->render();

            SendEmail::run(
                explode(',', $toEmail),
                config('app.name') . ' - ' . (count($serviceOrdersData) > 1 ? 'laudos' : 'laudo') . ' de OS',
                $message,
                $tenantSettings->serviceOrderDefaultSmtpConfiguration,
                $serviceOrdersData
            );
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
