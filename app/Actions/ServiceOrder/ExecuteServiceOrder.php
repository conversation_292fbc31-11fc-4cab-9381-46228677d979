<?php

namespace App\Actions\ServiceOrder;

use App\Actions\ServiceOrderHistory\CreateServiceOrderHistory;
use App\Actions\WorkflowState\Queries\GetWorkflowStatesForWorkflowType;
use App\Enums\ServiceOrderStatusEnum;
use App\Enums\WorkflowTypeEnum;
use App\Models\ServiceOrder;
use App\Models\WorkflowState;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ExecuteServiceOrder
{
    use AsAction;

    public function handle(ServiceOrder $serviceOrder): ServiceOrder
    {
        try {
            /** @var \Illuminate\Support\Collection $workflowStates */
            $workflowStates = GetWorkflowStatesForWorkflowType::run(WorkflowTypeEnum::ServiceOrder->value);

            /** @var \App\Models\WorkflowState $newWorkflowState */
            $newWorkflowState = $workflowStates->filter(fn(WorkflowState $workflowState): bool => $workflowState->characterizes_service_order_execution)->first();

            return DB::transaction(function () use ($serviceOrder, $newWorkflowState): ServiceOrder {
                $oldWorkflowState = $serviceOrder->workflowState;

                $serviceOrder->update([
                    'status' => ServiceOrderStatusEnum::Executing->value,
                    'started_at' => now(),
                ]);

                ProcessServiceOrderWorkflowTransition::run($serviceOrder, $oldWorkflowState, $newWorkflowState);

                CreateServiceOrderExecutionChecklistSteps::run($serviceOrder);

                CreateServiceOrderHistory::run($serviceOrder, 'execute');

                return $serviceOrder;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
