<?php

namespace App\Actions\ServiceOrder;

use App\Models\ServiceOrder;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateServiceOrderPdf
{
    use AsAction;

    public function handle(string $token, bool $toOutput = false): mixed
    {
        $decodedToken = base64_decode($token);

        /** @var \App\Models\ServiceOrder $serviceOrder */
        $serviceOrder = ServiceOrder::find(explode(';', $decodedToken)[0]);

        if (!$serviceOrder) {
            throw new Exception('A ordem de serviço não foi encontrada.');
        }

        $logoSrc = ouess_tenant()->getLogoSrc();

        $layoutData = [
            'logoSrc' => $logoSrc,
            'serviceOrder' => $serviceOrder,
        ];

        if ($toOutput) {
            return Pdf::loadView('app.services.checklists.base_checklist_layout', $layoutData, encoding: 'utf-8')
                ->setOption(['isRemoteEnabled' => true])
                ->output();
        }

        return Pdf::loadView('app.services.checklists.base_checklist_layout', $layoutData, encoding: 'utf-8')
            ->setOption(['isRemoteEnabled' => true])
            ->stream();
    }
}
