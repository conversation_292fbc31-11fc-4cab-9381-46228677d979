<?php

namespace App\Actions\ServiceOrder;

use App\Models\Product;
use App\Models\Quote;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepNeed;
use App\Models\ServiceType;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class SendServiceOrderToQuote
{
    use AsAction;

    private Quote $quote;

    public function handle(ServiceOrder $serviceOrder): ServiceOrder
    {
        $this->quote = $serviceOrder->protocol->quotes->first();

        try {
            return DB::transaction(function () use ($serviceOrder): ServiceOrder {
                $this->quote->quoteItems()->firstOrCreate([
                    'service_order_id' => $serviceOrder->id,
                    'service_type_id' => $serviceOrder->service_type_id,
                    'equipment_id' => $serviceOrder->equipment_id,
                ], [
                    'quantity' => $serviceOrder->quantity,
                    'unit_amount' => $serviceOrder->serviceType->default_amount,
                    'total_amount' => $serviceOrder->serviceType->default_amount * $serviceOrder->quantity,
                ]);

                $serviceOrder->serviceOrderExecutionChecklistSteps->each(function (ServiceOrderExecutionChecklistStep $step): void {
                    $step->serviceOrderExecutionChecklistStepNeeds->each(function (ServiceOrderExecutionChecklistStepNeed $need): void {
                        $this->quote->quoteItems()->firstOrCreate([
                            'service_order_id' => $need->serviceOrderExecutionChecklistStep->service_order_id,
                            'service_order_execution_checklist_step_id' => $need->serviceOrderExecutionChecklistStep->id,
                            'service_order_execution_checklist_step_need_id' => $need->id,
                        ], [
                            'service_type_id' => $need->need_type === ServiceType::class
                                ? $need->need_id
                                : null,
                            'product_id' => $need->need_type === Product::class
                                ? $need->need_id
                                : null,
                            'quantity' => $need->quantity,
                            'unit_amount' => $need->need->default_amount,
                            'total_amount' => $need->quantity * $need->need->default_amount,
                        ]);
                    });
                });

                return $serviceOrder;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
