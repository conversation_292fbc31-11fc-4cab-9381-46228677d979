<?php

namespace App\Actions\ServiceOrder;

use App\Enums\ServiceOrderStatusEnum;
use App\Models\ServiceOrder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CancelServiceOrder
{
    use AsAction;

    public function handle(ServiceOrder $serviceOrder, ?string $cancellationAdditionalInfo = null): ServiceOrder
    {
        try {
            $serviceOrder->update([
                'status' => ServiceOrderStatusEnum::Cancelled->value,
                'cancelled_at' => now(),
                'cancellation_additional_info' => $cancellationAdditionalInfo,
            ]);

            return $serviceOrder;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
