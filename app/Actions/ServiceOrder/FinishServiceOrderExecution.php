<?php

namespace App\Actions\ServiceOrder;

use App\Enums\ServiceOrderStatusEnum;
use App\Models\ServiceOrder;
use Lorisleiva\Actions\Concerns\AsAction;

class FinishServiceOrderExecution
{
    use AsAction;

    public function handle(ServiceOrder $serviceOrder, array $data): ServiceOrder
    {
        unset($data['service_order_execution_checklist_step_products']);
        unset($data['service_order_execution_checklist_step_needs']);

        foreach ($data as $key => $value) {
            $serviceOrder->serviceOrderExecutionChecklistSteps()
                ->where('sequence', substr($key, 6))
                ->first()
                ->update(['collected_value' => $value]);
        }

        $serviceOrder->update([
            'status' => ServiceOrderStatusEnum::Finished->value,
            'finished_at' => now(),
        ]);

        return $serviceOrder;
    }
}
