<?php

namespace App\Actions\ServiceOrder;

use App\Actions\ServiceOrder\Integrations\ErpFlex\CreateServiceOrderInErpFlex;
use App\Models\ServiceOrder;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateServiceOrder
{
    use AsAction;

    public function handle(array $data): ServiceOrder
    {
        try {
            /** @var \App\Models\ServiceOrder $serviceOrder */
            $serviceOrder = DB::transaction(function () use ($data): ServiceOrder {
                $checklistIds = $data['checklist_ids'];

                unset($data['checklist_ids']);

                /** @var \App\Models\ServiceOrder $serviceOrder */
                $serviceOrder = ServiceOrder::create($data);

                foreach ($checklistIds as $checklistId) {
                    $serviceOrder->serviceOrderChecklists()->create(['checklist_id' => $checklistId]);
                }

                return $serviceOrder;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }

        CreateServiceOrderInErpFlex::dispatch($serviceOrder, auth()->id());

        return $serviceOrder;
    }
}
