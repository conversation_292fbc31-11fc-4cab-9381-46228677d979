<?php

namespace App\Actions\Customer\Queries;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomersByNameTradingNameOrTaxIdentificationNumber
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $search
     * @return \Illuminate\Support\Collection
     */
    public function handle(string $search): Collection
    {
        $taxIdNumber = Str::remove(['.', '/', '-'], $search);

        return Customer::query()
            ->select(['id', 'name'])
            ->where(function (Builder $query) use ($search, $taxIdNumber): Builder {
                return $query
                    ->where('name', 'like', "%$search%")
                    ->orWhere('trading_name', 'like', "%$search%")
                    ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                        return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                    });
            })
            ->get();
    }
}
