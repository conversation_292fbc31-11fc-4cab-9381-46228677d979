<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteCustomer
{
    use AsAction;

    public function handle(Customer $customer): void
    {
        try {
            DB::transaction(function () use ($customer): void {
                $customer->customerAddresses()->delete();
                $customer->customerContacts()->delete();
                $customer->customerEquipment()->delete();
                $customer->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
