<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use Lorisleiva\Actions\Concerns\AsAction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class CreateCustomer
{
    use AsAction;

    public function handle(array $data): Customer
    {
        try {
            return DB::transaction(function () use ($data): Customer {
                /** @var \App\Models\Customer $customer */
                $customer = Customer::create($data);

                $customer->user()->create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => Str::random(),
                ]);

                return $customer;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
