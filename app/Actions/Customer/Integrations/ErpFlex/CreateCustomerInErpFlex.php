<?php

namespace App\Actions\Customer\Integrations\ErpFlex;

use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByCustomerId;
use App\Enums\CustomerAddressTypeEnum;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use App\Http\Integrations\ErpFlex\Services\ErpFlexCustomerService;
use App\Models\Customer;
use App\Models\IntegrationType;
use App\Models\ThirdPartyCustomer;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class CreateCustomerInErpFlex
{
    use AsAction;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.customers.send'));
    }

    public function handle(Customer $customer): Customer
    {
        /** @var \App\Models\ThirdPartyCustomer $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $customer->id);

        if ($thirdPartyCustomer && $thirdPartyCustomer->third_party_id) {
            return $customer;
        }

        if ($customer->customerAddresses->count() === 0) {
            throw new Exception('Não é possível integrar clientes com o ERPFlex sem endereços. Cadastre ao menos um endereço para continuar.');
        }

        /** @var \App\Models\CustomerAddress|null $customerAddress */
        $customerAddress = $customer->customerAddresses()
            ->where('type', CustomerAddressTypeEnum::Contact->value)
            ->first();

        if (!$customerAddress) {
            /** @var \App\Models\CustomerAddress|null $customerAddress */
            $customerAddress = $customer->customerAddresses()
                ->where('type', CustomerAddressTypeEnum::Billing->value)
                ->first();
        }

        $erpFlexCustomer = new ErpFlexCustomer(
            nome: $customer->name ?? null,
            fantasia: $customer->trading_name ?? null,
            cpf_cnpj: $customer->tax_id_number ?? null,
            inscricao_estadual: $customer->state_registration ?? null,
            cep: $customerAddress->zipcode,
            endereco: $customerAddress->address,
            numero: $customerAddress->number,
            complemento: $customerAddress->complement,
            bairro: $customerAddress->district,
            municipio: $customerAddress->city,
            estado: $customerAddress->state
        );

        /** @var \App\Models\ThirdPartyCustomer $thirdPartyCustomer */
        $thirdPartyCustomer = ThirdPartyCustomer::create([
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'customer_id' => $customer->id,
        ]);

        $tenantErpFlexApiCredentials = get_erp_flex_api_credentials();

        $username = $tenantErpFlexApiCredentials['username'];
        $password = $tenantErpFlexApiCredentials['password'];

        if (isset($tenantErpFlexApiCredentials['consolidator'])) {
            $username = $tenantErpFlexApiCredentials['consolidator']['api']['username'];
            $password = $tenantErpFlexApiCredentials['consolidator']['api']['password'];
        }

        $erpFlexCustomerService = new ErpFlexCustomerService($username, $password);

        $thirdPartyCustomer->update([
            'third_party_id' => $erpFlexCustomerService->create($customer, $thirdPartyCustomer, $erpFlexCustomer)->id
        ]);

        return $customer;
    }
}
