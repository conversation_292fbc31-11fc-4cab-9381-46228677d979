<?php

namespace App\Actions\Customer\Integrations;

use App\Actions\IntegrationSetting\Queries\GetActiveIntegrationSettings;
use App\Actions\Customer\Integrations\ErpFlex\GetCustomersFromErpFlex;
use App\Models\IntegrationType;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyCustomers
{
    use AsAction;

    public string $commandSignature = 'ouess:get-third-party-customers {tenant?}';
    public string $commandDescription = 'Get all customers from the integrations.';

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        $tenants = Tenant::query()
            ->when($command->argument('tenant'), function (Builder $query) use ($command): Builder {
                return $query->where('id', $command->argument('tenant'));
            })
            ->get();

        tenancy()->runForMultiple(
            $tenants,
            fn() => $this->handle()
        );
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        $integrationSettings = GetActiveIntegrationSettings::run();

        foreach ($integrationSettings as $integrationSetting) {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            switch ($integrationSetting->integration_type_id) {
                case IntegrationType::TYPE_ERP_FLEX:
                    GetCustomersFromErpFlex::dispatch($integrationSetting, $force, auth()->check() ? auth()->id() : null, null);
                    break;
                default:
                    break;
            }
        }
    }
}
