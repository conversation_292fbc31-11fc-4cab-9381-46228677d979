<?php

namespace App\Actions\ServiceOrderExecutionChecklistStepProduct;

use App\Actions\StockMovement\CreateStockMovement;
use App\Models\Product;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateServiceOrderExecutionChecklistStepProduct
{
    use AsAction;

    public function handle(ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep, array $data): ServiceOrderExecutionChecklistStepProduct
    {
        /** @var \App\Models\StockMovement $stockMovement */
        $stockMovement = CreateStockMovement::run([
            'item_id' => $data['product_id'],
            'item_type' => Product::class,
            'quantity' => $data['quantity'],
            'origin_warehouse_id' => $data['origin_warehouse_id'],
            'destination_warehouse_id' => null,
        ]);

        return $serviceOrderExecutionChecklistStep->serviceOrderExecutionChecklistStepProducts()->create([
            'product_id' => $data['product_id'],
            'origin_warehouse_id' => $data['origin_warehouse_id'],
            'stock_movement_id' => $stockMovement->id,
            'quantity' => $data['quantity'],
        ]);
    }
}
