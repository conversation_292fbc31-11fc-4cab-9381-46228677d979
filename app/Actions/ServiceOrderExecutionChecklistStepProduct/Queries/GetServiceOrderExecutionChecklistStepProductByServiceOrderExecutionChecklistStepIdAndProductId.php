<?php

namespace App\Actions\ServiceOrderExecutionChecklistStepProduct\Queries;

use App\Models\ServiceOrderExecutionChecklistStepProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetServiceOrderExecutionChecklistStepProductByServiceOrderExecutionChecklistStepIdAndProductId
{
    use AsAction;

    public function handle(int $serviceOrderExecutionChecklistStepId, int $productId): ?ServiceOrderExecutionChecklistStepProduct
    {
        return ServiceOrderExecutionChecklistStepProduct::query()
            ->where('service_order_execution_checklist_step_id', $serviceOrderExecutionChecklistStepId)
            ->where('product_id', $productId)
            ->first();
    }
}
