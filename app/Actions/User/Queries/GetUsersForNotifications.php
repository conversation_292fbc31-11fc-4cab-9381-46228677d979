<?php

namespace App\Actions\User\Queries;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetUsersForNotifications
{
    use AsAction;

    public function handle(array $notificationTypes): Collection
    {
        return User::query()
            ->whereHas('notificationSetting', function (Builder $query) use ($notificationTypes): Builder {
                return $query->where(function (Builder $query) use ($notificationTypes): Builder {
                    foreach ($notificationTypes as $notificationType) {
                        $query->orWhere($notificationType, true);
                    }

                    return $query;
                });
            })
            ->get();
    }
}
