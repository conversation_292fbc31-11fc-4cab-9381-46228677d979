<?php

namespace App\Actions\User;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteUser
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\User $user
     * @return void
     */
    public function handle(User $user)
    {
        try {
            $user->delete();
        } catch (Throwable $th) {
            Log::error($th);
            throw $th;
        }
    }
}
