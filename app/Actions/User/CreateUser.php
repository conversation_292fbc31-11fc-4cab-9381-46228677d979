<?php

namespace App\Actions\User;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\Permission\Models\Role;
use Throwable;

class CreateUser
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\User
     */
    public function handle(array $data): User
    {
        try {
            $password = !isset($data['password']) || is_null($data['password']) || $data['password'] === ''
                ? Str::random()
                : $data['password'];

            /** @var \App\Models\User $user */
            $user = User::create(array_merge($data, [
                'password' => bcrypt($password)
            ]));

            $user->syncRoles(Role::find($data['role'])->name);

            return $user;
        } catch (Throwable $th) {
            Log::error($th);
            throw $th;
        }
    }
}
