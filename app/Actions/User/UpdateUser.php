<?php

namespace App\Actions\User;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Spatie\Permission\Models\Role;
use Throwable;

class UpdateUser
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\User $user
     * @param  array $data
     * @return \App\Models\User
     */
    public function handle(User $user, array $data): User
    {
        try {
            if (!is_null($data['password'])) {
                $data['password'] = bcrypt($data['password']);
            } else {
                unset($data['password']);
            }

            $user->update($data);
            $user->syncRoles(Role::find($data['role'])->name);

            return $user;
        } catch (Throwable $th) {
            Log::error($th);
            throw $th;
        }
    }
}
