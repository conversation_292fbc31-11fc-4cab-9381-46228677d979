<?php

namespace App\Actions;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateBaseAdminUser
{
    use AsAction;

    public string $commandSignature = 'p4sell:create-base-admin-user {name} {email} {password}';
    public string $commandDescription = 'Create the base admin user.';

    public function asCommand(Command $command): void
    {
        $this->handle([
            'name' => $command->argument('name'),
            'email' => $command->argument('email'),
            'password' => $command->argument('password'),
        ]);

        $command->info('Done!');
    }

    public function handle(array $data): User
    {
        return User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ]);
    }
}
