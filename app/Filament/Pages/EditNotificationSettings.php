<?php

namespace App\Filament\Pages;

use App\Models\NotificationSetting;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class EditNotificationSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-bell';
    protected static ?string $title = 'Preferências de notificação';
    protected static ?string $slug = 'edit-notification-settings';
    protected static string $view = 'filament.pages.edit-notification-settings';

    public NotificationSetting $notificationSetting;

    public ?bool $contract_termination = false;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function mount(): void
    {
        $this->notificationSetting = NotificationSetting::firstOrCreate([
            'user_id' => auth()->id(),
        ]);

        $this->contract_termination = $this->notificationSetting->contract_termination;
    }

    protected function getFormSchema(): array
    {
        return [
            Grid::make(1)->schema([
                Tabs::make()->tabs([
                    Tab::make('contratos')
                        ->label('Contratos')
                        ->schema([
                            Grid::make(4)->schema([
                                Toggle::make('contract_termination')
                                    ->label(__('notification_settings.forms.fields.contract_termination'))
                                    ->default(false)
                                    ->reactive(),
                            ]),
                        ]),
                ]),
            ]),
        ];
    }

    public function updateNotificationSettings()
    {
        $this->notificationSetting->update([
            'contract_termination' => $this->contract_termination,
        ]);

        success_notification(__('notification_settings.responses.update.success'))->send();

        return redirect()->route('filament.app.pages.dashboard');
    }
}
