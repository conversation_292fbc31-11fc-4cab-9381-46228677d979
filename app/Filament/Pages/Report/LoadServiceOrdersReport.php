<?php

namespace App\Filament\Pages\Report;

use App\Actions\Equipment\Queries\GetEquipmentBySerialNumberOrName;
use App\Actions\ServiceType\Queries\GetServiceTypeByName;
use App\Actions\User\Queries\GetUsers;
use App\Core\Filament\Form\Sections\ReportFormatSection;
use App\Enums\ServiceOrderStatusEnum;
use App\Models\Customer;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Set;
use Filament\Pages\Page;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class LoadServiceOrdersReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Ordens de serviço';
    protected static ?string $slug = 'service-orders-report';
    protected static string $view = 'filament.pages.report.load-service-orders-report';

    public ?string $protocolCode = '';
    public ?string $protocolInvoiceNumber = '';
    public ?string $customerId = '';
    public ?string $customerTradingName = '';
    public ?string $customerTaxIdNumber = '';
    public ?int $equipmentId = null;
    public ?int $serviceTypeId = null;
    public ?int $employeeId = null;
    public ?string $status = '';
    public ?string $dateFrom;
    public ?string $dateTo;
    public ?string $format = null;

    public function mount(): void
    {
        $this->dateFrom = now()->startOfMonth()->format('Y-m-d');
        $this->dateTo = now()->endOfMonth()->format('Y-m-d');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Filtros')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('dateFrom')
                            ->type('date')
                            ->label('Data de')
                            ->required(),
                        TextInput::make('dateTo')
                            ->type('date')
                            ->label('Data até')
                            ->required(),
                        TextInput::make('protocolCode')
                            ->label('Código do protocolo'),
                        TextInput::make('protocolInvoiceNumber')
                            ->label('Número da nota fiscal'),
                    ]),
                    Grid::make(4)->schema([
                        Select::make('customerId')
                            ->label(__('customers.forms.fields.name'))
                            ->placeholder('Digite a razão social, fantasia ou o CPF/CNPJ')
                            ->columnSpan(2)
                            ->reactive()
                            ->searchable()
                            ->getSearchResultsUsing(function (?string $search) {
                                $taxIdNumber = Str::remove(['-', '.', '/'], $search);

                                return Customer::query()
                                    ->where('name', 'like', "%$search%")
                                    ->orWhere('trading_name', 'like', "%$search%")
                                    ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                        return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                    })
                                    ->get()
                                    ->map(fn(Customer $customer) => [
                                        'id' => $customer->id,
                                        'name' => "$customer->name | $customer->trading_name"
                                    ])
                                    ->pluck('name', 'id');
                            })
                            ->afterStateUpdated(function (?string $state, Set $set): void {
                                $this->handleCustomerDependantFields($state, $set);
                            }),
                        TextInput::make('customerTradingName')
                            ->disabled()
                            ->label(__('customers.forms.fields.trading_name')),
                        TextInput::make('customerTaxIdNumber')
                            ->disabled()
                            ->label(__('customers.forms.fields.tax_id_number')),
                    ]),
                    Grid::make(4)->schema([
                        Select::make('equipmentId')
                            ->label(__('service_orders.forms.fields.equipment_id'))
                            ->searchable()
                            ->getSearchResultsUsing(fn(?string $search): array => GetEquipmentBySerialNumberOrName::run($search, true)->pluck('name', 'id')->toArray()),
                        Select::make('serviceTypeId')
                            ->label('Tipo de serviço')
                            ->searchable()
                            ->getSearchResultsUsing(fn(?string $search): array => GetServiceTypeByName::run($search, true)->pluck('name', 'id')->toArray()),
                        Select::make('employeeId')
                            ->label('Funcionário')
                            ->options(GetUsers::run()->pluck('name', 'id')),
                        Select::make('status')
                            ->label('Status')
                            ->options(array_merge(['' => 'Todos'], ServiceOrderStatusEnum::getTranslated()))
                            ->selectablePlaceholder(false),
                    ]),
                ]),
            ReportFormatSection::build()
        ];
    }

    protected function handleCustomerDependantFields(?string $customerId, Set $set): ?Customer
    {
        if (is_null($customerId)) {
            $set('customerTradingName', '');
            $set('customerTaxIdNumber', '');
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customerTradingName', $customer?->trading_name ?? '');
        $set('customerTaxIdNumber', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }

    public function generate()
    {
        $tokenString = $this->dateFrom
            . ';' . $this->dateTo
            . ';' . $this->protocolCode
            . ';' . $this->protocolInvoiceNumber
            . ';' . $this->customerId
            . ';' . $this->equipmentId
            . ';' . $this->serviceTypeId
            . ';' . $this->employeeId
            . ';' . $this->status
            . ';' . $this->format;

        $this->dispatch('runReport', url: route('reports.generate_general_service_orders_report', [
            'token' => base64_encode($tokenString)
        ]));
    }

    public function cancel(): mixed
    {
        return redirect()->route('filament.app.pages.list-reports');
    }
}
