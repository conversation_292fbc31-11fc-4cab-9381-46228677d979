<?php

namespace App\Filament\Pages;

use App\Actions\SmtpConfiguration\Queries\GetSmtpConfigurations;
use App\Filament\Clusters\Administration;
use App\Models\ErpFlexParameter;
use App\Models\TenantSettings;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class EditTenantSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $cluster = Administration::class;
    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?int $navigationSort = 5;
    protected static ?string $title = 'Parâmetros';
    protected static ?string $slug = 'edit-tenant-settings';
    protected static string $view = 'filament.pages.edit-tenant-settings';

    public TenantSettings $tenantSettings;
    public ?ErpFlexParameter $erpFlexParameter;

    public ?int $service_order_default_smtp_configuration_id = null;
    public bool $service_order_replicate_needs_to_products = false;
    public bool $protocol_create_quote_automatically = true;
    public ?string $erp_flex_equipment_serial_number_field = null;
    public ?int $erp_flex_service_order_default_used_product_stock_nature_id = null;
    public ?int $erp_flex_contract_item_default_nature_id = null;
    public ?int $erp_flex_quote_default_salesman_id = null;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole('Administrador');
    }

    public function mount(): void
    {
        $this->tenantSettings = TenantSettings::firstOrCreate();
        $this->erpFlexParameter = ErpFlexParameter::firstOrCreate();

        $this->service_order_default_smtp_configuration_id = $this->tenantSettings->service_order_default_smtp_configuration_id;
        $this->service_order_replicate_needs_to_products = $this->tenantSettings->service_order_replicate_needs_to_products;
        $this->protocol_create_quote_automatically = $this->tenantSettings->protocol_create_quote_automatically;
        $this->erp_flex_equipment_serial_number_field = $this->erpFlexParameter->equipment_serial_number_field;
        $this->erp_flex_service_order_default_used_product_stock_nature_id = $this->erpFlexParameter->service_order_default_used_product_stock_nature_id;
        $this->erp_flex_contract_item_default_nature_id = $this->erpFlexParameter->contract_item_default_nature_id;
        $this->erp_flex_quote_default_salesman_id = $this->erpFlexParameter->quote_default_salesman_id;
    }

    protected function getFormSchema(): array
    {
        return [
            Tabs::make()->tabs([
                Tab::make('service_order_tab')
                    ->label('Ordens de serviço')
                    ->schema([
                        Grid::make(1)->schema([
                            Toggle::make('service_order_replicate_needs_to_products')
                                ->label(__('tenant_settings.forms.fields.service_order_replicate_needs_to_products'))
                                ->default(false),
                        ]),
                        Grid::make(4)->schema([
                            Select::make('service_order_default_smtp_configuration_id')
                                ->label(__('tenant_settings.forms.fields.service_order_default_smtp_configuration_id'))
                                ->columnSpan(2)
                                ->options(GetSmtpConfigurations::run()->pluck('username', 'id')->toArray()),
                        ]),
                    ]),
                Tab::make('protocol_tab')
                    ->label('Protocolos')
                    ->schema([
                        Grid::make(4)->schema([
                            Toggle::make('protocol_create_quote_automatically')
                                ->label(__('tenant_settings.forms.fields.protocol_create_quote_automatically'))
                                ->default(true),
                        ]),
                    ]),
                Tab::make('erp_flex_tab')
                    ->label('ERPFlex')
                    ->schema([
                        Grid::make(4)->schema([
                            TextInput::make('erp_flex_equipment_serial_number_field')
                                ->label(__('tenant_settings.forms.fields.erp_flex_equipment_serial_number_field')),
                            TextInput::make('erp_flex_service_order_default_used_product_stock_nature_id')
                                ->label(__('tenant_settings.forms.fields.erp_flex_service_order_default_used_product_stock_nature_id'))
                                ->numeric(),
                            TextInput::make('erp_flex_contract_item_default_nature_id')
                                ->label(__('tenant_settings.forms.fields.erp_flex_contract_item_default_nature_id'))
                                ->numeric(),
                            TextInput::make('erp_flex_quote_default_salesman_id')
                                ->label(__('tenant_settings.forms.fields.erp_flex_quote_default_salesman_id'))
                                ->numeric(),
                        ]),
                    ]),
            ]),
        ];
    }

    public function updateTenantSettings()
    {
        $this->tenantSettings->update([
            'service_order_default_smtp_configuration_id' => $this->service_order_default_smtp_configuration_id,
            'service_order_replicate_needs_to_products' => $this->service_order_replicate_needs_to_products,
            'protocol_create_quote_automatically' => $this->protocol_create_quote_automatically,
        ]);

        $this->erpFlexParameter->update([
            'equipment_serial_number_field' => $this->erp_flex_equipment_serial_number_field,
            'service_order_default_used_product_stock_nature_id' => $this->erp_flex_service_order_default_used_product_stock_nature_id,
            'contract_item_default_nature_id' => $this->erp_flex_contract_item_default_nature_id,
            'quote_default_salesman_id' => $this->erp_flex_quote_default_salesman_id,
        ]);

        success_notification(__('tenant_settings.responses.update.success'))->send();

        return redirect()->route('filament.app.pages.dashboard');
    }
}
