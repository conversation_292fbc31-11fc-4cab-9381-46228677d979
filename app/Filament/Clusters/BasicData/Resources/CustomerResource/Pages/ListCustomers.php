<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\Pages;

use App\Actions\Customer\Integrations\GetThirdPartyCustomers;
use App\Filament\Clusters\BasicData\Resources\CustomerResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ListRecords;
use Throwable;

class ListCustomers extends ListRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-service-types')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyCustomers::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.basic-data.resources.customers.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os clientes neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
        ];
    }
}
