<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\Pages;

use App\Filament\Clusters\BasicData\Resources\CustomerResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateCustomer extends CreateRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('customers.responses.create.success'))->send();
    }
}
