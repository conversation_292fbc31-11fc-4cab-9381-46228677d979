<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\Pages;

use App\Filament\Clusters\BasicData\Resources\CustomerResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateCustomer extends CreateRecord
{
    protected static string $resource = CustomerResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        return \App\Actions\Customer\CreateCustomer::run($data);
    }

    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('customers.responses.create.success'))->send();
    }
}
