<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\Pages;

use App\Actions\Customer\DeleteCustomer;
use App\Actions\Customer\Integrations\ErpFlex\CreateCustomerInErpFlex;
use App\Filament\Clusters\BasicData\Resources\CustomerResource;
use App\Models\IntegrationType;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Throwable;

class EditCustomer extends EditRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('send_to_erp_flex')
                ->label('Enviar para o ERPFlex')
                ->icon('heroicon-s-arrows-right-left')
                ->color('gray')
                ->visible(function (): bool {
                    if (!$this->record->thirdPartyCustomers || $this->record->thirdPartyCustomers->count() === 0) {
                        return true;
                    }

                    $unlinkedCustomerExists = $this->record->thirdPartyCustomers()
                        ->where('integration_type_id', IntegrationType::TYPE_ERP_FLEX)
                        ->whereNull('third_party_id')
                        ->exists();

                    return $unlinkedCustomerExists;
                })
                ->modalHeading(fn(): string => "Enviar cliente #{$this->record->id} para o ERPFlex")
                ->action(function (): void {
                    try {
                        CreateCustomerInErpFlex::run($this->record);
                        success_notification(__('customers.responses.create_in_erp_flex.success'))->send();
                    } catch (Throwable $th) {
                        error_notification(__('customers.responses.create_in_erp_flex.error'))->send();
                        error($th);
                    }
                })
                ->successNotification(success_notification(__('customers.responses.approve.success')))
                ->requiresConfirmation(),
            DeleteAction::make()
                ->using(function () {
                    try {
                        DeleteCustomer::run($this->record);
                        return redirect()->route('filament.app.basic-data.resources.customers.index');
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                })
                ->successNotification(success_notification(__('customers.responses.delete.success'))),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return EditCustomer::run($record, $data);
    }

    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('customers.responses.update.success'))->send();
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }
}
