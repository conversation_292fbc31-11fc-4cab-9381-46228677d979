<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers;

use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\CustomerAddressTypeEnum;
use App\Http\Integrations\ViaCep\Services\ViaCepZipcodeService;
use App\Models\City;
use App\Models\State;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Throwable;

class CustomerAddressesRelationManager extends RelationManager
{
    protected static string $relationship = 'customerAddresses';
    protected static ?string $title = 'Endereços';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(4)->schema([
                    TextInput::make('zipcode')
                        ->label(__('customer_addresses.forms.fields.zipcode'))
                        ->mask('99999-999')
                        ->columnSpan(1)
                        ->lazy()
                        ->afterStateUpdated(function (?string $state, Set $set) {
                            if (is_null($state) || trim($state) === '') {
                                return;
                            }

                            try {
                                $fullAddress = ViaCepZipcodeService::make()->getZipcodeDetails($state);
                            } catch (Throwable $th) {
                                error($th);
                                error_notification('Não foi possível obter os dados automáticos do CEP.')->send();

                                $set('address', null);
                                $set('district', null);
                                $set('city_id', null);
                                $set('city', null);
                                $set('state_id', null);
                                $set('state', null);

                                return;
                            }

                            if (isset($fullAddress->erro) && $fullAddress->erro) {
                                $set('address', null);
                                $set('district', null);
                                $set('city_id', null);
                                $set('city', null);
                                $set('state_id', null);
                                $set('state', null);
                                return;
                            }

                            /** @var \App\Models\City $city */
                            $city = City::find($fullAddress->ibge);

                            /** @var \App\Models\State $addressState */
                            $addressState = State::query()
                                ->where('abbreviation', $fullAddress->uf)
                                ->first();

                            $set('address', $fullAddress->logradouro);
                            $set('district', $fullAddress->bairro);
                            $set('city_id', $city->id);
                            $set('city', $city->name);
                            $set('state_id', $addressState->id);
                            $set('state', $addressState->abbreviation);
                        }),
                    Select::make('type')
                        ->label(__('customer_addresses.forms.fields.type'))
                        ->required()
                        ->columnStart(4)
                        ->options(CustomerAddressTypeEnum::getTranslated()),
                ]),
                Grid::make(4)->schema([
                    TextInput::make('address')
                        ->label(__('customer_addresses.forms.fields.address'))
                        ->columnSpan(2),
                    TextInput::make('number')
                        ->label(__('customer_addresses.forms.fields.number'))
                        ->columnSpan(1),
                    TextInput::make('complement')
                        ->label(__('customer_addresses.forms.fields.complement'))
                        ->columnSpan(1),
                ]),
                Grid::make(4)->schema([
                    TextInput::make('district')
                        ->label(__('customer_addresses.forms.fields.district'))
                        ->columnSpan(2),
                    Hidden::make('city_id'),
                    TextInput::make('city')
                        ->label(__('customer_addresses.forms.fields.city'))
                        ->readOnly()
                        ->columnSpan(1),
                    Hidden::make('state_id'),
                    TextInput::make('state')
                        ->label(__('customer_addresses.forms.fields.state'))
                        ->readOnly()
                        ->columnSpan(1),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('address')
            ->modelLabel('endereço')
            ->columns([
                Tables\Columns\TextColumn::make('address')
                    ->label(__('customer_addresses.forms.fields.address')),
                Tables\Columns\TextColumn::make('number')
                    ->label(__('customer_addresses.forms.fields.number')),
                Tables\Columns\TextColumn::make('district')
                    ->label(__('customer_addresses.forms.fields.district')),
                Tables\Columns\TextColumn::make('city')
                    ->label(__('customer_addresses.forms.fields.city')),
                Tables\Columns\TextColumn::make('state')
                    ->label(__('customer_addresses.forms.fields.state')),
            ])
            ->filters([
                TableTextFilter::buildLike('customer_addresses', 'address'),
                TableTextFilter::buildLike('customer_addresses', 'city'),
                TableTextFilter::buildLike('customer_addresses', 'state'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->successNotification(success_notification(__('customer_addresses.responses.create.success'))),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('customer_addresses.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('customer_addresses.responses.delete.success'))),
                ]),
            ]);
    }
}
