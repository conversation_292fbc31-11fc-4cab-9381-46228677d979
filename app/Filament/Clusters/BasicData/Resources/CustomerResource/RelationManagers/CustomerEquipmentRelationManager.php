<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers;

use App\Actions\EquipmentCustomer\SetEquipmentCustomerTerm;
use App\Core\Filament\Filters\TableTextFilter;
use App\Models\EquipmentCustomer;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Throwable;

class CustomerEquipmentRelationManager extends RelationManager
{
    protected static string $relationship = 'customerEquipment';
    protected static ?string $title = 'Equipamentos';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(2)->schema([
                TextInput::make('serial_number')
                    ->label(__('equipment.forms.fields.serial_number'))
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): ?string => $equipmentCustomer->equipment->serial_number),
                TextInput::make('equipment_type_id')
                    ->label(__('equipment.forms.fields.equipment_type_id'))
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): ?string => $equipmentCustomer->equipment->equipmentType->name),
            ]),
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('equipment.forms.fields.name'))
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): ?string => $equipmentCustomer->equipment->name),
            ]),
            Grid::make(1)->schema([
                Textarea::make('additional_info')
                    ->label(__('equipment.forms.fields.additional_info'))
                    ->rows(3)
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): ?string => $equipmentCustomer->equipment->additional_info),
            ]),
            Grid::make(2)->schema([
                TextInput::make('manufactured_at')
                    ->label(__('equipment.forms.fields.manufactured_at'))
                    ->type('date')
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): ?string => $equipmentCustomer->equipment->manufactured_at ? carbon($equipmentCustomer->equipment->manufactured_at)->format('Y-m-d') : null),
                TextInput::make('warranty_expires_at')
                    ->label(__('equipment.forms.fields.warranty_expires_at'))
                    ->type('date')
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): ?string => $equipmentCustomer->equipment->warranty_expires_at ? carbon($equipmentCustomer->equipment->warranty_expires_at)->format('Y-m-d') : null),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('equipment.name')
            ->columns([
                Tables\Columns\TextColumn::make('equipment.serial_number')
                    ->label(__('equipment.forms.fields.serial_number')),
                Tables\Columns\TextColumn::make('equipment.name')
                    ->label(__('equipment.forms.fields.name')),
                Tables\Columns\TextColumn::make('equipment.equipmentType.name')
                    ->label(__('equipment.forms.fields.equipment_type_id')),
                Tables\Columns\TextColumn::make('equipment.code')
                    ->label(__('equipment.forms.fields.code')),
                Tables\Columns\TextColumn::make('term_started_at')
                    ->label(__('equipment_customers.forms.fields.term_started_at'))
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): string => format_date($equipmentCustomer->term_started_at)),
                Tables\Columns\TextColumn::make('term_ended_at')
                    ->label(__('equipment_customers.forms.fields.term_ended_at'))
                    ->formatStateUsing(fn(EquipmentCustomer $equipmentCustomer): string => format_date($equipmentCustomer->term_ended_at)),
            ])
            ->filters([
                TableTextFilter::buildRelation('equipment', 'serial_number', 'equipment', 'serial_number'),
                TableTextFilter::buildRelation('equipment', 'name', 'equipment', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\Action::make('set_term')
                        ->label('Atribuir vigência')
                        ->icon('heroicon-o-calendar-days')
                        ->color('primary')
                        ->visible(fn(EquipmentCustomer $equipmentCustomer): bool => is_null($equipmentCustomer->term_started_at))
                        ->form([
                            Grid::make(2)->schema([
                                TextInput::make('term_started_at')
                                    ->label(__('equipment_customers.forms.fields.term_started_at'))
                                    ->type('date')
                                    ->required(),
                                TextInput::make('term_ended_at')
                                    ->label(__('equipment_customers.forms.fields.term_ended_at'))
                                    ->type('date')
                                    ->required(),
                            ]),
                        ])
                        ->action(function (EquipmentCustomer $equipmentCustomer, array $data): void {
                            try {
                                SetEquipmentCustomerTerm::run($equipmentCustomer, carbon($data['term_started_at']), carbon($data['term_ended_at']));
                                success_notification('A vigência foi atribuída ao equipamento.')->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage());
                            }
                        }),
                    Tables\Actions\Action::make('set_term_end')
                        ->label('Finalizar vigência')
                        ->icon('heroicon-o-calendar')
                        ->color('primary')
                        ->visible(fn(EquipmentCustomer $equipmentCustomer): bool => !is_null($equipmentCustomer->term_started_at))
                        ->form([
                            Grid::make(1)->schema([
                                TextInput::make('term_ended_at')
                                    ->label(__('equipment_customers.forms.fields.term_ended_at'))
                                    ->type('date')
                                    ->required(),
                            ]),
                        ])
                        ->action(function (EquipmentCustomer $equipmentCustomer, array $data): void {
                            try {
                                SetEquipmentCustomerTerm::run($equipmentCustomer, carbon($equipmentCustomer->term_started_at), carbon($data['term_ended_at']));
                                success_notification('A vigência do equipamento no cliente foi finalizada.')->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage());
                            }
                        }),
                ]),
            ]);
    }
}
