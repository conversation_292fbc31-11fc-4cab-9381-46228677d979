<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers;

use App\Core\Filament\Filters\TableActiveFilter;
use App\Core\Filament\Filters\TableBooleanFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\CustomerAddressTypeEnum;
use App\Enums\CustomerContactTypeEnum;
use App\Http\Integrations\ViaCep\Services\ViaCepZipcodeService;
use App\Models\City;
use App\Models\State;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Throwable;

class CustomerContactsRelationManager extends RelationManager
{
    protected static string $relationship = 'customerContacts';
    protected static ?string $title = 'Contatos';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(4)->schema([
                    Toggle::make('active')
                        ->label(__('customer_contacts.forms.fields.active')),
                    Toggle::make('main')
                        ->label(__('customer_contacts.forms.fields.main')),
                ]),
                Grid::make(4)->schema([
                    Select::make('type')
                        ->label(__('customer_contacts.forms.fields.type'))
                        ->required()
                        ->options(CustomerContactTypeEnum::getTranslated())
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 1,
                            'xl' => 1,
                        ]),
                    TextInput::make('name')
                        ->label(__('customer_contacts.forms.fields.name'))
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 3,
                            'xl' => 3,
                        ]),
                ]),
                Grid::make(4)->schema([
                    TextInput::make('email')
                        ->label(__('customer_contacts.forms.fields.email'))
                        ->email()
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 1,
                            'xl' => 1,
                        ]),
                    TextInput::make('phone_1')
                        ->label(__('customer_contacts.forms.fields.phone_1'))
                        ->mask('(99) 99999-9999')
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 1,
                            'xl' => 1,
                        ]),
                    TextInput::make('phone_2')
                        ->label(__('customer_contacts.forms.fields.phone_2'))
                        ->mask('(99) 99999-9999')
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 1,
                            'xl' => 1,
                        ]),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('address')
            ->modelLabel('endereço')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('customer_contacts.forms.fields.name')),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('customer_contacts.forms.fields.email')),
                Tables\Columns\TextColumn::make('phone_1')
                    ->label(__('customer_contacts.forms.fields.phone_1')),
                Tables\Columns\TextColumn::make('phone_2')
                    ->label(__('customer_contacts.forms.fields.phone_2')),
                Tables\Columns\IconColumn::make('active')
                    ->label(__('customer_contacts.forms.fields.active'))
                    ->boolean(),
                    Tables\Columns\IconColumn::make('main')
                    ->label(__('customer_contacts.forms.fields.main'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('customer_contacts', 'name'),
                TableTextFilter::buildLike('customer_contacts', 'email'),
                TableTextFilter::buildLike('customer_contacts', 'phone_1'),
                TableTextFilter::buildLike('customer_contacts', 'phone_2'),
                TableActiveFilter::build('customer_contacts'),
                TableBooleanFilter::buildBoolean('customer_contacts', 'main'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->successNotification(success_notification(__('customer_contacts.responses.create.success'))),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('customer_contacts.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('customer_contacts.responses.delete.success'))),
                ]),
            ]);
    }
}
