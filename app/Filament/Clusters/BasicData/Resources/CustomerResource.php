<?php

namespace App\Filament\Clusters\BasicData\Resources;

use App\Core\Filament\Filters\TableActiveFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\BasicData;
use App\Filament\Clusters\BasicData\Resources\CustomerResource\Pages;
use App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers\CustomerAddressesRelationManager;
use App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers\CustomerContactsRelationManager;
use App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers\CustomerEquipmentRelationManager;
use App\Http\Integrations\Receita\Services\ReceitaCnpjService;
use App\Models\Customer;
use Closure;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Throwable;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;
    protected static ?string $modelLabel = 'cliente';
    protected static ?string $navigationGroup = 'Cadastros';
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $cluster = BasicData::class;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Grid::make(1)->schema([
                    Toggle::make('active')
                        ->label(__('customers.forms.fields.active'))
                        ->default(true),
                ]),
                Grid::make(4)->schema([
                    TextInput::make('tax_id_number')
                        ->required()
                        ->label(__('customers.forms.fields.tax_id_number'))
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 1,
                            'xl' => 1,
                        ])
                        ->minLength(14)
                        ->maxLength(18)
                        ->formatStateUsing(fn(?Customer $customer): string => $customer?->friendly_tax_id_number ?? '')
                        ->mask(RawJs::make(<<<'JS'
                            $input.length > 14 ? '99.999.999/9999-99' : '999.999.999-99'
                        JS))
                        ->lazy()
                        ->afterStateUpdated(function (?string $state, Set $set) {
                            if (strlen($state) !== 18) {
                                return;
                            }

                            try {
                                $companyDetails = ReceitaCnpjService::make()->getCompanyDetails($state);
                            } catch (Throwable $th) {
                                error($th);
                                error_notification('Não foi possível obter os dados da receita.')->send();
                                return;
                            }

                            if (isset($companyDetails->status) && $companyDetails->status !== 'OK') {
                                return;
                            }

                            $set('name', $companyDetails->nome);
                            $set('trading_name', $companyDetails->fantasia);
                        })
                        ->rules([
                            fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                if (strlen($value) <= 14 && !validate_cpf($value)) {
                                    $fail('O CPF é inválido.');
                                }
                            }
                        ]),
                    TextInput::make('name')
                        ->required()
                        ->label(__('customers.forms.fields.name'))
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 2,
                            'xl' => 2,
                        ]),
                    TextInput::make('trading_name')
                        ->label(__('customers.forms.fields.trading_name'))
                        ->columnSpan([
                            'xs' => 4,
                            'sm' => 4,
                            'md' => 4,
                            'lg' => 1,
                            'xl' => 1,
                        ]),
                ]),
                Grid::make(4)->schema([
                    TextInput::make('state_registration')
                        ->label(__('customers.forms.fields.state_registration'))
                        ->columnSpan(2),
                    TextInput::make('city_registration')
                        ->label(__('customers.forms.fields.city_registration'))
                        ->columnSpan(2),
                ]),
                Grid::make(1)->schema([
                    Textarea::make('additional_info')
                        ->label(__('customers.forms.fields.additional_info'))
                        ->rows(5),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('customers.forms.fields.name')),
                TextColumn::make('trading_name')
                    ->label(__('customers.forms.fields.trading_name')),
                TextColumn::make('tax_id_number')
                    ->label(__('customers.forms.fields.tax_id_number'))
                    ->formatStateUsing(fn(Customer $customer): string => $customer->friendly_tax_id_number),
                IconColumn::make('active')
                    ->label(__('customers.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('customers', 'name'),
                TableTextFilter::buildLike('customers', 'trading_name'),
                Filter::make('tax_id_number')
                    ->form([TextInput::make('tax_id_number')->label(__('customers.forms.fields.tax_id_number'))])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(!is_null($data['tax_id_number']) && $data['tax_id_number'] !== '', function (Builder $query) use ($data): Builder {
                            return $query->where('tax_id_number', 'like', '%' . Str::remove(['-', '.', '/'], $data['tax_id_number']) . '%');
                        });
                    }),
                TableActiveFilter::build('customers'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->using(function (Customer $customer) {
                            try {
                                DeleteCustomer::run($customer);
                                return redirect()->route('filament.app.resources.customers.index');
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->successNotification(success_notification(__('customers.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->successNotification(success_notification(__('customers.responses.delete_batch.success'))),
                ]),
            ])
            ->emptyStateHeading('Ainda sem clientes')
            ->emptyStateDescription('Assim que você cadastrar seus clientes, eles aparecerão nesta listagem.');
    }

    public static function getRelations(): array
    {
        return [
            CustomerAddressesRelationManager::class,
            CustomerContactsRelationManager::class,
            CustomerEquipmentRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
            'view' => Pages\ViewCustomer::route('/{record}'),
        ];
    }
}
