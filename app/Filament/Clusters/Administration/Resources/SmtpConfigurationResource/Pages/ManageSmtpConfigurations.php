<?php

namespace App\Filament\Clusters\Administration\Resources\SmtpConfigurationResource\Pages;

use App\Filament\Clusters\Administration\Resources\SmtpConfigurationResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageSmtpConfigurations extends ManageRecords
{
    protected static string $resource = SmtpConfigurationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
