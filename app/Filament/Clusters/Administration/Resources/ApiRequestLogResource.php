<?php

namespace App\Filament\Clusters\Administration\Resources;

use App\Core\Filament\Filters\TableDateFilter;
use App\Filament\Clusters\Administration;
use App\Filament\Clusters\Administration\Resources\ApiRequestLogResource\Pages\ListApiRequestLogs;
use App\Filament\Clusters\Administration\Resources\ApiRequestLogResource\Pages\ViewApiRequestLog;
use App\Models\ApiRequestLog;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Novadaemon\FilamentPrettyJson\Form\PrettyJsonField;

class ApiRequestLogResource extends Resource
{
    protected static ?string $model = ApiRequestLog::class;
    protected static ?string $cluster = Administration::class;
    protected static ?string $modelLabel = 'Log de API';
    protected static ?string $pluralModelLabel = 'Logs de API';
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';
    protected static ?int $navigationSort = 4;

    public static function getEloquentQuery(): Builder
    {
        return ApiRequestLog::query()
            ->orderByDesc('id');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('status_code')
                    ->label('Status Code'),
            ]),
            Grid::make(2)->schema([
                PrettyJsonField::make('request_headers')
                    ->label('Headers do JSON de requisição')
                    ->formatStateUsing(fn(Get $get) => ouess_aes256cbc_decrypt($get('request_headers'))),
                PrettyJsonField::make('request_body')
                    ->label('JSON de requisição')
                    ->formatStateUsing(fn(Get $get) => ouess_aes256cbc_decrypt($get('request_body'))),
            ]),
            Grid::make(2)->schema([
                PrettyJsonField::make('response_headers')
                    ->label('Headers do JSON de resposta')
                    ->formatStateUsing(fn(Get $get) => ouess_aes256cbc_decrypt($get('response_headers'))),
                PrettyJsonField::make('response_body')
                    ->label('JSON de resposta')
                    ->formatStateUsing(fn(Get $get) => ouess_aes256cbc_decrypt($get('response_body'))),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID'),
                TextColumn::make('model_id')
                    ->label('ID do modelo'),
                TextColumn::make('model_type')
                    ->label('Classe'),
                IconColumn::make('success')
                    ->label('Sucesso')
                    ->boolean(),
                TextColumn::make('status_code')
                    ->label('Status Code'),
                TextColumn::make('created_at')
                    ->label('Criado em')
                    ->formatStateUsing(fn(ApiRequestLog $apiRequestLog): string => format_datetime($apiRequestLog->created_at)),
            ])
            ->filters([
                SelectFilter::make('success')
                    ->label(__('api_request_logs.forms.fields.success'))
                    ->options([
                        true => 'Sim',
                        false => 'Não',
                    ]),
                TableDateFilter::buildFrom('api_request_logs', 'created_at', 'created_at_from'),
                TableDateFilter::buildTo('api_request_logs', 'created_at', 'created_at_to'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListApiRequestLogs::route('/'),
            'view' => ViewApiRequestLog::route('/{record}'),
        ];
    }
}
