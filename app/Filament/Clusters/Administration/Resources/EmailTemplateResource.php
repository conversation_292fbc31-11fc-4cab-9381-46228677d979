<?php

namespace App\Filament\Clusters\Administration\Resources;

use App\Actions\SmtpConfiguration\Queries\GetSmtpConfigurations;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Administration;
use App\Filament\Clusters\Administration\Resources\EmailTemplateResource\Pages;
use App\Models\EmailTemplate;
use Dotswan\FilamentCodeEditor\Fields\CodeEditor;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;

class EmailTemplateResource extends Resource
{
    protected static ?string $model = EmailTemplate::class;
    protected static ?string $cluster = Administration::class;
    protected static ?string $modelLabel = 'template de email';
    protected static ?string $pluralModelLabel = 'templates de email';
    protected static ?string $navigationIcon = 'heroicon-o-envelope';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Grid::make(4)->schema([
                TextInput::make('name')
                    ->label(__('email_templates.forms.fields.name'))
                    ->required()
                    ->columnSpan(3),
                Forms\Components\Select::make('smtp_configuration_id')
                    ->label(__('email_templates.forms.fields.smtp_configuration_id'))
                    ->required()
                    ->options(GetSmtpConfigurations::run()->pluck('username', 'id')->toArray()),
            ]),
            Forms\Components\Grid::make(4)->schema([
                Forms\Components\Toggle::make('uses_for_service_order_reporting')
                    ->label(__('email_templates.forms.fields.uses_for_service_order_reporting'))
                    ->default(true),
            ]),
            Forms\Components\Grid::make(1)->schema([
                CodeEditor::make('email_template')
                    ->label(__('email_templates.forms.fields.email_template'))
                    ->required()
                    ->columnSpanFull(),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('email_templates.forms.fields.name')),
                Tables\Columns\TextColumn::make('smtpConfiguration.username')
                    ->label(__('smtp_configurations.forms.fields.username')),
            ])
            ->filters([
                TableTextFilter::buildLike('email_templates', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge),
                    Tables\Actions\EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->successNotification(success_notification(__('email_templates.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('email_templates.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateDescription('Assim que você cadastrar seus templates de email, eles aparecerão nessa listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEmailTemplates::route('/'),
        ];
    }
}