<?php

namespace App\Filament\Clusters\Administration\Resources\ApiRequestLogResource\Pages;

use App\Actions\Order\Integrations\ErpFlex\CreateOrderInErpFlex;
use App\Filament\Clusters\Administration\Resources\ApiRequestLogResource;
use App\Models\ThirdPartyOrder;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Resources\Pages\ListRecords;
use Throwable;

class ListApiRequestLogs extends ListRecords
{
    protected static string $resource = ApiRequestLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('reprocess_orders')
                    ->label('Reprocessar pedidos')
                    ->requiresConfirmation()
                    ->action(function (): void {
                        try {
                            ThirdPartyOrder::query()
                                ->whereNull('third_party_id')
                                ->get()
                                ->each(fn(ThirdPartyOrder $thirdPartyOrder) => CreateOrderInErpFlex::dispatch($thirdPartyOrder->order));

                            success_notification('Os pedidos estão sendo reprocessados em segundo plano.')->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage());
                        }
                    }),
            ]),
        ];
    }
}
