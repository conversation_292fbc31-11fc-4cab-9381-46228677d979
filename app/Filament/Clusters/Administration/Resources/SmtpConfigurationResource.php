<?php

namespace App\Filament\Clusters\Administration\Resources;

use App\Filament\Clusters\Administration;
use App\Filament\Clusters\Administration\Resources\SmtpConfigurationResource\Pages\ManageSmtpConfigurations;
use App\Models\SmtpConfiguration;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SmtpConfigurationResource extends Resource
{
    protected static ?string $model = SmtpConfiguration::class;
    protected static ?string $cluster = Administration::class;
    protected static ?string $modelLabel = 'Configuração SMTP';
    protected static ?string $pluralModelLabel = 'Configurações SMTP';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 2;

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('host')
                    ->label(__('smtp_configurations.forms.fields.host'))
                    ->required()
                    ->columnSpan(3),
                TextInput::make('port')
                    ->label(__('smtp_configurations.forms.fields.port'))
                    ->required()
                    ->numeric(),
            ]),
            Grid::make(4)->schema([
                TextInput::make('username')
                    ->label(__('smtp_configurations.forms.fields.username'))
                    ->required()
                    ->columnSpan(2),
                TextInput::make('password')
                    ->label(__('smtp_configurations.forms.fields.password'))
                    ->required(),
                TextInput::make('encryption')
                    ->label(__('smtp_configurations.forms.fields.encryption'))
                    ->required(),
            ]),
            Grid::make(4)->schema([
                TextInput::make('from_address')
                    ->label(__('smtp_configurations.forms.fields.from_address'))
                    ->required()
                    ->columnSpan(3),
                TextInput::make('from_name')
                    ->label(__('smtp_configurations.forms.fields.from_name'))
                    ->required(),
            ]),
            Grid::make(1)->schema([
                Toggle::make('active')
                    ->label(__('smtp_configurations.forms.fields.active')),
            ])
        ]);
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label(__('smtp_configurations.forms.fields.id')),
                TextColumn::make('host')->label(__('smtp_configurations.forms.fields.host')),
                TextColumn::make('username')->label(__('smtp_configurations.forms.fields.username')),
                IconColumn::make('active')
                    ->label(__('smtp_configurations.forms.fields.active'))
                    ->boolean()
            ])
            ->filters([
                Filter::make('host')
                    ->form([
                        TextInput::make('host')->label(__('smtp_configurations.forms.fields.host'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->where('host', 'like', "%{$data['host']}%");
                    }),
                SelectFilter::make('active')
                    ->label(__('smtp_configurations.forms.fields.active'))
                    ->options([
                        true => 'Sim',
                        false => 'Não'
                    ])
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make(),
                    DeleteAction::make()
                ])
            ])
            ->bulkActions([])
            ->emptyStateActions([
                CreateAction::make(),
            ])
            ->emptyStateHeading('Ainda sem configurações SMTP')
            ->emptyStateDescription('Assim que você cadastrar suas configurações SMTP, elas aparecerão nesta listagem.');
    }

    /**
     * Get the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageSmtpConfigurations::route('/')
        ];
    }
}
