<?php

namespace App\Filament\Clusters\Administration\Resources\WorkflowResource\Pages;

use App\Filament\Clusters\Administration\Resources\WorkflowResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageWorkflows extends ManageRecords
{
    protected static string $resource = WorkflowResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->successNotification(success_notification(__('workflows.responses.create.success'))),
        ];
    }
}
