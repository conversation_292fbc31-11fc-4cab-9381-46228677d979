<?php

namespace App\Filament\Clusters\Administration\Resources;

use App\Actions\Workflow\DeleteWorkflow;
use App\Core\Filament\Filters\TableActiveFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\WorkflowTypeEnum;
use App\Filament\Clusters\Administration;
use App\Filament\Clusters\Administration\Resources\WorkflowResource\Pages;
use App\Models\Workflow;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Throwable;

class WorkflowResource extends Resource
{
    protected static ?string $model = Workflow::class;
    protected static ?string $navigationIcon = 'heroicon-o-view-columns';
    protected static ?string $cluster = Administration::class;
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Toggle::make('active')
                    ->label(__('workflows.forms.fields.active'))
                    ->default(true),
            ]),
            Grid::make(4)->schema([
                TextInput::make('name')
                    ->label(__('workflows.forms.fields.name'))
                    ->required()
                    ->columnSpan(3),
                Select::make('type')
                    ->label(__('workflows.forms.fields.type'))
                    ->required()
                    ->options(WorkflowTypeEnum::getTranslated()),
            ]),
            Grid::make(1)->schema([
                Fieldset::make('Passos')->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('workflow_states')
                            ->relationship('workflowStates')
                            ->hiddenLabel()
                            ->addActionLabel('Adicionar passo')
                            ->headers([
                                Header::make(__('workflow_states.forms.fields.sequence'))
                                    ->markAsRequired()
                                    ->width('15%'),
                                Header::make(__('workflow_states.forms.fields.name'))
                                    ->markAsRequired(),
                                Header::make(__('workflow_states.forms.fields.characterizes_service_order_execution'))
                                    ->width('10%'),
                                Header::make(__('workflow_states.forms.fields.characterizes_resolution'))
                                    ->width('10%'),
                            ])
                            ->schema([
                                TextInput::make('sequence')
                                    ->required()
                                    ->numeric(),
                                TextInput::make('name')
                                    ->required(),
                                Toggle::make('characterizes_service_order_execution'),
                                Toggle::make('characterizes_resolution'),
                            ])
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('workflows.forms.fields.name')),
                TextColumn::make('type')
                    ->label(__('workflows.forms.fields.type'))
                    ->formatStateUsing(fn(Workflow $workflow): string => $workflow->friendly_type),
                IconColumn::make('active')
                    ->label(__('workflows.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('workflows', 'name'),
                TableActiveFilter::build('workflows'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('workflows.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->action(function (Workflow $workflow): void {
                            try {
                                DeleteWorkflow::run($workflow);
                                success_notification(__('workflows.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage());
                            }
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Ainda sem workflows')
            ->emptyStateDescription('Assim que você cadastrar seus workflows, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageWorkflows::route('/'),
        ];
    }
}
