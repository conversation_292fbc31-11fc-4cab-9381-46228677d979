<?php

namespace App\Filament\Clusters\AccessControl\Resources\RoleResource\Pages;

use App\Actions\Role\CreateRole;
use App\Filament\Clusters\AccessControl\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Throwable;

class ManageRoles extends ManageRecords
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->using(function (array $data): void {
                    try {
                        CreateRole::run($data);
                        success_notification(__('roles.responses.create.success'))->send();
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }
}
