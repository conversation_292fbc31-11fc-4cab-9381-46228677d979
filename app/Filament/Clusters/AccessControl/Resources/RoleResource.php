<?php

namespace App\Filament\Clusters\AccessControl\Resources;

use App\Actions\Role\DeleteRole;
use App\Actions\Role\UpdateRole;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\AccessControl;
use App\Filament\Clusters\AccessControl\Resources\RoleResource\Pages;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Throwable;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;
    protected static ?string $modelLabel = 'perfil';
    protected static ?string $pluralModelLabel = 'perfis';
    protected static ?string $cluster = AccessControl::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 2;

    public static function getEloquentQuery(): Builder
    {
        return Role::query()
            ->whereNot('name', 'Administrador');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Tabs::make()
                ->columnSpanFull()
                ->schema([
                    Tabs\Tab::make('Geral')->schema([
                        Grid::make(1)->schema([
                            TextInput::make('name')
                                ->label(__('roles.forms.fields.name'))
                                ->required(),
                        ]),
                    ]),
                    Tabs\Tab::make('Permissões')->schema([
                        Grid::make(1)->schema([
                            Fieldset::make('Serviços')->schema([
                                Grid::make(4)->schema([
                                    Toggle::make('get_service_orders')->label(__('permissions.get_service_orders')),
                                    Toggle::make('create_service_orders')->label(__('permissions.create_service_orders')),
                                    Toggle::make('update_service_orders')->label(__('permissions.update_service_orders')),
                                    Toggle::make('delete_service_orders')->label(__('permissions.delete_service_orders')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('reopen_service_orders')->label(__('permissions.reopen_service_orders')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_protocols')->label(__('permissions.get_protocols')),
                                    Toggle::make('create_protocols')->label(__('permissions.create_protocols')),
                                    Toggle::make('update_protocols')->label(__('permissions.update_protocols')),
                                    Toggle::make('delete_protocols')->label(__('permissions.delete_protocols')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_contracts')->label(__('permissions.get_contracts')),
                                    Toggle::make('create_contracts')->label(__('permissions.create_contracts')),
                                    Toggle::make('update_contracts')->label(__('permissions.update_contracts')),
                                    Toggle::make('delete_contracts')->label(__('permissions.delete_contracts')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_quotes')->label(__('permissions.get_quotes')),
                                    Toggle::make('create_quotes')->label(__('permissions.create_quotes')),
                                    Toggle::make('update_quotes')->label(__('permissions.update_quotes')),
                                    Toggle::make('delete_quotes')->label(__('permissions.delete_quotes')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_checklists')->label(__('permissions.get_checklists')),
                                    Toggle::make('create_checklists')->label(__('permissions.create_checklists')),
                                    Toggle::make('update_checklists')->label(__('permissions.update_checklists')),
                                    Toggle::make('delete_checklists')->label(__('permissions.delete_checklists')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_service_types')->label(__('permissions.get_service_types')),
                                    Toggle::make('create_service_types')->label(__('permissions.create_service_types')),
                                    Toggle::make('update_service_types')->label(__('permissions.update_service_types')),
                                    Toggle::make('delete_service_types')->label(__('permissions.delete_service_types')),
                                ]),
                            ]),
                            Fieldset::make('Chamados')->schema([
                                Grid::make(4)->schema([
                                    Toggle::make('get_tickets')->label(__('permissions.get_tickets')),
                                    Toggle::make('create_tickets')->label(__('permissions.create_tickets')),
                                    Toggle::make('update_tickets')->label(__('permissions.update_tickets')),
                                    Toggle::make('delete_tickets')->label(__('permissions.delete_tickets')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_ticket_types')->label(__('permissions.get_ticket_types')),
                                    Toggle::make('create_ticket_types')->label(__('permissions.create_ticket_types')),
                                    Toggle::make('update_ticket_types')->label(__('permissions.update_ticket_types')),
                                    Toggle::make('delete_ticket_types')->label(__('permissions.delete_ticket_types')),
                                ]),
                            ]),
                            Fieldset::make('Gestão de ativos')->schema([
                                Grid::make(4)->schema([
                                    Toggle::make('get_equipment')->label(__('permissions.get_equipment')),
                                    Toggle::make('create_equipment')->label(__('permissions.create_equipment')),
                                    Toggle::make('update_equipment')->label(__('permissions.update_equipment')),
                                    Toggle::make('delete_equipment')->label(__('permissions.delete_equipment')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_equipment_types')->label(__('permissions.get_equipment_types')),
                                    Toggle::make('create_equipment_types')->label(__('permissions.create_equipment_types')),
                                    Toggle::make('update_equipment_types')->label(__('permissions.update_equipment_types')),
                                    Toggle::make('delete_equipment_types')->label(__('permissions.delete_equipment_types')),
                                ]),
                            ]),
                            Fieldset::make('Estoque')->schema([
                                Grid::make(4)->schema([
                                    Toggle::make('get_products')->label(__('permissions.get_products')),
                                    Toggle::make('create_products')->label(__('permissions.create_products')),
                                    Toggle::make('update_products')->label(__('permissions.update_products')),
                                    Toggle::make('delete_products')->label(__('permissions.delete_products')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_stock_movements')->label(__('permissions.get_stock_movements')),
                                    Toggle::make('create_stock_movements')->label(__('permissions.create_stock_movements')),
                                    Toggle::make('update_stock_movements')->label(__('permissions.update_stock_movements')),
                                    Toggle::make('delete_stock_movements')->label(__('permissions.delete_stock_movements')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_product_types')->label(__('permissions.get_product_types')),
                                    Toggle::make('create_product_types')->label(__('permissions.create_product_types')),
                                    Toggle::make('update_product_types')->label(__('permissions.update_product_types')),
                                    Toggle::make('delete_product_types')->label(__('permissions.delete_product_types')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_warehouses')->label(__('permissions.get_warehouses')),
                                    Toggle::make('create_warehouses')->label(__('permissions.create_warehouses')),
                                    Toggle::make('update_warehouses')->label(__('permissions.update_warehouses')),
                                    Toggle::make('delete_warehouses')->label(__('permissions.delete_warehouses')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_measuring_units')->label(__('permissions.get_measuring_units')),
                                    Toggle::make('create_measuring_units')->label(__('permissions.create_measuring_units')),
                                    Toggle::make('update_measuring_units')->label(__('permissions.update_measuring_units')),
                                    Toggle::make('delete_measuring_units')->label(__('permissions.delete_measuring_units')),
                                ]),
                            ]),
                            Fieldset::make('Cadastros')->schema([
                                Grid::make(4)->schema([
                                    Toggle::make('get_customers')->label(__('permissions.get_customers')),
                                    Toggle::make('create_customers')->label(__('permissions.create_customers')),
                                    Toggle::make('update_customers')->label(__('permissions.update_customers')),
                                    Toggle::make('delete_customers')->label(__('permissions.delete_customers')),
                                ]),
                            ]),
                            Fieldset::make('Controle de acesso')->schema([
                                Grid::make(4)->schema([
                                    Toggle::make('get_users')->label(__('permissions.get_users')),
                                    Toggle::make('create_users')->label(__('permissions.create_users')),
                                    Toggle::make('update_users')->label(__('permissions.update_users')),
                                    Toggle::make('delete_users')->label(__('permissions.delete_users')),
                                ]),
                                Grid::make(4)->schema([
                                    Toggle::make('get_roles')->label(__('permissions.get_roles')),
                                    Toggle::make('create_roles')->label(__('permissions.create_roles')),
                                    Toggle::make('update_roles')->label(__('permissions.update_roles')),
                                    Toggle::make('delete_roles')->label(__('permissions.delete_roles')),
                                ]),
                            ]),
                        ]),
                    ]),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('roles.forms.fields.name')),
            ])
            ->filters([
                TableTextFilter::buildLike('roles', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->mutateRecordDataUsing(function (Role $role, array $data): array {
                            return array_merge(
                                $data,
                                $role->permissions
                                    ->mapWithKeys(fn(Permission $permission): array => [$permission->name => true])
                                    ->toArray()
                            );
                        }),
                    Tables\Actions\EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->mutateRecordDataUsing(function (Role $role, array $data): array {
                            return array_merge(
                                $data,
                                $role->permissions
                                    ->mapWithKeys(fn(Permission $permission): array => [$permission->name => true])
                                    ->toArray()
                            );
                        })
                        ->using(function (Role $role, array $data): void {
                            try {
                                UpdateRole::run($role, $data);
                                success_notification(__('roles.responses.update.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    Tables\Actions\DeleteAction::make()
                        ->using(function (Role $role): void {
                            try {
                                DeleteRole::run($role);
                                success_notification(__('roles.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                ])
            ])
            ->emptyStateHeading('Ainda sem perfis')
            ->emptyStateDescription('Assim que você cadastrar seus perfis, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRoles::route('/'),
        ];
    }
}
