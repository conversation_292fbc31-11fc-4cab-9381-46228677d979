<?php

namespace App\Filament\Clusters\Tickets\Resources;

use App\Actions\ServiceType\Queries\GetServiceTypes;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\TicketTypePriorityEnum;
use App\Filament\Clusters\Tickets;
use App\Filament\Clusters\Tickets\Resources\TicketTypeResource\Pages;
use App\Models\TicketType;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TicketTypeResource extends Resource
{
    protected static ?string $model = TicketType::class;
    protected static ?string $modelLabel = 'tipo de chamado';
    protected static ?string $pluralModelLabel = 'tipos de chamado';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $cluster = Tickets::class;
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('ticket_types.forms.fields.name'))
                    ->required(),
            ]),
            Grid::make(4)->schema([
                Select::make('service_type_id')
                    ->label(__('ticket_types.forms.fields.service_type_id'))
                    ->required()
                    ->options(GetServiceTypes::run(true)->pluck('name', 'id')->toArray())
                    ->columnSpan(2),
                Select::make('priority')
                    ->label(__('ticket_types.forms.fields.priority'))
                    ->required()
                    ->options(TicketTypePriorityEnum::getTranslated()),
                TextInput::make('sla_hours')
                    ->label(__('ticket_types.forms.fields.sla_hours'))
                    ->required()
                    ->numeric(),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('ticket_types.forms.fields.name')),
                TextColumn::make('serviceType.name')
                    ->label(__('ticket_types.forms.fields.service_type_id')),
                TextColumn::make('priority')
                    ->label(__('ticket_types.forms.fields.priority'))
                    ->formatStateUsing(fn(TicketType $ticketType): string => TicketTypePriorityEnum::getTranslated()[$ticketType->priority])
                    ->badge(),
                TextColumn::make('sla_hours')
                    ->label(__('ticket_types.forms.fields.sla_hours')),
            ])
            ->filters([
                TableTextFilter::buildLike('ticket_types', 'name'),
                TableSelectFilter::build('ticket_types', 'service_type_id', GetServiceTypes::run(true)->pluck('name', 'id')->toArray()),
                TableSelectFilter::build('ticket_types', 'priority', TicketTypePriorityEnum::getTranslated()),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(success_notification(__('ticket_types.responses.update.success'))),
                    DeleteAction::make()
                        ->successNotification(success_notification(__('ticket_types.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem tipos de chamado')
            ->emptyStateDescription('Assim que você cadastrar seus tipos de chamado, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTicketTypes::route('/'),
        ];
    }
}
