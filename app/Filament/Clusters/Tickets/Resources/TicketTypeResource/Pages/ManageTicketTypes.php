<?php

namespace App\Filament\Clusters\Tickets\Resources\TicketTypeResource\Pages;

use App\Filament\Clusters\Tickets\Resources\TicketTypeResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;

class ManageTicketTypes extends ManageRecords
{
    protected static string $resource = TicketTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->successNotification(success_notification(__('ticket_types.responses.create.success'))),
        ];
    }
}
