<?php

namespace App\Filament\Clusters\AssetManagement\Resources\EquipmentResource\Pages;

use App\Actions\Equipment\CreateEquipment;
use App\Actions\Equipment\Integrations\GetThirdPartyEquipment;
use App\Filament\Clusters\AssetManagement\Resources\EquipmentResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageEquipment extends ManageRecords
{
    protected static string $resource = EquipmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-equipment')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyEquipment::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.asset-management.resources.equipment.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os tipos de serviço neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->using(function (array $data): void {
                    try {
                        CreateEquipment::run($data);
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                })
                ->successNotification(success_notification(__('equipment.responses.create.success'))),
        ];
    }
}
