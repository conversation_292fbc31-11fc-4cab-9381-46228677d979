<?php

namespace App\Filament\Clusters\AssetManagement\Resources;

use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\AssetManagement;
use App\Filament\Clusters\AssetManagement\Resources\EquipmentTypeResource\Pages;
use App\Models\EquipmentType;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class EquipmentTypeResource extends Resource
{
    protected static ?string $model = EquipmentType::class;
    protected static ?string $modelLabel = 'tipo de equipamento';
    protected static ?string $pluralModelLabel = 'tipos de equipamento';
    protected static ?string $cluster = AssetManagement::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('equipment_types.forms.fields.name'))
                    ->required(),
            ]),
            Grid::make(1)->schema([
                TableRepeater::make('equipment_type_custom_fields')
                    ->label('Campos personalizados')
                    ->relationship('equipmentTypeCustomFields')
                    ->defaultItems(0)
                    ->addActionLabel('Adicionar campo customizado')
                    ->headers([
                        Header::make(__('equipment_types.forms.fields.name'))
                            ->markAsRequired(),
                        Header::make(__('equipment_types.forms.fields.required')),
                    ])
                    ->schema([
                        TextInput::make('name'),
                        Toggle::make('required'),
                    ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('equipment.forms.fields.name')),
            ])
            ->filters([
                TableTextFilter::buildLike('equipment', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('equipment_types.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('equipment_types.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem tipos de equipamento')
            ->emptyStateDescription('Assim que você cadastrar seus tipos de equipamento, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEquipmentTypes::route('/'),
        ];
    }
}
