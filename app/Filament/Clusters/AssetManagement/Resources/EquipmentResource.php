<?php

namespace App\Filament\Clusters\AssetManagement\Resources;

use App\Actions\Customer\Queries\GetCustomersByNameTradingNameOrTaxIdentificationNumber;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\AssetManagement;
use App\Filament\Clusters\AssetManagement\Resources\EquipmentResource\Pages;
use App\Models\Customer;
use App\Models\Equipment;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class EquipmentResource extends Resource
{
    protected static ?string $model = Equipment::class;
    protected static ?string $modelLabel = 'equipamento';
    protected static ?string $cluster = AssetManagement::class;
    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Select::make('customer_id')
                    ->label(__('equipment.forms.fields.customer_id'))
                    ->relationship('customer', 'name')
                    ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                    ->reactive()
                    ->searchable()
                    ->getSearchResultsUsing(fn(string $search): array => GetCustomersByNameTradingNameOrTaxIdentificationNumber::run($search, true)->pluck('name', 'id')->toArray()),
            ]),
            Grid::make(2)->schema([
                TextInput::make('serial_number')
                    ->label(__('equipment.forms.fields.serial_number'))
                    ->required(),
                Select::make('equipment_type_id')
                    ->label(__('equipment.forms.fields.equipment_type_id'))
                    ->relationship('equipmentType', 'name')
                    ->required(),
            ]),
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('equipment.forms.fields.name'))
                    ->required(),
            ]),
            Grid::make(1)->schema([
                Textarea::make('additional_info')
                    ->label(__('equipment.forms.fields.additional_info'))
                    ->rows(3),
            ]),
            Grid::make(2)->schema([
                TextInput::make('manufactured_at')
                    ->label(__('equipment.forms.fields.manufactured_at'))
                    ->type('date'),
                TextInput::make('warranty_expires_at')
                    ->label(__('equipment.forms.fields.warranty_expires_at'))
                    ->type('date'),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('serial_number')
                    ->label(__('equipment.forms.fields.serial_number')),
                TextColumn::make('name')
                    ->label(__('equipment.forms.fields.name')),
                TextColumn::make('equipmentType.name')
                    ->label(__('equipment.forms.fields.equipment_type_id')),
                TextColumn::make('code')
                    ->label(__('equipment.forms.fields.code')),
            ])
            ->filters([
                TableTextFilter::buildLike('equipment', 'serial_number'),
                TableTextFilter::buildLike('equipment', 'name'),
                TableTextFilter::buildLike('equipment', 'code'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->mutateRecordDataUsing(function (Equipment $equipment, array $data): array {
                            return array_merge($data, ['equipment_type_id' => $equipment->equipment_type_id]);
                        })
                        ->successNotification(success_notification(__('equipment.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('equipment.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Ainda sem equipamentos')
            ->emptyStateDescription('Assim que você cadastrar seus equipamentos, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEquipment::route('/'),
        ];
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  \App\Models\Equipment|null $equipment
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected static function handleCustomerDependantFields(?Equipment $equipment, ?string $customerId, Set $set): ?Customer
    {
        if (is_null($customerId)) {
            return null;
        }

        if ($equipment->customer) {
            $set('customer_trading_name', $equipment->customer?->trading_name ?? '');
            $set('customer_tax_id_number', $equipment->customer?->friendly_tax_id_number ?? '');
            return $equipment->customer;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customer_trading_name', $customer?->trading_name ?? '');
        $set('customer_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }
}
