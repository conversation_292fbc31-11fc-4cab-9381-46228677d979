<?php

namespace App\Filament\Clusters\AssetManagement\Resources\EquipmentTypeResource\Pages;

use App\Actions\EquipmentType\Integrations\GetThirdPartyEquipmentTypes;
use App\Filament\Clusters\AssetManagement\Resources\EquipmentTypeResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageEquipmentTypes extends ManageRecords
{
    protected static string $resource = EquipmentTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-equipment-types')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyEquipmentTypes::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.asset-management.resources.equipment-types.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os tipos de equipamento neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->successNotification(success_notification(__('equipment_types.responses.create.success'))),
        ];
    }
}
