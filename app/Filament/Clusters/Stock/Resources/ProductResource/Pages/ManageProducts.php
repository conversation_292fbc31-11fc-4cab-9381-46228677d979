<?php

namespace App\Filament\Clusters\Stock\Resources\ProductResource\Pages;

use App\Actions\Product\Integrations\GetThirdPartyProducts;
use App\Filament\Clusters\Stock\Resources\ProductResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Throwable;

class ManageProducts extends ManageRecords
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-equipment')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyProducts::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.stock.resources.products.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os produtos neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->successNotification(success_notification(__('products.responses.create.success'))),
        ];
    }
}
