<?php

namespace App\Filament\Clusters\Stock\Resources\StockMovementResource\Pages;

use App\Actions\StockMovement\CreateStockMovement;
use App\Filament\Clusters\Stock\Resources\StockMovementResource;
use App\Models\Product;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageStockMovements extends ManageRecords
{
    protected static string $resource = StockMovementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->action(function (array $data): void {
                    $data['item_type'] = Product::class;

                    try {
                        CreateStockMovement::run($data);
                        success_notification(__('stock_movements.responses.create.success'))->send();
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }
}
