<?php

namespace App\Filament\Clusters\Stock\Resources;

use App\Actions\Product\Queries\GetProductsByCodeOrName;
use App\Actions\StockMovement\DeleteStockMovement;
use App\Actions\StockMovement\UpdateStockMovement;
use App\Actions\Warehouse\Queries\GetWarehousesByName;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Stock;
use App\Filament\Clusters\Stock\Resources\StockMovementResource\Pages;
use App\Models\Product;
use App\Models\StockMovement;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Throwable;

class StockMovementResource extends Resource
{
    protected static ?string $model = StockMovement::class;
    protected static ?string $modelLabel = 'movimento de estoque';
    protected static ?string $pluralModelLabel = 'movimentos de estoque';
    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';
    protected static ?string $cluster = Stock::class;
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                Select::make('item_id')
                    ->label(__('stock_movements.forms.fields.item_id'))
                    ->placeholder('Digite o nome de um produto')
                    ->required()
                    ->columnSpan(3)
                    ->reactive()
                    ->searchable()
                    ->getSearchResultsUsing(fn(string $search): array => GetProductsByCodeOrName::run($search, true)->pluck('name', 'id')->toArray())
                    ->getOptionLabelUsing(fn(?string $state, ?StockMovement $stockMovement): ?string => $stockMovement->item->name ?? Product::find($state)->name ?? ''),
                TextInput::make('quantity')
                    ->label(__('stock_movements.forms.fields.quantity'))
                    ->required()
                    ->numeric(),
            ]),
            Grid::make(2)->schema([
                Select::make('origin_warehouse_id')
                    ->label(__('stock_movements.forms.fields.origin_warehouse_id'))
                    ->required()
                    ->reactive()
                    ->options(GetWarehousesByName::run(true)->pluck('name', 'id')->toArray()),
                Select::make('destination_warehouse_id')
                    ->label(__('stock_movements.forms.fields.destination_warehouse_id'))
                    ->required()
                    ->reactive()
                    ->options(GetWarehousesByName::run(true)->pluck('name', 'id')->toArray()),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('item.name')
                    ->label(__('stock_movements.forms.fields.item_id')),
                TextColumn::make('quantity')
                    ->label(__('stock_movements.forms.fields.quantity')),
                TextColumn::make('originWarehouse.name')
                    ->label(__('stock_movements.forms.fields.origin_warehouse_id')),
                TextColumn::make('destinationWarehouse.name')
                    ->label(__('stock_movements.forms.fields.destination_warehouse_id')),
            ])
            ->filters([
                TableTextFilter::buildRelation('stock_movements', 'item_id', 'item', 'name'),
                TableSelectFilter::build('stock_movements', 'origin_warehouse_id', GetWarehousesByName::run(true)->pluck('name', 'id')->toArray()),
                TableSelectFilter::build('stock_movements', 'destination_warehouse_id', GetWarehousesByName::run(true)->pluck('name', 'id')->toArray()),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->action(function (StockMovement $stockMovement, array $data): void {
                            try {
                                UpdateStockMovement::run($stockMovement, $data);
                                success_notification(__('stock_movements.responses.update.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    Tables\Actions\DeleteAction::make()
                        ->action(function (StockMovement $stockMovement): void {
                            try {
                                DeleteStockMovement::run($stockMovement);
                                success_notification(__('stock_movements.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                ]),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageStockMovements::route('/'),
        ];
    }
}
