<?php

namespace App\Filament\Clusters\Stock\Resources;

use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Stock;
use App\Filament\Clusters\Stock\Resources\ProductTypeResource\Pages;
use App\Models\ProductType;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ProductTypeResource extends Resource
{
    protected static ?string $model = ProductType::class;
    protected static ?string $modelLabel = 'tipo de produto';
    protected static ?string $pluralModelLabel = 'tipos de produto';
    protected static ?string $cluster = Stock::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('product_types.forms.fields.name'))
                    ->required(),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('product_types.forms.fields.name')),
            ])
            ->filters([
                TableTextFilter::buildLike('product_types', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('product_types.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('product_types.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Ainda sem tipos de produto')
            ->emptyStateDescription('Assim que você cadastrar seus tipos de produto, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProductTypes::route('/'),
        ];
    }
}
