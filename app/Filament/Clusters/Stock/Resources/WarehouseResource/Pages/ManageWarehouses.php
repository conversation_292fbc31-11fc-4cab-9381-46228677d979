<?php

namespace App\Filament\Clusters\Stock\Resources\WarehouseResource\Pages;

use App\Actions\Warehouse\Integrations\GetThirdPartyWarehouses;
use App\Filament\Clusters\Stock\Resources\WarehouseResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageWarehouses extends ManageRecords
{
    protected static string $resource = WarehouseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-warehouses')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyWarehouses::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.stock.resources.warehouses.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os armazéns neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->successNotification(success_notification(__('warehouses.responses.create.success'))),
        ];
    }
}
