<?php

namespace App\Filament\Clusters\Stock\Resources;

use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Stock;
use App\Filament\Clusters\Stock\Resources\WarehouseResource\Pages;
use App\Models\Warehouse;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class WarehouseResource extends Resource
{
    protected static ?string $model = Warehouse::class;
    protected static ?string $modelLabel = 'armazém';
    protected static ?string $pluralModelLabel = 'armazéns';
    protected static ?string $cluster = Stock::class;
    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('warehouses.forms.fields.name'))
                    ->required(),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('warehouses.forms.fields.name')),
            ])
            ->filters([
                TableTextFilter::buildLike('warehouses', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('warehouses.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('warehouses.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Ainda sem armazéns')
            ->emptyStateDescription('Assim que você cadastrar seus armazéns, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageWarehouses::route('/'),
        ];
    }
}
