<?php

namespace App\Filament\Clusters\Stock\Resources\MeasuringUnitResource\Pages;

use App\Actions\MeasuringUnit\Integrations\GetThirdPartyMeasuringUnits;
use App\Filament\Clusters\Stock\Resources\MeasuringUnitResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageMeasuringUnits extends ManageRecords
{
    protected static string $resource = MeasuringUnitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-measuring-units')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyMeasuringUnits::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.stock.resources.measuring-units.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar as unidades de medida neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->successNotification(success_notification(__('measuring_units.responses.create.success'))),
        ];
    }
}
