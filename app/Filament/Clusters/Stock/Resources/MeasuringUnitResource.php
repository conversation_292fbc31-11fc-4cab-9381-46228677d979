<?php

namespace App\Filament\Clusters\Stock\Resources;

use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Stock;
use App\Filament\Clusters\Stock\Resources\MeasuringUnitResource\Pages;
use App\Models\MeasuringUnit;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class MeasuringUnitResource extends Resource
{
    protected static ?string $model = MeasuringUnit::class;
    protected static ?string $modelLabel = 'unidade de medida';
    protected static ?string $pluralModelLabel = 'unidades de medida';
    protected static ?string $cluster = Stock::class;
    protected static ?string $navigationIcon = 'heroicon-o-scale';
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('measuring_units.forms.fields.name'))
                    ->required(),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('measuring_units.forms.fields.name')),
            ])
            ->filters([
                TableTextFilter::buildLike('measuring_units', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('measuring_units.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('measuring_units.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Ainda sem unidades de medida')
            ->emptyStateDescription('Assim que você cadastrar suas unidades de medida, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageMeasuringUnits::route('/'),
        ];
    }
}
