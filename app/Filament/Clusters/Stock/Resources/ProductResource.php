<?php

namespace App\Filament\Clusters\Stock\Resources;

use App\Actions\MeasuringUnit\Queries\GetMeasuringUnits;
use App\Actions\Product\DeleteProduct;
use App\Actions\ProductType\Queries\GetProductTypes;
use App\Actions\Warehouse\Queries\GetWarehousesByName;
use App\Core\Filament\Filters\TableActiveFilter;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Stock;
use App\Filament\Clusters\Stock\Resources\ProductResource\Pages;
use App\Models\Product;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Throwable;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;
    protected static ?string $modelLabel = 'produto';
    protected static ?string $cluster = Stock::class;
    protected static ?string $navigationIcon = 'heroicon-o-archive-box';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                Toggle::make('active')
                    ->label(__('products.forms.fields.active'))
                    ->default(true),
            ]),
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            FileUpload::make('image_path')
                                ->label('Imagem')
                                ->disk('digitalocean')
                                ->downloadable(),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('code')
                                ->label(__('products.forms.fields.code'))
                                ->required(),
                            TextInput::make('name')
                                ->label(__('products.forms.fields.name'))
                                ->required()
                                ->columnSpan(3),
                        ]),
                        Grid::make(4)->schema([
                            Select::make('product_type_id')
                                ->label(__('products.forms.fields.product_type_id'))
                                ->required()
                                ->columnSpan(2)
                                ->options(GetProductTypes::run(true)->pluck('name', 'id')->toArray()),
                            Select::make('measuring_unit_id')
                                ->label(__('products.forms.fields.measuring_unit_id'))
                                ->required()
                                ->options(GetMeasuringUnits::run(true)->pluck('name', 'id')->toArray()),
                            TextInput::make('default_amount')
                                ->label(__('products.forms.fields.default_amount'))
                                ->required()
                                ->mask(fn(): RawJs => RawJs::make(<<<'JS'
                                    'R$ ' + $money($input, ',', '', 2)
                                JS)),
                        ]),
                        Grid::make(1)->schema([
                            Textarea::make('additional_info')
                                ->label(__('products.forms.fields.additional_info'))
                                ->rows(3)
                                ->maxLength(2000),
                        ]),
                    ]),
                    Tab::make('Estoque')->schema([
                        Grid::make(1)->schema([
                            TableRepeater::make('product_warehouses')
                                ->relationship('productWarehouses')
                                ->hiddenLabel()
                                ->addActionLabel('Adicionar armazém')
                                ->defaultItems(1)
                                ->headers([
                                    Header::make(__('warehouse_products.forms.fields.warehouse_id'))
                                        ->markAsRequired()
                                        ->width('60%'),
                                    Header::make(__('warehouse_products.forms.fields.available_amount'))
                                        ->markAsRequired(),
                                    Header::make(__('warehouse_products.forms.fields.minimum_amount')),
                                ])
                                ->schema([
                                    Select::make('warehouse_id')
                                        ->options(GetWarehousesByName::run(true)->pluck('name', 'id')->toArray()),
                                    TextInput::make('available_amount')
                                        ->numeric()
                                        ->disabledOn('edit'),
                                    TextInput::make('minimum_amount')
                                        ->numeric(),
                                ]),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label(__('products.forms.fields.code')),
                TextColumn::make('name')
                    ->label(__('products.forms.fields.name')),
                TextColumn::make('productType.name')
                    ->label(__('products.forms.fields.product_type_id')),
                TextColumn::make('measuringUnit.name')
                    ->label(__('products.forms.fields.measuring_unit_id')),
                TextColumn::make('default_amount')
                    ->label(__('products.forms.fields.default_amount'))
                    ->formatStateUsing(fn(Product $product): string => $product->friendly_default_amount),
                TextColumn::make('available_quantity')
                    ->label(__('warehouse_products.forms.fields.available_amount')),
                IconColumn::make('active')
                    ->label(__('products.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('products', 'code'),
                TableTextFilter::buildLike('products', 'name'),
                TableSelectFilter::build('products', 'product_type_id', GetProductTypes::run(true)->pluck('name', 'id')->toArray()),
                TableSelectFilter::build('products', 'measuring_unit_id', GetMeasuringUnits::run(true)->pluck('name', 'id')->toArray()),
                TableActiveFilter::build('products'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge),
                    Tables\Actions\EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->successNotification(success_notification(__('products.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->action(function (Product $product): void {
                            try {
                                DeleteProduct::run($product);
                                success_notification(__('products.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage());
                            }
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Ainda sem produtos')
            ->emptyStateDescription('Assim que você cadastrar seus produtos, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProducts::route('/'),
        ];
    }
}
