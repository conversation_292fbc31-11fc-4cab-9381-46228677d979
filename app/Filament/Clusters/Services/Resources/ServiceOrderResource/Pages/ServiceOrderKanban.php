<?php

namespace App\Filament\Clusters\Services\Resources\ServiceOrderResource\Pages;

use App\Actions\WorkflowState\Queries\GetWorkflowStatesForWorkflowType;
use App\Enums\WorkflowTypeEnum;
use App\Models\ServiceOrder;
use Filament\Actions\Action;
use Heloufir\FilamentKanban\Filament\KanbanBoard;
use Heloufir\FilamentKanban\ValueObjects\KanbanStatuses;
use Illuminate\Database\Eloquent\Builder;

class ServiceOrderKanban extends KanbanBoard
{
    protected static ?string $title = 'Kanban';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-group';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
            ->label('Voltar')
            ->color('gray')
            ->url(fn(): string => route('filament.app.services.resources.service-orders.index')),
        ];
    }

    public function getStatuses(): KanbanStatuses
    {
        return KanbanStatuses::make(GetWorkflowStatesForWorkflowType::run(WorkflowTypeEnum::ServiceOrder->value));
    }

    public function model(): string
    {
        return ServiceOrder::class;
    }

    public function query(Builder $query): Builder
    {
        return $query;
    }

    public function recordForm(): array
    {
        return [];
    }

    public function recordInfolist(): array
    {
        return [];
    }
}
