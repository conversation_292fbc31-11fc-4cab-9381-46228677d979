<?php

namespace App\Filament\Clusters\Services\Resources\ServiceOrderResource\Pages;

use App\Actions\ServiceOrder\CreateServiceOrder;
use App\Filament\Clusters\Services\Resources\ServiceOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Throwable;

class ManageServiceOrders extends ManageRecords
{
    protected static string $resource = ServiceOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('kanban')
                ->label('Kanban')
                ->color('gray')
                ->url(fn(): string => route('filament.app.services.pages.service-orders.kanban')),
            Actions\CreateAction::make()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->successNotification(success_notification(__('service_orders.responses.create.success'))),
        ];
    }
}
