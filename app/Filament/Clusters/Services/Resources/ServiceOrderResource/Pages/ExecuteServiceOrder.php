<?php

namespace App\Filament\Clusters\Services\Resources\ServiceOrderResource\Pages;

use App\Actions\Product\Queries\GetProductsByCodeOrName;
use App\Actions\ServiceOrder\FinishServiceOrderExecution;
use App\Actions\ServiceOrder\Integrations\ErpFlex\UpdateServiceOrderInErpFlex;
use App\Actions\ServiceOrderExecutionChecklistStepNeed\Queries\GetServiceOrderExecutionChecklistStepNeedByServiceOrderExecutionChecklistStepIdNeedIdAndNeedType;
use App\Actions\ServiceOrderExecutionChecklistStepProduct\CreateServiceOrderExecutionChecklistStepProduct;
use App\Actions\ServiceOrderExecutionChecklistStepProduct\Queries\GetServiceOrderExecutionChecklistStepProductByServiceOrderExecutionChecklistStepIdAndProductId;
use App\Actions\ServiceType\Queries\GetServiceTypeByName;
use App\Actions\StockMovement\UpdateStockMovement;
use App\Actions\Warehouse\Queries\GetWarehousesByName;
use App\Enums\ChecklistStepDataTypeEnum;
use App\Filament\Clusters\Services\Resources\ServiceOrderResource;
use App\Models\Product;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepNeed;
use App\Models\ServiceOrderExecutionChecklistStepOption;
use App\Models\ServiceOrderExecutionChecklistStepProduct;
use App\Models\ServiceType;
use App\Models\ThirdPartyServiceOrder;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions\Action;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Saade\FilamentAutograph\Forms\Components\SignaturePad;

class ExecuteServiceOrder extends Page implements HasForms
{
    use InteractsWithForms;
    use InteractsWithRecord;

    protected static string $resource = ServiceOrderResource::class;
    protected static ?string $title = 'Execução de ordem de serviço';
    protected static string $view = 'filament.clusters.services.resources.service-order-resource.pages.execute-service-order';
    public array $data = [];

    public function mount(): void
    {
        $this->record = $this->resolveRecord(request('service_order'));

        $this->data = collect($this->record->serviceOrderExecutionChecklistSteps)
            ->mapWithKeys(function (ServiceOrderExecutionChecklistStep $step): array {
                if ($step->data_type === ChecklistStepDataTypeEnum::MultipleChoice->value) {
                    return ['field_' . $step->sequence => json_decode($step->collected_value)];
                }

                if ($step->data_type === ChecklistStepDataTypeEnum::ProgressReport->value) {
                    $collectedValue = json_decode($step->collected_value, true);

                    return ['field_' . $step->sequence => [
                        'date' => $collectedValue['date'] ?? date('Y-m-d'),
                        'value' => $collectedValue['value'] ?? null,
                    ]];
                }

                return ['field_' . $step->sequence => $step->collected_value];
            })
            ->toArray();

        $this->data['service_order_execution_checklist_step_products'] = collect($this->record->serviceOrderExecutionChecklistSteps)
            ->mapWithKeys(fn(ServiceOrderExecutionChecklistStep $step) => [
                'field_' . $step->sequence => $step->serviceOrderExecutionChecklistStepProducts
                    ->map(fn(ServiceOrderExecutionChecklistStepProduct $serviceOrderExecutionChecklistStepProduct): array => [
                        'product_id' => $serviceOrderExecutionChecklistStepProduct->product_id,
                        'origin_warehouse_id' => $serviceOrderExecutionChecklistStepProduct->origin_warehouse_id,
                        'quantity' => $serviceOrderExecutionChecklistStepProduct->quantity,
                    ])
                    ->toArray()
            ])
            ->toArray();

        $this->data['service_order_execution_checklist_step_needs'] = collect($this->record->serviceOrderExecutionChecklistSteps)
            ->mapWithKeys(fn(ServiceOrderExecutionChecklistStep $step) => [
                'field_' . $step->sequence => $step->serviceOrderExecutionChecklistStepNeeds
                    ->map(fn(ServiceOrderExecutionChecklistStepNeed $serviceOrderExecutionChecklistStepNeed): array => [
                        'need_id' => $serviceOrderExecutionChecklistStepNeed->need_id,
                        'need_type' => $serviceOrderExecutionChecklistStepNeed->need_type,
                        'quantity' => $serviceOrderExecutionChecklistStepNeed->quantity,
                    ])
                    ->toArray()
            ])
            ->toArray();

        $this->form->fill($this->data);

        if (!$this->record->started_at) {
            $this->record->update(['started_at' => now()]);
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Voltar')
                ->color('gray')
                ->url(route('filament.app.services.resources.service-orders.index')),
        ];
    }

    public function form(Form $form): Form
    {
        $serviceOrderExecutionChecklistSteps = collect($this->record->serviceOrderExecutionChecklistSteps);

        $steps = $serviceOrderExecutionChecklistSteps
            ->map(function (ServiceOrderExecutionChecklistStep $step): Step {
                $collectedValueField = match ($step->data_type) {
                    ChecklistStepDataTypeEnum::Character->value => TextInput::make("data.field_$step->sequence")
                        ->hiddenLabel()
                        ->maxLength(1),
                    ChecklistStepDataTypeEnum::Date->value => TextInput::make("data.field_$step->sequence")
                        ->hiddenLabel()
                        ->type('date'),
                    ChecklistStepDataTypeEnum::MultipleChoice->value => CheckboxList::make("data.field_$step->sequence")
                        ->hiddenLabel()
                        ->options(
                            $step->serviceOrderExecutionChecklistStepOptions
                                ->mapWithKeys(fn(ServiceOrderExecutionChecklistStepOption $option) => [$option->id => $option->value])
                                ->toArray(),
                        ),
                    ChecklistStepDataTypeEnum::Number->value => TextInput::make("data.field_$step->sequence")
                        ->hiddenLabel()
                        ->numeric(),
                    ChecklistStepDataTypeEnum::ProgressReport->value => Fieldset::make("data.field_$step->sequence")
                        ->label('Medição')
                        ->columnSpan(2)
                        ->schema([
                            DatePicker::make("data.field_$step->sequence.date")
                                ->hiddenLabel()
                                ->format('d/m/Y'),
                            TextInput::make("data.field_$step->sequence.value")
                                ->hiddenLabel()
                                ->numeric(),
                        ]),
                    ChecklistStepDataTypeEnum::SingleChoice->value => Radio::make("data.field_$step->sequence")
                        ->hiddenLabel()
                        ->options(
                            $step->serviceOrderExecutionChecklistStepOptions
                                ->mapWithKeys(fn(ServiceOrderExecutionChecklistStepOption $option) => [$option->id => $option->value])
                                ->toArray(),
                        ),
                    ChecklistStepDataTypeEnum::Text->value => Textarea::make("data.field_$step->sequence")
                        ->hiddenLabel(),
                    ChecklistStepDataTypeEnum::Signature->value => SignaturePad::make("data.field_$step->sequence")
                        ->hiddenLabel(),
                };

                return Step::make($step->checklist_step_name)
                    ->schema([
                        Grid::make($step->data_type === ChecklistStepDataTypeEnum::Signature->value ? 4 : 1)->schema([
                            $collectedValueField,
                        ]),
                        Grid::make(2)->schema([
                            Fieldset::make('Necessidades')
                                ->columnSpan(1)
                                ->visible(fn(): bool => $step->has_needs)
                                ->schema([
                                    Grid::make(1)->schema([
                                        TableRepeater::make("data.service_order_execution_checklist_step_needs.field_$step->sequence")
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->addActionLabel('Adicionar necessidade')
                                            ->headers([
                                                Header::make(__('service_order_execution_checklist_step_needs.forms.fields.need_type'))
                                                    ->width('40%'),
                                                Header::make(__('service_order_execution_checklist_step_needs.forms.fields.need_id'))
                                                    ->width('40%'),
                                                Header::make(__('service_order_execution_checklist_step_needs.forms.fields.quantity'))
                                                    ->width('20%'),
                                            ])
                                            ->schema([
                                                Select::make('need_type')
                                                    ->required()
                                                    ->reactive()
                                                    ->options([
                                                        ServiceType::class => 'Tipo de serviço',
                                                        Product::class => 'Produto',
                                                    ]),
                                                Select::make('need_id')
                                                    ->placeholder(fn(Get $get): string => $get('need_type') === ServiceType::class ? 'Digite o nome de um tipo de serviço' : 'Digite o nome ou o código de um produto')
                                                    ->required()
                                                    ->reactive()
                                                    ->searchable()
                                                    ->getSearchResultsUsing(function (Get $get, ?string $search): array {
                                                        if ($get('need_type') === ServiceType::class) {
                                                            return GetServiceTypeByName::run($search, true)->pluck('name', 'id')->toArray();
                                                        }

                                                        return GetProductsByCodeOrName::run($search, true)->pluck('name', 'id')->toArray();
                                                    })
                                                    ->getOptionLabelUsing(function (Get $get, ?string $state): ?string {
                                                        if ($get('need_type') === ServiceType::class) {
                                                            return ServiceType::find($state)->name ?? '';
                                                        }

                                                        return Product::find($state)->name ?? '';
                                                    }),
                                                TextInput::make('quantity')
                                                    ->required()
                                                    ->numeric(),
                                            ]),
                                    ]),
                                ]),
                            Fieldset::make('Produtos')
                                ->columnSpan(1)
                                ->visible(fn(): bool => $step->has_products)
                                ->schema([
                                    Grid::make(1)->schema([
                                        TableRepeater::make("data.service_order_execution_checklist_step_products.field_$step->sequence")
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->addActionLabel('Adicionar produto utilizado')
                                            ->headers([
                                                Header::make(__('service_order_execution_checklist_step_products.forms.fields.product_id'))
                                                    ->width('40%'),
                                                Header::make(__('service_order_execution_checklist_step_products.forms.fields.origin_warehouse_id'))
                                                    ->width('40%'),
                                                Header::make(__('service_order_execution_checklist_step_products.forms.fields.quantity'))
                                                    ->width('20%'),
                                            ])
                                            ->schema([
                                                Select::make('product_id')
                                                    ->placeholder('Digite o nome de um produto')
                                                    ->required()
                                                    ->reactive()
                                                    ->searchable()
                                                    ->getSearchResultsUsing(fn(string $search): array => GetProductsByCodeOrName::run($search, true)->pluck('name', 'id')->toArray())
                                                    ->getOptionLabelUsing(fn(?string $state): ?string => Product::find($state)->name ?? ''),
                                                Select::make('origin_warehouse_id')
                                                    ->required()
                                                    ->reactive()
                                                    ->options(GetWarehousesByName::run(true)->pluck('name', 'id')->toArray()),
                                                TextInput::make('quantity')
                                                    ->required()
                                                    ->numeric(),
                                            ]),
                                    ]),
                                ]),
                        ]),
                    ])
                    ->afterValidation(fn() => $this->saveCurrentState());
            })
            ->toArray();

        /** @var \App\Models\ServiceOrderExecutionChecklistStep $nextAvailableStep */
        $nextAvailableStep = $serviceOrderExecutionChecklistSteps
            ->filter(fn(ServiceOrderExecutionChecklistStep $step): bool => is_null($step->collected_value))
            ->first();

        return $form->schema([
            Grid::make(1)->schema([
                Wizard::make()
                    ->steps($steps)
                    ->startOnStep($nextAvailableStep->sequence ?? 1)
                    ->submitAction(new HtmlString(Blade::render(
                        <<<BLADE
                        <x-filament::button type="button" wire:click="finishExecution()">Finalizar execução</x-filament::button>
                    BLADE,
                    ))),
            ]),
        ]);
    }

    public function saveCurrentState(): void
    {
        $isIntegrated = ThirdPartyServiceOrder::query()
            ->where('service_order_id', $this->record->id)
            ->exists();

        DB::transaction(function () use ($isIntegrated): void {
            $newStepProducts = [];
            $deletedStepProducts = [];

            foreach ($this->data as $key => $value) {
                if (!str_starts_with($key, 'field_')) {
                    continue;
                }

                $serviceOrderExecutionChecklistStep = $this->updateServiceOrderExecutionChecklistStep($key, $value);

                if (isset($this->data['service_order_execution_checklist_step_products'][$key])) {
                    $this->handleServiceOrderExecutionChecklistStepProducts($key, $serviceOrderExecutionChecklistStep);
                }

                if (isset($this->data['service_order_execution_checklist_step_needs'][$key])) {
                    $this->handleServiceOrderExecutionChecklistStepNeeds($key, $serviceOrderExecutionChecklistStep);
                }
            }

            if ($isIntegrated) {
                UpdateServiceOrderInErpFlex::run($this->record, $newStepProducts, $deletedStepProducts, auth()->id());
            }
        });
    }

    private function updateServiceOrderExecutionChecklistStep(string $key, mixed $value): ServiceOrderExecutionChecklistStep
    {
        /** @var \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep */
        $serviceOrderExecutionChecklistStep = $this->record->serviceOrderExecutionChecklistSteps()
            ->where('sequence', substr($key, 6))
            ->first();

        if ($serviceOrderExecutionChecklistStep->data_type !== ChecklistStepDataTypeEnum::MultipleChoice->value) {
            $serviceOrderExecutionChecklistStep->update(['collected_value' => $value]);
            return $serviceOrderExecutionChecklistStep;
        }

        $serviceOrderExecutionChecklistStep->update(['collected_value' => array_values($value)]);

        return $serviceOrderExecutionChecklistStep;
    }

    private function handleServiceOrderExecutionChecklistStepProducts(string $key, ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep): void
    {
        $itemsArray = collect($this->data['service_order_execution_checklist_step_products'][$key]);

        foreach ($itemsArray as $item) {
            /** @var \App\Models\ServiceOrderExecutionChecklistStepProduct|null $serviceOrderExecutionChecklistStepProduct */
            $serviceOrderExecutionChecklistStepProduct = GetServiceOrderExecutionChecklistStepProductByServiceOrderExecutionChecklistStepIdAndProductId::run(
                $serviceOrderExecutionChecklistStep->id,
                $item['product_id'],
            );

            if ($serviceOrderExecutionChecklistStepProduct) {
                $deletedStepProducts[] = [
                    'product_id' => $serviceOrderExecutionChecklistStepProduct->product_id,
                    'quantity' => $serviceOrderExecutionChecklistStepProduct->quantity
                ];

                UpdateStockMovement::run($serviceOrderExecutionChecklistStepProduct->stockMovement, [
                    'item_id' => $item['product_id'],
                    'item_type' => Product::class,
                    'quantity' => $item['quantity'],
                    'origin_warehouse_id' => $item['origin_warehouse_id'],
                ]);

                $serviceOrderExecutionChecklistStepProduct->update($item);

                $newStepProducts[] = [
                    'product_id' => $item['product_id'],
                    'quantity' => (float) $item['quantity'],
                ];
            } else {
                /** @var \App\Models\ServiceOrderExecutionChecklistStepProduct $serviceOrderExecutionChecklistStepProduct */
                $serviceOrderExecutionChecklistStepProduct = CreateServiceOrderExecutionChecklistStepProduct::run($serviceOrderExecutionChecklistStep, $item);

                $newStepProducts[] = [
                    'product_id' => $item['product_id'],
                    'quantity' => (float) $item['quantity'],
                ];
            }
        }
    }

    private function handleServiceOrderExecutionChecklistStepNeeds(string $key, ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep): void
    {
        $itemsArray = collect($this->data['service_order_execution_checklist_step_needs'][$key]);

        foreach ($itemsArray as $item) {
            /** @var \App\Models\ServiceOrderExecutionChecklistStepNeed|null $serviceOrderExecutionChecklistStepNeed */
            $serviceOrderExecutionChecklistStepNeed = GetServiceOrderExecutionChecklistStepNeedByServiceOrderExecutionChecklistStepIdNeedIdAndNeedType::run(
                $serviceOrderExecutionChecklistStep->id,
                $item['need_id'],
                $item['need_type'],
            );

            if ($serviceOrderExecutionChecklistStepNeed) {
                $serviceOrderExecutionChecklistStepNeed->update($item);
            } else {
                /** @var \App\Models\ServiceOrderExecutionChecklistStepNeed $serviceOrderExecutionChecklistStepNeed */
                $serviceOrderExecutionChecklistStepNeed = $serviceOrderExecutionChecklistStep->serviceOrderExecutionChecklistStepNeeds()->create($item);
            }
        }
    }

    public function finishExecution(): mixed
    {
        $this->saveCurrentState();
        FinishServiceOrderExecution::run($this->record, $this->data);
        success_notification(__('service_orders.responses.finish_execution.success'))->send();
        return redirect()->route('filament.app.services.resources.service-orders.index');
    }
}
