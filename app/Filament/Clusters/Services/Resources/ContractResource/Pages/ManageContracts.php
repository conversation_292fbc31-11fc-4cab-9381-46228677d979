<?php

namespace App\Filament\Clusters\Services\Resources\ContractResource\Pages;

use App\Filament\Clusters\Services\Resources\ContractResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;

class ManageContracts extends ManageRecords
{
    protected static string $resource = ContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->modalWidth(MaxWidth::Full)
                ->successNotification(success_notification(__('contracts.responses.create.success'))),
        ];
    }
}
