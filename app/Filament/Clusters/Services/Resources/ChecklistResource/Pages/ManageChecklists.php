<?php

namespace App\Filament\Clusters\Services\Resources\ChecklistResource\Pages;

use App\Actions\Checklist\CreateChecklist;
use App\Filament\Clusters\Services\Resources\ChecklistResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Throwable;

class ManageChecklists extends ManageRecords
{
    protected static string $resource = ChecklistResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->action(function (array $data) {
                    try {
                        CreateChecklist::run($data);
                        success_notification(__('checklists.responses.create.success'))->send();
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }
}
