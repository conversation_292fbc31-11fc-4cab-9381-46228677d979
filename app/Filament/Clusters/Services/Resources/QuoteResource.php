<?php

namespace App\Filament\Clusters\Services\Resources;

use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\Product\Queries\GetProductsByCodeOrName;
use App\Actions\Quote\ApproveQuote;
use App\Actions\Quote\DeleteQuote;
use App\Actions\Quote\EditQuote;
use App\Actions\Quote\Integrations\ErpFlex\CreateQuoteInErpFlex;
use App\Actions\Quote\RejectQuote;
use App\Actions\ServiceType\Queries\GetServiceTypeByName;
use App\Core\Filament\Filters\TableDateFilter;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\QuoteStatusEnum;
use App\Filament\Clusters\Services;
use App\Filament\Clusters\Services\Resources\QuoteResource\Pages;
use App\Models\ErpFlexParameter;
use App\Models\Product;
use App\Models\Quote;
use App\Models\QuoteItem;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderExecutionChecklistStepNeed;
use App\Models\ServiceType;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\RawJs;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

class QuoteResource extends Resource
{
    protected static ?string $model = Quote::class;
    protected static ?string $modelLabel = 'orçamento';
    protected static ?string $navigationIcon = 'heroicon-o-document-currency-dollar';
    protected static ?string $cluster = Services::class;
    protected static ?int $navigationSort = 4;

    public static ?ErpFlexParameter $erpFlexParameter = null;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            Select::make('customer_id')
                                ->label(__('quotes.forms.fields.customer_id'))
                                ->relationship('customer', 'name')
                                ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                                ->columnSpan(2)
                                ->searchable(),
                            DatePicker::make('issued_at')
                                ->label(__('quotes.forms.fields.issued_at'))
                                ->required()
                                ->default(now()->format('Y-m-d')),
                            Select::make('protocol_id')
                                ->label(__('quotes.forms.fields.protocol_id'))
                                ->relationship('protocol', 'code')
                                ->disabled(),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('discount_percentage')
                                ->label(__('quotes.forms.fields.discount_percentage'))
                                ->mask(RawJs::make(<<<'JS'
                                    $money($input, ',', '', 2) + '%'
                                JS))
                                ->lazy()
                                ->afterStateUpdated(function (?string $state, Set $set, Get $get): void {
                                    $discountPercentage = unmask_percentage($state ?? '0');
                                    $quoteItems = $get('quote_items') ?? [];

                                    // Calculate subtotal from approved items
                                    $subtotal = collect($quoteItems)
                                        ->filter(fn(array $item): bool => $item['approved'] ?? false)
                                        ->sum(fn(array $item): float => unmask_money($item['total_amount'] ?? '0'));

                                    // Calculate discount amount
                                    $discountAmount = $subtotal * ($discountPercentage / 100);

                                    // Calculate final amount
                                    $finalAmount = $subtotal - $discountAmount;

                                    $set('discount_subtotal', format_money($discountAmount));
                                    $set('amount', format_money($finalAmount));
                                })
                                ->columnSpan(1),
                            TextInput::make('discount_subtotal')
                                ->label(__('quotes.forms.fields.discount_subtotal'))
                                ->readOnly()
                                ->columnSpan(1),
                            TextInput::make('amount')
                                ->label('Valor Total')
                                ->disabled()
                                ->columnSpan(1)
                                ->columnStart(4),
                        ]),
                        Grid::make(1)->schema([
                            Textarea::make('general_conditions')
                                ->label(__('quotes.forms.fields.general_conditions'))
                                ->rows(3),
                        ]),
                    ]),
                    Tab::make('Itens')->schema([
                        Grid::make(1)->schema([
                            Repeater::make('quote_items')
                                ->hiddenLabel()
                                ->defaultItems(1)
                                ->addActionLabel('Adicionar item')
                                ->collapsible()
                                ->collapsed()
                                ->itemLabel(function (array $state): ?string {
                                    if (is_null($state['item_id'])) {
                                        return '';
                                    }

                                    $label = '';

                                    if ($state['service_order_code'] && $state['service_order_code'] !== '') {
                                        $label .= $state['service_order_code'] . ' - ';
                                    }

                                    if ($state['type'] === Product::class) {
                                        $label .= Product::find($state['item_id'])->name ?? '';
                                        $label .= ' (Produto)';
                                    } else {
                                        $label .= ServiceType::find($state['item_id'])->name ?? '';
                                        $label .= ' (Serviço)';
                                    }

                                    return $label;
                                })
                                ->schema([
                                    Hidden::make('id'),
                                    Hidden::make('service_order_id'),
                                    Hidden::make('service_order_code'),
                                    Hidden::make('service_order_execution_checklist_step_id'),
                                    Hidden::make('service_order_execution_checklist_step_need_id'),
                                    Hidden::make('protocol_code'),
                                    Grid::make(4)->schema([
                                        Select::make('type')
                                            ->label(__('quote_items.forms.fields.type'))
                                            ->columnSpan(1)
                                            ->options([
                                                Product::class => 'Produto',
                                                ServiceType::class => 'Serviço',
                                            ])
                                            ->reactive()
                                            ->afterStateUpdated(function (Set $set): void {
                                                $set('item_id', null);
                                                $set('quantity', null);
                                                $set('unit_amount', null);
                                                $set('total_amount', null);
                                            }),
                                        Select::make('item_id')
                                            ->label(__('quote_items.forms.fields.item_id'))
                                            ->columnSpan(2)
                                            ->lazy()
                                            ->searchable()
                                            ->getSearchResultsUsing(function (Get $get, ?string $search): array {
                                                return $get('type') === Product::class
                                                    ? GetProductsByCodeOrName::run($search, true)->pluck('name', 'id')->toArray()
                                                    : GetServiceTypeByName::run($search, true)->pluck('name', 'id')->toArray();
                                            })
                                            ->getOptionLabelUsing(function (Get $get, ?string $state): ?string {
                                                return $get('type') === Product::class
                                                    ? (Product::find($state)->name ?? '')
                                                    : (ServiceType::find($state)->name ?? '');
                                            })
                                            ->afterStateUpdated(function (?string $state, Set $set, Get $get): void {
                                                if (!$state) {
                                                    $set('quantity', null);
                                                    $set('unit_amount', null);
                                                    $set('total_amount', null);
                                                    return;
                                                }

                                                if ($get('type') === Product::class) {
                                                    /** @var \App\Models\Product $product */
                                                    $product = Product::find($state);

                                                    $set('unit_amount', $product->friendly_default_amount);
                                                    $set('total_amount', format_money($product->default_amount * (int) $get('quantity')));
                                                    return;
                                                }

                                                /** @var \App\Models\ServiceType $serviceType */
                                                $serviceType = ServiceType::find($state);

                                                $set('unit_amount', format_money($serviceType->default_amount));
                                                $set('total_amount', format_money($serviceType->default_amount * (int) $get('quantity')));
                                            }),
                                        TextInput::make('quantity')
                                            ->label(__('quote_items.forms.fields.quantity'))
                                            ->numeric()
                                            ->lazy()
                                            ->afterStateUpdated(function (?string $state, Set $set, Get $get): void {
                                                if (!$state) {
                                                    $set('total_amount', null);
                                                    return;
                                                }

                                                $set('total_amount', format_money(unmask_money($get('unit_amount')) * (int) $state));
                                            })
                                            ->afterStateHydrated(function (?string $state, Set $set, Get $get): void {
                                                if (!$state) {
                                                    $set('total_amount', null);
                                                    return;
                                                }

                                                $set('total_amount', format_money(unmask_money($get('unit_amount')) * (int) $state));
                                            }),
                                    ]),
                                    Grid::make(4)->schema([
                                        TextInput::make('unit_amount')
                                            ->label(__('quote_items.forms.fields.unit_amount'))
                                            ->lazy()
                                            ->mask(fn(): RawJs => RawJs::make(<<<'JS'
                                                'R$ ' + $money($input, ',', '', 2)
                                            JS))
                                            ->afterStateUpdated(function (?string $state, Set $set, Get $get): void {
                                                if (!$state) {
                                                    $set('total_amount', null);
                                                    return;
                                                }

                                                $set('total_amount', format_money(unmask_money($get('unit_amount')) * (int) $get('quantity')));
                                            })
                                            ->afterStateHydrated(function (?string $state, Set $set, Get $get): void {
                                                if (!$state) {
                                                    $set('total_amount', null);
                                                    return;
                                                }

                                                $set('total_amount', format_money(unmask_money($get('unit_amount')) * (int) $get('quantity')));
                                            }),
                                        TextInput::make('discount_percentage')
                                            ->label(__('quote_items.forms.fields.discount_percentage'))
                                            ->mask(RawJs::make(<<<'JS'
                                                $money($input, ',', '', 2) + '%'
                                            JS))
                                            ->lazy()
                                            ->afterStateUpdated(function (?string $state, Set $set, Get $get): void {
                                                self::recalculateItemDiscount($set, $get);
                                                self::recalculateQuoteAmounts($set, $get);
                                            })
                                            ->columnSpan(1),
                                        TextInput::make('discount_subtotal')
                                            ->label(__('quote_items.forms.fields.discount_subtotal'))
                                            ->readOnly()
                                            ->columnSpan(1),
                                        TextInput::make('total_amount')
                                            ->label(__('quote_items.forms.fields.total_amount'))
                                            ->readOnly()
                                            ->afterStateHydrated(function (?string $state, Set $set, Get $get): void {
                                                self::recalculateItemDiscount($set, $get);
                                                self::recalculateQuoteAmounts($set, $get);
                                            }),
                                    ]),
                                    Grid::make(1)->schema([
                                        Toggle::make('approved')
                                            ->label('Aprovado')
                                            ->default(false)
                                            ->afterStateUpdated(function (?bool $state, Set $set, Get $get): void {
                                                self::recalculateQuoteAmounts($set, $get);
                                            })
                                            ->columnSpan(1),
                                    ]),
                                    Grid::make(1)->schema([
                                        Textarea::make('additional_info')
                                            ->label(__('quote_items.forms.fields.additional_info'))
                                            ->rows(3),
                                    ]),
                                ]),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        self::$erpFlexParameter = GetErpFlexParameter::run();

        return $table
            ->modifyQueryUsing(function (Builder $query): Builder {
                return $query->orderByDesc('id');
            })
            ->columns([
                TextColumn::make('id')
                    ->label(__('quotes.forms.fields.id')),
                TextColumn::make('protocol.code')
                    ->label(__('quotes.forms.fields.protocol_id')),
                TextColumn::make('customer.name')
                    ->label(__('quotes.forms.fields.customer_id')),
                TextColumn::make('issued_at')
                    ->label(__('quotes.forms.fields.issued_at'))
                    ->formatStateUsing(fn(Quote $quote): string => format_date($quote->issued_at)),
                TextColumn::make('status')
                    ->label(__('quotes.forms.fields.status'))
                    ->formatStateUsing(fn(Quote $quote): string => $quote->friendly_status)
                    ->badge()
                    ->color(fn(Quote $quote): string => QuoteStatusEnum::getTableColors()[$quote->status]),
            ])
            ->filters([
                TableTextFilter::buildLike('quotes', 'id'),
                TableTextFilter::buildRelation('quotes', 'customer_id', 'customer', 'name'),
                TableDateFilter::buildFrom('quotes', 'issued_at', 'issued_at_from'),
                TableDateFilter::buildTo('quotes', 'issued_at', 'issued_at_to'),
                TableSelectFilter::build('quotes', 'status', QuoteStatusEnum::getTranslated()),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->modalWidth(MaxWidth::Full)
                        ->mutateRecordDataUsing(function (Quote $quote, array $data): array {
                            return array_merge($data, [
                                'discount_percentage' => format_percentage($quote->discount_percentage),
                                'discount_subtotal' => $quote->friendly_discount_subtotal,
                                'amount' => $quote->friendly_amount,
                                'quote_items' => $quote->quoteItems
                                    ->map(fn(QuoteItem $quoteItem): array => [
                                        'id' => $quoteItem->id,
                                        'type' => $quoteItem->product_id
                                            ? Product::class
                                            : ServiceType::class,
                                        'item_id' => $quoteItem->product_id ?? $quoteItem->service_type_id,
                                        'service_order_id' => $quoteItem->service_order_id,
                                        'service_order_execution_checklist_step_id' => $quoteItem->service_order_execution_checklist_step_id,
                                        'service_order_execution_checklist_step_need_id' => $quoteItem->service_order_execution_checklist_step_need_id,
                                        'protocol_code' => $quoteItem->serviceOrder?->protocol?->code ?? '',
                                        'service_order_code' => $quoteItem->serviceOrder?->code ?? '',
                                        'quantity' => $quoteItem->quantity,
                                        'unit_amount' => $quoteItem->friendly_unit_amount,
                                        'total_amount' => $quoteItem->friendly_total_amount,
                                        'additional_info' => $quoteItem->additional_info,
                                        'approved' => $quoteItem->approved,
                                        'discount_percentage' => $quoteItem->friendly_discount_percentage,
                                        'discount_subtotal' => $quoteItem->friendly_discount_subtotal,
                                    ])
                                    ->toArray(),
                            ]);
                        }),
                    EditAction::make()
                        ->modalWidth(MaxWidth::Full)
                        ->visible(fn(Quote $quote): bool => $quote->status === QuoteStatusEnum::Pending->value)
                        ->mutateRecordDataUsing(function (Quote $quote, array $data): array {
                            return array_merge($data, [
                                'discount_percentage' => format_percentage($quote->discount_percentage),
                                'discount_subtotal' => $quote->friendly_discount_subtotal,
                                'amount' => $quote->friendly_amount,
                                'quote_items' => $quote->quoteItems
                                    ->map(fn(QuoteItem $quoteItem): array => [
                                        'id' => $quoteItem->id,
                                        'type' => $quoteItem->product_id
                                            ? Product::class
                                            : ServiceType::class,
                                        'item_id' => $quoteItem->product_id ?? $quoteItem->service_type_id,
                                        'service_order_id' => $quoteItem->service_order_id,
                                        'service_order_execution_checklist_step_id' => $quoteItem->service_order_execution_checklist_step_id,
                                        'service_order_execution_checklist_step_need_id' => $quoteItem->service_order_execution_checklist_step_need_id,
                                        'protocol_code' => $quoteItem->serviceOrder?->protocol?->code ?? '',
                                        'service_order_code' => $quoteItem->serviceOrder?->code ?? '',
                                        'quantity' => $quoteItem->quantity,
                                        'unit_amount' => $quoteItem->friendly_unit_amount,
                                        'total_amount' => $quoteItem->friendly_total_amount,
                                        'additional_info' => $quoteItem->additional_info,
                                        'approved' => $quoteItem->approved,
                                        'discount_percentage' => $quoteItem->friendly_discount_percentage,
                                        'discount_subtotal' => $quoteItem->friendly_discount_subtotal,
                                    ])
                                    ->toArray(),
                            ]);
                        })
                        ->using(fn(Quote $quote, array $data) => EditQuote::run($quote, $data))
                        ->successNotification(success_notification(__('quotes.responses.update.success'))),
                    Action::make('approve')
                        ->label('Aprovar')
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check')
                        ->visible(fn(Quote $quote): bool => $quote->status === QuoteStatusEnum::Pending->value)
                        ->action(function (Quote $quote): void {
                            try {
                                ApproveQuote::run($quote);
                                success_notification(__('quotes.responses.approve.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    Action::make('reject')
                        ->label('Rejeitar')
                        ->requiresConfirmation()
                        ->icon('heroicon-o-x-circle')
                        ->visible(fn(Quote $quote): bool => $quote->status === QuoteStatusEnum::Pending->value)
                        ->action(function (Quote $quote): void {
                            try {
                                RejectQuote::run($quote);
                                success_notification(__('quotes.responses.reject.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    Action::make('generate_pdf')
                        ->label('Gerar PDF')
                        ->icon('heroicon-o-document')
                        ->color('gray')
                        ->url(fn(Quote $quote): string => route('filament.app.services.pages.generate-quote-pdf', $quote->token))
                        ->openUrlInNewTab(),
                    Action::make('integrate_with_erp_flex')
                        ->label('Integrar com ERPFlex')
                        ->requiresConfirmation()
                        ->icon('heroicon-o-arrow-up-tray')
                        ->visible(fn(Quote $quote): bool => !is_null(self::$erpFlexParameter) && $quote->status === QuoteStatusEnum::Approved->value)
                        ->action(function (Quote $quote): void {
                            try {
                                $quote = CreateQuoteInErpFlex::run($quote, auth()->id(), true);

                                if ($quote) {
                                    success_notification(__('quotes.responses.create_in_erp_flex.success'))->send();
                                }
                            } catch (Throwable $th) {
                                error($th);
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    DeleteAction::make()
                        ->action(function (Quote $quote): void {
                            try {
                                DeleteQuote::run($quote);
                                success_notification(__('quotes.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem orçamentos')
            ->emptyStateDescription('Assim que você cadastrar seus orçamentos, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageQuotes::route('/'),
        ];
    }

    protected static function runAdvancedServiceOrderSearch(Get $get, Set $set): void
    {
        $serviceOrderLine = ServiceOrder::query()
            ->with([
                'customer:id,name',
                'equipment:id,name',
                'serviceType:id,name',
            ])
            ->when(!is_null($get('service_order_search_service_order_code')) && $get('service_order_search_service_order_code') !== '', function (Builder $query) use ($get): Builder {
                return $query->where(function (Builder $query) use ($get): Builder {
                    return $query->where('code', 'like', "%{$get('service_order_search_service_order_code')}%");
                });
            })
            ->get()
            ->map(fn(ServiceOrder $serviceOrder): array => [
                'advanced_search_results_service_order_select' => false,
                'advanced_search_results_service_order_id' => $serviceOrder->id,
                'advanced_search_results_service_order_execution_checklist_step_need_id' => null,
                'service_order_code' => $serviceOrder->code,
                'customer_name' => $serviceOrder->customer->name,
                'equipment_name' => $serviceOrder->equipment->name,
                'service_type_name' => $serviceOrder->serviceType->name,
                'need_name' => null,
            ])
            ->toArray();

        $needs = ServiceOrderExecutionChecklistStepNeed::query()
            ->with([
                'need:id,name',
                'serviceOrderExecutionChecklistStep:id,service_order_id',
                'serviceOrderExecutionChecklistStep.serviceOrder:id,code,customer_id,equipment_id,service_type_id',
                'serviceOrderExecutionChecklistStep.serviceOrder.customer:id,name',
                'serviceOrderExecutionChecklistStep.serviceOrder.equipment:id,name',
                'serviceOrderExecutionChecklistStep.serviceOrder.serviceType:id,name',
            ])
            ->when(!is_null($get('service_order_search_service_order_code')) && $get('service_order_search_service_order_code') !== '', function (Builder $query) use ($get): Builder {
                return $query->where(function (Builder $query) use ($get): Builder {
                    return $query->whereRelation('serviceOrderExecutionChecklistStep.serviceOrder', 'code', 'like', "%{$get('service_order_search_service_order_code')}%");
                });
            })
            ->get()
            ->map(fn(ServiceOrderExecutionChecklistStepNeed $serviceOrderExecutionChecklistStepNeed): array => [
                'advanced_search_results_service_order_select' => false,
                'advanced_search_results_service_order_id' => $serviceOrderExecutionChecklistStepNeed->serviceOrderExecutionChecklistStep->serviceOrder->id,
                'advanced_search_results_service_order_execution_checklist_step_need_id' => $serviceOrderExecutionChecklistStepNeed->id,
                'service_order_code' => $serviceOrderExecutionChecklistStepNeed->serviceOrderExecutionChecklistStep->serviceOrder->code,
                'customer_name' => $serviceOrderExecutionChecklistStepNeed->serviceOrderExecutionChecklistStep->serviceOrder->customer->name,
                'equipment_name' => null,
                'service_type_name' => null,
                'need_name' => $serviceOrderExecutionChecklistStepNeed->need->name,
            ])
            ->toArray();

        $set('advanced_search_service_orders', array_merge($serviceOrderLine, $needs));
    }

    private static function recalculateItemDiscount(Set $set, Get $get): void
    {
        $discountPercentage = unmask_percentage($get('discount_percentage') ?? 0);
        $totalAmount = unmask_money(($get('quantity') ?? 0) * (unmask_money($get('unit_amount')) ?? 0) ?? 0);
        $discountAmount = $totalAmount * ($discountPercentage / 100);

        $set('discount_subtotal', format_money($discountAmount));
        $set('total_amount', format_money($totalAmount - $discountAmount));
    }

    private static function recalculateQuoteAmounts(Set $set, Get $get): void
    {
        $quoteDiscountPercentage = unmask_percentage($get('../../discount_percentage') ?? 0);
        $quoteItems = $get('../../quote_items') ?? [];

        $subtotal = collect($quoteItems)
            ->filter(fn(array $item): bool => $item['approved'] ?? false)
            ->sum(fn(array $item): float => unmask_money($item['total_amount'] ?? 0));

        $quoteDiscountAmount = $subtotal * ($quoteDiscountPercentage / 100);

        $finalAmount = $subtotal - $quoteDiscountAmount;

        $set('../../amount', format_money($finalAmount));
    }
}
