<?php

namespace App\Filament\Clusters\Services\Resources;

use App\Actions\Checklist\Queries\GetActiveChecklistsByName;
use App\Actions\Contract\DeleteContract;
use App\Actions\Contract\GenerateContractServiceOrders;
use App\Actions\Contract\Integrations\ErpFlex\CreateContractInErpFlex;
use App\Actions\Customer\Queries\GetCustomersByNameTradingNameOrTaxIdentificationNumber;
use App\Actions\Equipment\Queries\GetEquipmentBySerialNumberOrName;
use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\ServiceType\Queries\GetServiceTypeByName;
use App\Core\Filament\Filters\TableDateFilter;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ContractItemPeriodEnum;
use App\Enums\ContractStatusEnum;
use App\Enums\ContractTypeEnum;
use App\Filament\Clusters\Services;
use App\Filament\Clusters\Services\Resources\ContractResource\Pages;
use App\Models\Contract;
use App\Models\ErpFlexParameter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Throwable;

class ContractResource extends Resource
{
    protected static ?string $model = Contract::class;
    protected static ?string $modelLabel = 'contrato';
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $cluster = Services::class;
    protected static ?int $navigationSort = 3;

    public static ?ErpFlexParameter $erpFlexParameter = null;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('code')
                                ->label(__('contracts.forms.fields.code'))
                                ->required(),
                            Select::make('customer_id')
                                ->label(__('contracts.forms.fields.customer_id'))
                                ->required()
                                ->columnSpan(2)
                                ->searchable()
                                ->lazy()
                                ->getSearchResultsUsing(fn(string $search): array => GetCustomersByNameTradingNameOrTaxIdentificationNumber::run($search)->pluck('name', 'id')->toArray()),
                            Select::make('type')
                                ->label(__('contracts.forms.fields.type'))
                                ->required()
                                ->options(ContractTypeEnum::getTranslated()),
                        ]),
                        Grid::make(4)->schema([
                            DatePicker::make('started_at')
                                ->label(__('contracts.forms.fields.started_at'))
                                ->required(),
                            DatePicker::make('ended_at')
                                ->label(__('contracts.forms.fields.ended_at')),
                            DatePicker::make('signed_at')
                                ->label(__('contracts.forms.fields.signed_at')),
                        ]),
                    ]),
                    Tab::make('Itens')->schema([
                        Grid::make(1)->schema([
                            Repeater::make('contract_items')
                                ->hiddenLabel()
                                ->relationship('contractItems')
                                ->addActionLabel('Adicionar item')
                                ->collapsible()
                                ->schema([
                                    Grid::make(4)->schema([
                                        Select::make('equipment_id')
                                            ->label(__('contract_items.forms.fields.equipment_id'))
                                            ->columnSpan(2)
                                            ->relationship('equipment', 'name')
                                            ->required()
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetEquipmentBySerialNumberOrName::run($search)->pluck('name', 'id')->toArray()),
                                        Select::make('service_type_id')
                                            ->label(__('contract_items.forms.fields.service_type_id'))
                                            ->relationship('serviceType', 'name')
                                            ->required()
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetServiceTypeByName::run($search)->pluck('name', 'id')->toArray()),
                                        Select::make('checklist_id')
                                            ->label(__('contract_items.forms.fields.checklist_id'))
                                            ->relationship('checklist', 'name')
                                            ->required()
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetActiveChecklistsByName::run($search)->pluck('name', 'id')->toArray()),
                                    ]),
                                    Grid::make(4)->schema([
                                        TextInput::make('interval')
                                            ->label(__('contract_items.forms.fields.interval'))
                                            ->required(),
                                        Select::make('period')
                                            ->label(__('contract_items.forms.fields.period'))
                                            ->required()
                                            ->options(ContractItemPeriodEnum::getTranslated()),
                                        DatePicker::make('started_at')
                                            ->label(__('contract_items.forms.fields.started_at'))
                                            ->required(),
                                        DatePicker::make('ended_at')
                                            ->label(__('contract_items.forms.fields.ended_at'))
                                            ->required(),
                                    ]),
                                    Grid::make(1)->schema([
                                        Textarea::make('additional_info')
                                            ->label(__('contract_items.forms.fields.additional_info'))
                                            ->rows(3),
                                    ]),
                                    Grid::make(1)->schema([
                                        Toggle::make('generate_service_order_for_starting_date')
                                            ->label(__('contract_items.forms.fields.generate_service_order_for_starting_date'))
                                            ->default(false),
                                    ])
                                ]),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        self::$erpFlexParameter = GetErpFlexParameter::run();

        return $table
            ->columns([
                TextColumn::make('code')
                    ->label(__('contracts.forms.fields.code'))
                    ->sortable(),
                TextColumn::make('customer.name')
                    ->label(__('contracts.forms.fields.customer_id'))
                    ->sortable(),
                TextColumn::make('type')
                    ->label(__('contracts.forms.fields.type'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => $contract->friendly_type),
                TextColumn::make('started_at')
                    ->label(__('contracts.forms.fields.started_at'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => format_date($contract->started_at)),
                TextColumn::make('ended_at')
                    ->label(__('contracts.forms.fields.ended_at'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => format_date($contract->ended_at)),
                TextColumn::make('created_at')
                    ->label(__('contracts.forms.fields.created_at'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => format_date($contract->created_at)),
                TextColumn::make('status')
                    ->label(__('contracts.forms.fields.status'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => $contract->friendly_status)
                    ->badge()
                    ->color(fn(Contract $contract): string => ContractStatusEnum::getTableColors()[$contract->status]),
            ])
            ->filters([
                TableTextFilter::buildLike('contracts', 'code'),
                TableTextFilter::buildRelation('contracts', 'customer_id', 'customer', 'name'),
                TableSelectFilter::build('contracts', 'type', ContractTypeEnum::getTranslated()),
                TableDateFilter::buildFrom('contracts', 'started_at', 'started_at_from', false),
                TableDateFilter::buildTo('contracts', 'started_at', 'started_at_to', false),
                TableDateFilter::buildFrom('contracts', 'ended_at', 'ended_at_from', false),
                TableDateFilter::buildTo('contracts', 'ended_at', 'ended_at_to', false),
                TableDateFilter::buildFrom('contracts', 'created_at', 'created_at_from', false),
                TableDateFilter::buildTo('contracts', 'created_at', 'created_at_to', false),
                TableSelectFilter::build('contracts', 'status', ContractStatusEnum::getTranslated()),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->modalWidth(MaxWidth::Full),
                    EditAction::make()
                        ->modalWidth(MaxWidth::Full)
                        ->successNotification(success_notification(__('contracts.responses.update.success'))),
                    Action::make('generate_service_orders')
                        ->label('Gerar ordens de serviço')
                        ->icon('heroicon-o-document')
                        ->requiresConfirmation()
                        ->action(function (Contract $contract): void {
                            try {
                                GenerateContractServiceOrders::run($contract);
                                success_notification(__('contracts.responses.generate_service_orders.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    Action::make('integrate_with_erp_flex')
                        ->label('Integrar com ERPFlex')
                        ->requiresConfirmation()
                        ->icon('heroicon-o-arrow-up-tray')
                        ->visible(fn(): bool => !is_null(self::$erpFlexParameter))
                        ->action(function (Contract $contract): void {
                            try {
                                $contract = CreateContractInErpFlex::run($contract, auth()->id(), true);

                                if ($contract) {
                                    success_notification(__('contracts.responses.create_in_erp_flex.success'))->send();
                                }
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    DeleteAction::make()
                        ->using(function (Contract $contract): void {
                            try {
                                DeleteContract::run($contract);
                                success_notification(__('contracts.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem contratos')
            ->emptyStateDescription('Assim que você cadastrar seus contratos, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageContracts::route('/'),
        ];
    }
}
