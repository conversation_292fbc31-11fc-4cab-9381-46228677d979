<?php

namespace App\Filament\Clusters\Services\Resources;

use App\Actions\Checklist\Queries\GetActiveChecklistsByName;
use App\Actions\Customer\Queries\GetCustomersByNameTradingNameOrTaxIdentificationNumber;
use App\Actions\Quote\GenerateServiceOrdersQuote;
use App\Actions\ServiceOrder\AttachChecklistToServiceOrder;
use App\Actions\ServiceOrder\CancelServiceOrder;
use App\Actions\ServiceOrder\DeleteServiceOrder;
use App\Actions\ServiceOrder\ExecuteServiceOrder;
use App\Actions\ServiceOrder\ReopenServiceOrder;
use App\Actions\ServiceOrder\SendServiceOrdersEmail;
use App\Actions\ServiceOrder\SendServiceOrderToQuote;
use App\Actions\ServiceType\Queries\GetServiceTypes;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ServiceOrderStatusEnum;
use App\Filament\Clusters\Services;
use App\Filament\Clusters\Services\Resources\ServiceOrderResource\Pages;
use App\Models\Permission;
use App\Models\ServiceOrder;
use App\Models\ServiceType;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;
use Throwable;

class ServiceOrderResource extends Resource
{
    protected static ?string $model = ServiceOrder::class;
    protected static ?string $modelLabel = 'ordem de serviço';
    protected static ?string $pluralModelLabel = 'ordens de serviço';
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $cluster = Services::class;
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('code')
                    ->label(__('service_orders.forms.fields.code'))
                    ->required(),
                TextInput::make('protocol_id')
                    ->label('Protocolo')
                    ->disabled()
                    ->columnStart(3)
                    ->hiddenOn('create')
                    ->formatStateUsing(fn(?ServiceOrder $serviceOrder): string => $serviceOrder->protocol?->code ?? ''),
                TextInput::make('ticket_id')
                    ->label('Chamado')
                    ->disabled()
                    ->columnStart(4)
                    ->hiddenOn('create'),
            ]),
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('general')
                        ->label('Geral')
                        ->schema([
                            Grid::make(1)->schema([
                                Select::make('customer_id')
                                    ->label(__('service_orders.forms.fields.customer_id'))
                                    ->relationship('customer', 'name')
                                    ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                                    ->reactive()
                                    ->searchable()
                                    ->getSearchResultsUsing(fn(string $search): array => GetCustomersByNameTradingNameOrTaxIdentificationNumber::run($search, true)->pluck('name', 'id')->toArray()),
                            ]),
                            Grid::make(4)->schema([
                                Select::make('equipment_id')
                                    ->label(__('service_orders.forms.fields.equipment_id'))
                                    ->relationship('equipment', 'name')
                                    ->reactive()
                                    ->searchable()
                                    ->columnSpan(3),
                                Select::make('service_type_id')
                                    ->label(__('service_orders.forms.fields.service_type_id'))
                                    ->relationship('serviceType', 'name')
                                    ->reactive()
                                    ->searchable()
                                    ->afterStateUpdated(function (?string $state, Set $set): void {
                                        if (!$state) {
                                            $set('estimated_duration_in_minutes', null);
                                            return;
                                        }

                                        $set('estimated_duration_in_minutes', ServiceType::find($state)->estimated_duration_in_minutes);
                                    }),
                            ]),
                            Grid::make(1)->schema([
                                Textarea::make('description')
                                    ->label(__('service_orders.forms.fields.description'))
                                    ->rows(3),
                            ]),
                            Grid::make(4)->schema([
                                TextInput::make('estimated_duration_in_minutes')
                                    ->label(__('service_orders.forms.fields.estimated_duration_in_minutes'))
                                    ->required(),
                                DateTimePicker::make('scheduled_to')
                                    ->label(__('service_orders.forms.fields.scheduled_to'))
                                    ->displayFormat('d/m/Y H:i'),
                                Select::make('employee_id')
                                    ->label(__('service_orders.forms.fields.employee_id'))
                                    ->relationship('employee', 'name')
                                    ->columnSpan(2)
                                    ->reactive()
                                    ->searchable(),
                            ]),
                            Grid::make(1)->schema([
                                Textarea::make('visit_additional_info')
                                    ->label(__('service_orders.forms.fields.visit_additional_info'))
                                    ->rows(3),
                            ]),
                        ]),
                    Tab::make('attachments')
                        ->label('Anexos')
                        ->schema([
                            Grid::make(1)->schema([
                                Repeater::make('attachments')
                                    ->relationship('serviceOrderAttachments')
                                    ->hiddenLabel()
                                    ->defaultItems(0)
                                    ->columns(1)
                                    ->addActionLabel('Adicionar anexo')
                                    ->schema([
                                        Grid::make(1)->schema([
                                            TextInput::make('filename')
                                                ->label('Arquivo')
                                                ->required(),
                                        ]),
                                        Grid::make(1)->schema([
                                            FileUpload::make('path')
                                                ->label('Documento')
                                                ->disk('digitalocean')
                                                ->downloadable(),
                                        ]),
                                    ]),
                            ]),
                        ]),
                    Tab::make('checklists')
                        ->label('Roteiros')
                        ->schema([
                            TableRepeater::make('checklists')
                                ->relationship('serviceOrderChecklists')
                                ->hiddenLabel()
                                ->defaultItems(1)
                                ->addActionLabel('Vincular roteiro')
                                ->headers([
                                    Header::make('Roteiro'),
                                ])
                                ->schema([
                                    Select::make('checklist_id')
                                        ->relationship('checklist', 'name')
                                        ->searchable()
                                        ->getSearchResultsUsing(fn(string $search): array => GetActiveChecklistsByName::run($search)->pluck('name', 'id')->toArray()),
                                ]),
                        ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('code')
                    ->label(__('service_orders.forms.fields.code')),
                TextColumn::make('customer.name')
                    ->label(__('service_orders.forms.fields.customer_id')),
                TextColumn::make('equipment.name')
                    ->label(__('service_orders.forms.fields.equipment_id')),
                TextColumn::make('serviceType.name')
                    ->label(__('service_orders.forms.fields.service_type_id')),
                TextColumn::make('scheduled_to')
                    ->label(__('service_orders.forms.fields.scheduled_to'))
                    ->formatStateUsing(function (ServiceOrder $serviceOrder): ?string {
                        if (!$serviceOrder->scheduled_to) {
                            return null;
                        }

                        return carbon($serviceOrder->scheduled_to)->hour === 0 && carbon($serviceOrder->scheduled_to)->minute === 0 && carbon($serviceOrder->scheduled_to)->second === 0
                            ? carbon($serviceOrder->scheduled_to)->format('d/m/Y')
                            : carbon($serviceOrder->scheduled_to)->format('d/m/Y H:i:s');
                    }),
                TextColumn::make('status')
                    ->label(__('service_orders.forms.fields.status'))
                    ->badge()
                    ->formatStateUsing(fn(ServiceOrder $serviceOrder): string => ServiceOrderStatusEnum::getTranslated()[$serviceOrder->status])
                    ->color(fn(ServiceOrder $serviceOrder): string => ServiceOrderStatusEnum::getTableColors()[$serviceOrder->status]),
                TextColumn::make('in_warranty')
                    ->label(__('service_orders.forms.fields.in_warranty'))
                    ->formatStateUsing(fn(ServiceOrder $serviceOrder): string => $serviceOrder->in_warranty ? 'Sim' : 'Não')
                    ->badge()
                    ->color(fn(ServiceOrder $serviceOrder): string => $serviceOrder->in_warranty ? 'primary' : 'gray'),
            ])
            ->filters([
                TableTextFilter::buildLike('service_orders', 'code'),
                TableTextFilter::buildRelation('service_orders', 'customer_id', 'customer', 'name'),
                TableTextFilter::buildRelation('service_orders', 'equipment_id', 'equipment', 'name'),
                TableSelectFilter::build('service_orders', 'service_type_id', GetServiceTypes::run()->pluck('name', 'id')->toArray()),
                TableSelectFilter::build('service_orders', 'status', ServiceOrderStatusEnum::getTranslated()),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge),
                    EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->successNotification(success_notification(__('service_orders.responses.update.success'))),
                    Action::make('execute')
                        ->label('Executar')
                        ->icon('heroicon-o-play')
                        ->color('primary')
                        ->requiresConfirmation()
                        ->visible(fn(ServiceOrder $serviceOrder): bool => in_array($serviceOrder->status, [ServiceOrderStatusEnum::Pending->value, ServiceOrderStatusEnum::Scheduled->value]))
                        ->action(function (ServiceOrder $serviceOrder): void {
                            try {
                                ExecuteServiceOrder::run($serviceOrder);
                                success_notification(__('service_orders.responses.execute.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    Action::make('continue_execution')
                        ->label('Continuar execução')
                        ->icon('heroicon-o-play')
                        ->color('gray')
                        ->visible(fn(ServiceOrder $serviceOrder): bool => $serviceOrder->status === ServiceOrderStatusEnum::Executing->value)
                        ->url(fn(ServiceOrder $serviceOrder): string => route('filament.app.services.pages.execute-service-order', $serviceOrder->id)),
                    Action::make('attach_checklists')
                        ->label('Vincular roteiros')
                        ->icon('heroicon-o-document')
                        ->color('gray')
                        ->hidden(fn(ServiceOrder $serviceOrder): bool => in_array($serviceOrder->status, [ServiceOrderStatusEnum::Finished->value, ServiceOrderStatusEnum::Cancelled->value]))
                        ->form([
                            Grid::make(1)->schema([
                                TableRepeater::make('checklist_ids')
                                    ->label('Roteiros')
                                    ->defaultItems(1)
                                    ->addActionLabel('Adicionar roteiro a ser vinculado')
                                    ->headers([
                                        Header::make('Roteiro'),
                                    ])
                                    ->schema([
                                        Select::make('checklist_id')
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetActiveChecklistsByName::run($search)->pluck('name', 'id')->toArray()),
                                    ]),
                            ]),
                            Grid::make(1)->schema([
                                Placeholder::make('checklist_attachment_warning_placeholder')
                                    ->hiddenLabel()
                                    ->content(fn(): HtmlString => new HtmlString('<strong>Importante:</strong> lembre-se de que roteiros previamente vinculados não serão duplicados, mas desconsiderados nessa ação.')),
                            ]),
                        ])
                        ->action(function (ServiceOrder $serviceOrder, array $data): void {
                            $checklistIds = array_values(
                                array_map(fn(array $item) => (int)$item['checklist_id'], $data['checklist_ids'])
                            );

                            try {
                                AttachChecklistToServiceOrder::run($serviceOrder, $checklistIds);
                                success_notification(__('service_orders.responses.attach_checklists.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->requiresConfirmation(),
                    Action::make('send_to_quote')
                        ->label('Enviar para orçamento (protocolo)')
                        ->icon('heroicon-o-currency-dollar')
                        ->color('gray')
                        ->visible(fn(ServiceOrder $serviceOrder): bool => !is_null($serviceOrder->protocol_id))
                        ->action(function(ServiceOrder $serviceOrder): void {
                            try {
                                SendServiceOrderToQuote::run($serviceOrder);
                                success_notification(__('service_orders.responses.send_to_quote.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->requiresConfirmation(),
                    Action::make('generate_pdf')
                        ->label('Gerar PDF')
                        ->icon('heroicon-o-document')
                        ->color('gray')
                        ->url(fn(ServiceOrder $serviceOrder): string => route('filament.app.services.pages.generate-service-order-pdf', $serviceOrder->token))
                        ->openUrlInNewTab(),
                    Action::make('reopen')
                        ->label('Reabrir')
                        ->icon('heroicon-o-arrow-uturn-left')
                        ->color('gray')
                        ->visible(fn(ServiceOrder $serviceOrder): bool => $serviceOrder->status === ServiceOrderStatusEnum::Finished->value && auth()->user()->can(Permission::REOPEN_SERVICE_ORDERS))
                        ->action(function (ServiceOrder $serviceOrder): void {
                            try {
                                ReopenServiceOrder::run($serviceOrder);
                                success_notification(__('service_orders.responses.reopen.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->requiresConfirmation(),
                    Action::make('cancel')
                        ->label('Cancelar')
                        ->icon('heroicon-o-x-circle')
                        ->visible(fn(ServiceOrder $serviceOrder): bool => in_array($serviceOrder->status, [ServiceOrderStatusEnum::Pending->value, ServiceOrderStatusEnum::Scheduled->value, ServiceOrderStatusEnum::Executing->value]))
                        ->form([
                            Grid::make(1)->schema([
                                Textarea::make('cancellation_additional_info')
                                    ->label('Motivo de cancelamento')
                                    ->required()
                                    ->rows(5)
                                    ->maxLength(500),
                            ]),
                        ])
                        ->action(function (ServiceOrder $serviceOrder, array $data): void {
                            try {
                                CancelServiceOrder::run($serviceOrder, $data['cancellation_additional_info']);
                                success_notification(__('service_orders.responses.cancel.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                    DeleteAction::make()
                        ->action(function (ServiceOrder $serviceOrder): void {
                            try {
                                DeleteServiceOrder::run($serviceOrder);
                                success_notification(__('service_orders.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                ])->dropdownWidth(MaxWidth::ExtraSmall),
            ])
            ->bulkActions([
                BulkAction::make('send_email')
                    ->label('Enviar e-mail para selecionados')
                    ->color('gray')
                    ->icon('heroicon-s-envelope')
                    ->form([
                        Grid::make(1)->schema([
                            TextInput::make('to_email')
                                ->label('E-mails (separados por vírgula)')
                                ->required()
                                ->placeholder('<EMAIL>,<EMAIL>'),
                        ]),
                    ])
                    ->action(function (\Illuminate\Support\Collection $records, array $data) {
                        $recordsIds = implode(',', $records->pluck('id')->toArray());

                        try {
                            SendServiceOrdersEmail::run(base64_encode($recordsIds), $data['to_email']);
                            success_notification(__('service_orders.responses.send_email.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->deselectRecordsAfterCompletion(),
                BulkAction::make('generate_quote')
                    ->label('Gerar orçamento para selecionados')
                    ->color('gray')
                    ->icon('heroicon-s-currency-dollar')
                    ->action(function (\Illuminate\Support\Collection $records): void {
                        try {
                            GenerateServiceOrdersQuote::run($records->pluck('id')->toArray());
                            success_notification(__('service_orders.responses.generate_quote.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->deselectRecordsAfterCompletion(),
            ])
            ->emptyStateHeading('Ainda sem ordens de serviço')
            ->emptyStateDescription('Assim que você cadastrar seus ordens de serviço, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageServiceOrders::route('/'),
        ];
    }
}
