<?php

namespace App\Filament\Clusters\Services\Resources\ProtocolResource\Pages;

use App\Actions\Protocol\GenerateProtocolQuote;
use App\Filament\Clusters\Services\Resources\ProtocolResource;
use App\Models\Protocol;
use App\Models\TenantSettings;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;

class ManageProtocols extends ManageRecords
{
    protected static string $resource = ProtocolResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->successNotification(success_notification(__('protocols.responses.create.success')))
                ->after(function (Protocol $protocol) {
                    /** @var \App\Models\TenantSettings $tenantSettings */
                    $tenantSettings = TenantSettings::first();

                    if ($tenantSettings->protocol_create_quote_automatically) {
                        GenerateProtocolQuote::run($protocol);
                    }
                }),
        ];
    }
}
