<?php

namespace App\Filament\Clusters\Services\Resources\ServiceTypeResource\Pages;

use App\Actions\ServiceType\Integrations\GetThirdPartyServiceTypes;
use App\Filament\Clusters\Services\Resources\ServiceTypeResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageServiceTypes extends ManageRecords
{
    protected static string $resource = ServiceTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-service-types')
                ->visible(auth()->user()->hasRole('Administrador'))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyServiceTypes::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.services.resources.service-types.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os tipos de serviço neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->successNotification(success_notification(__('service_types.responses.create.success'))),
        ];
    }
}
