<?php

namespace App\Filament\Clusters\Services\Resources\QuoteResource\Pages;

use App\Actions\Quote\CreateQuote;
use App\Filament\Clusters\Services\Resources\QuoteResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Throwable;

class ManageQuotes extends ManageRecords
{
    protected static string $resource = QuoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth(MaxWidth::Full)
                ->action(function (array $data): void {
                    try {
                        CreateQuote::run($data);
                        success_notification(__('quotes.responses.create.success'))->send();
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }
}
