<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TenantResource\Pages;
use App\Models\Tenant;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TenantResource extends Resource
{
    protected static ?string $model = Tenant::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make('tabs')->schema([
                    Tab::make('Identificação')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('id')
                                ->label(__('tenants.forms.fields.id'))
                                ->required()
                                ->disabledOn('edit'),
                            TextInput::make('logo_url')
                                ->label(__('tenants.forms.fields.logo_url'))
                                ->required()
                                ->columnSpan(2),
                        ]),
                    ]),
                    Tab::make('Licenças')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('max_administrators_count')
                                ->label(__('tenants.forms.fields.max_administrators_count'))
                                ->required()
                                ->numeric(),
                            TextInput::make('max_technicians_count')
                                ->label(__('tenants.forms.fields.max_technicians_count'))
                                ->required()
                                ->numeric(),
                        ]),
                    ]),
                    Tab::make('Usuário inicial')
                        ->visibleOn('create')
                        ->schema([
                            Grid::make(4)->schema([
                                TextInput::make('main_user_name')
                                    ->label(__('tenants.forms.fields.main_user_name'))
                                    ->required()
                                    ->columnSpan(2),
                                TextInput::make('main_user_email')
                                    ->label(__('tenants.forms.fields.main_user_email'))
                                    ->required(),
                                TextInput::make('main_user_password')
                                    ->label(__('tenants.forms.fields.main_user_password'))
                                    ->required(),
                            ]),
                        ]),
                    Tab::make('ERPFlex')->schema([
                        Grid::make(4)->schema([
                            Select::make('erp_flex_environment')
                                ->label(__('tenants.forms.fields.erp_flex_environment'))
                                ->selectablePlaceholder(false)
                                ->options([
                                    'sandbox' => 'Homologação',
                                    'production' => 'Produção',
                                ]),
                        ]),
                        Fieldset::make('Base individual')->schema([
                            Grid::make(4)->schema([
                                TextInput::make('erp_flex_tenant_id')
                                    ->label(__('tenants.forms.fields.erp_flex_tenant_id')),
                                TextInput::make('erp_flex_api_username')
                                    ->label(__('tenants.forms.fields.erp_flex_api_username')),
                                TextInput::make('erp_flex_api_password')
                                    ->label(__('tenants.forms.fields.erp_flex_api_password')),
                            ]),
                        ]),
                        Fieldset::make('Base consolidadora')->schema([
                            Grid::make(4)->schema([
                                TextInput::make('erp_flex_consolidator_tenant_id')
                                    ->label(__('tenants.forms.fields.erp_flex_consolidator_tenant_id')),
                                TextInput::make('erp_flex_consolidator_api_username')
                                    ->label(__('tenants.forms.fields.erp_flex_consolidator_api_username')),
                                TextInput::make('erp_flex_consolidator_api_password')
                                    ->label(__('tenants.forms.fields.erp_flex_consolidator_api_password')),
                            ]),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label(__('tenants.forms.fields.id')),
            ])
            ->filters([
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTenants::route('/'),
            'create' => Pages\CreateTenant::route('/create'),
            'edit' => Pages\EditTenant::route('/{record}/edit'),
            'view' => Pages\ViewTenant::route('/{record}'),
        ];
    }
}
