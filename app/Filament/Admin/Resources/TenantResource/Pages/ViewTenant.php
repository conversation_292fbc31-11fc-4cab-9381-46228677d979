<?php

namespace App\Filament\Admin\Resources\TenantResource\Pages;

use App\Filament\Admin\Resources\TenantResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTenant extends ViewRecord
{
    protected static string $resource = TenantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return array_merge($data, [
            'erp_flex_api_username' => ouess_aes256cbc_decrypt($this->record->erp_flex['api']['username'] ?? null),
            'erp_flex_api_password' => ouess_aes256cbc_decrypt($this->record->erp_flex['api']['password'] ?? null),
            'erp_flex_tenant_id' => $this->record->erp_flex['customer_id'] ?? null,
            'erp_flex_environment' => $this->record->erp_flex['environment'] ?? null,
        ]);
    }
}
