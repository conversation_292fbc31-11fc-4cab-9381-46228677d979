<?php

namespace App\Filament\Admin\Resources\TenantResource\Pages;

use App\Actions\Tenant\UpdateTenant;
use App\Filament\Admin\Resources\TenantResource;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditTenant extends EditRecord
{
    protected static string $resource = TenantResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return array_merge($data, [
            'erp_flex_api_username' => ouess_aes256cbc_decrypt($this->record->erp_flex['api']['username'] ?? null),
            'erp_flex_api_password' => ouess_aes256cbc_decrypt($this->record->erp_flex['api']['password'] ?? null),
            'erp_flex_tenant_id' => $this->record->erp_flex['customer_id'] ?? null,
            'erp_flex_environment' => $this->record->erp_flex['environment'] ?? null,
        ]);
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return UpdateTenant::run($record, $data);
    }
}
