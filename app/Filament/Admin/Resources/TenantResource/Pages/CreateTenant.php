<?php

namespace App\Filament\Admin\Resources\TenantResource\Pages;

use App\Actions\Tenant\CreateBasicTenantEnvironment;
use App\Filament\Admin\Resources\TenantResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateTenant extends CreateRecord
{
    protected static string $resource = TenantResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        return CreateBasicTenantEnvironment::run($data);
    }
}
