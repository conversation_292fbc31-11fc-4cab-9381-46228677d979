<?php

function get_erp_flex_api_credentials(): array
{
    $tenantErpFlexApiCredentials = tenant()->erp_flex['api'];

    return array_filter([
        'username' => ouess_aes256cbc_decrypt($tenantErpFlexApiCredentials['username']),
        'password' => ouess_aes256cbc_decrypt($tenantErpFlexApiCredentials['password']),
        'consolidator' => !isset($tenantErpFlexApiCredentials['consolidator'])
            ? null
            : [
                'api' => [
                    'username' => ouess_aes256cbc_decrypt($tenantErpFlexApiCredentials['consolidator']['api']['username']),
                    'password' => ouess_aes256cbc_decrypt($tenantErpFlexApiCredentials['consolidator']['api']['password']),
                ],
            ]
    ]);
}
