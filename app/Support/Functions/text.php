<?php

use Illuminate\Support\Str;

function normalize_text_upper(?string $value)
{
    return !is_null($value)
        ? trim(Str::ascii(mb_strtoupper($value)))
        : null;
}

function get_numbers(?string $value): string
{
    return is_null($value)
        ? ''
        : preg_replace('/[^0-9]+/', '', $value);
}

function mask_cpf(mixed $value): string
{
    return is_null($value)
        ? ''
        : substr($value, 0, 3) . '.' . substr($value, 3, 3) . '.' . substr($value, 6, 3) . '-' . substr($value, -2);
}

function mask_cnpj(mixed $value): string
{
    return is_null($value)
        ? ''
        : substr($value, 0, 2) . '.' . substr($value, 2, 3) . '.' . substr($value, 5, 3) . '/' . substr($value, 8, 4) . '-' . substr($value, -2);
}

function mask_cnae(mixed $value): string
{
    return is_null($value)
        ? ''
        : substr($value, 0, 4) . '-' . substr($value, 4, 1) . '/' . substr($value, 5, 2);
}

/**
 * Mask a string with brazilian zipcode format.
 *
 * @param  string|null $value
 * @return string|null
 */
function mask_zipcode(?string $value): ?string
{
    return is_null($value)
        ? null
        : substr($value, 0, 5) . '-' . substr($value, 5, 3);
}

function unmask_cpf(mixed $value): string
{
    return get_numbers($value);
}

function unmask_cnae(mixed $value): string
{
    return get_numbers($value);
}

function unmask_cnpj(mixed $value): string
{
    return get_numbers($value);
}

function unmask_zipcode(mixed $value): string
{
    return get_numbers($value);
}

function format_money(?float $value, int $decimalPlaces = 2): string
{
    if (is_null($value)) {
        return '';
    }

    return 'R$ ' . number_format($value, $decimalPlaces, ',', '.');
}

function format_percentage(?float $value): string
{
    if (is_null($value)) {
        return '';
    }

    return number_format($value, 2, ',', '.') . '%';
}

function unmask_money(?string $value): float
{
    if (is_null($value)) {
        return 0.0;
    }

    if (is_numeric($value)) {
        return $value;
    }

    return (float) str_replace(['R$', '.', ','], ['', '', '.'], $value);
}

function unmask_percentage(?string $value): float
{
    if (is_null($value)) {
        return 0.0;
    }

    if (is_numeric($value)) {
        return $value;
    }

    return (float) str_replace(['%', '.', ','], ['', '', '.'], $value);
}

function format_phone(?string $value): ?string
{
    if (is_null($value)) {
        return '';
    }

    return '(' . substr($value, 0, 2) . ') '
        . substr($value, 2, strlen($value) === 11 ? 5 : 4) . '-'
        . substr($value, strlen($value) === 11 ? 7 : 6, 4);
}

/**
 * Unmask a phone string.
 *
 * @param  string|null $value
 * @return string|null
 */
function unmask_phone(?string $value): ?string
{
    if (is_null($value)) {
        return null;
    }

    return trim(str_replace(['(', ')', '-', ' '], ['', '', '', ''], $value));
}

function validate_cpf(string $taxIdNumber): bool
{
    // Extrai somente os números
    $taxIdNumber = preg_replace('/[^0-9]/is', '', $taxIdNumber);

    // Verifica se foi informado todos os digitos corretamente
    if (strlen($taxIdNumber) != 11) {
        return false;
    }

    // Verifica se foi informada uma sequência de digitos repetidos. Ex: 111.111.111-11
    if (preg_match('/(\d)\1{10}/', $taxIdNumber)) {
        return false;
    }

    // Faz o calculo para validar o CPF
    for ($t = 9; $t < 11; $t++) {
        for ($d = 0, $c = 0; $c < $t; $c++) {
            $d += $taxIdNumber[$c] * (($t + 1) - $c);
        }

        $d = ((10 * $d) % 11) % 10;

        if ($taxIdNumber[$c] != $d) {
            return false;
        }
    }
    return true;
}
