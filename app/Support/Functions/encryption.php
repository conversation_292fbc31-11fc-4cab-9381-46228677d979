<?php

function ouess_aes256cbc_encrypt(?string $value): string
{
    if (is_null($value)) {
        return '';
    }

    return openssl_encrypt(
        $value,
        'aes-256-cbc',
        config('encryption.key'),
        iv: config('encryption.vector')
    );
}

function ouess_aes256cbc_decrypt(?string $value): string
{
    if (is_null($value)) {
        return '';
    }

    return openssl_decrypt(
        $value,
        'aes-256-cbc',
        config('encryption.key'),
        iv: config('encryption.vector')
    );
}
