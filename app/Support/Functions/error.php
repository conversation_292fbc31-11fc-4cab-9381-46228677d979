<?php

/*
|--------------------------------------------------------------------------
| Error helpers
|--------------------------------------------------------------------------
|
*/

if (!function_exists('error')) {
    /**
     * Write some error to the log.
     *
     * @param  string $message
     * @param  array $context
     * @return void
     */
    function error($message, $context = [])
    {
        app('log')->error($message, $context);
    }
}

/**
 * Log and throw a specific error.
 *
 * @param  \Throwable $th
 * @param  string|null $message
 * @param  bool $log
 * @return void
 */
function throw_error(\Throwable $th, ?string $message = null, bool $log = true): void
{
    if ($log) {
        error($th);
    }

    throw new \Exception($message ?? __('general.unknown_error'));
}

/**
 * Log and throw a generic unknown error.
 *
 * @param  \Throwable $th
 * @return void
 */
function throwUnknownError(\Throwable $th): void
{
    throw_error($th);
}

function validate_foreign_key(Throwable $th, string $table, string $entity, string $reason)
{
    if ((int) $th->getCode() === 23000 && str_contains($th->getMessage(), $table)) {
        throw new Exception(__("Desculpe, não foi possível excluir $entity pois $reason."));
    }
}
