<?php

/**
 * Short helper for Carbon instances.
 *
 * @param  mixed $value
 * @return \Carbon\Carbon|null
 */
function carbon(mixed $value)
{
    return !is_null($value)
        ? \Carbon\Carbon::parse($value)
        : null;
}

/**
 * Format a given date to d/m/Y format (example: 01/01/1980).
 *
 * @param  mixed $value
 * @return string
 */
function format_date(mixed $value): string
{
    return !is_null($value)
        ? carbon($value)->format('d/m/Y')
        : '';
}

/**
 * Format a given date/time to d/m/Y H:i:s format (example: 01/01/1980 12:00:00).
 *
 * @param  mixed $value
 * @param  string $timezone
 * @return string
 */
function format_datetime(mixed $value, string $timezone = '-3:00'): string
{
    return !is_null($value)
        ? carbon($value)->setTimezone($timezone)->format('d/m/Y H:i:s')
        : '';
}

/**
 * Get the timezoned "issued at" date/time based on the current tenant's configuration.
 *
 * @return string
 */
function get_timezoned_issued_at(): string
{
    return format_datetime(now(), tenant('timezone') ?? '-3:00');
}
