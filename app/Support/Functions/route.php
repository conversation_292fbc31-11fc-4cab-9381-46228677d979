<?php

use Illuminate\Http\RedirectResponse;

function redirect_success(string $routeName, string $message, mixed $parameters = [], bool $back = false): RedirectResponse
{
    if (!is_array($parameters)) {
        $parameters = [$parameters];
    }

    if ($back) {
        return back()->with(flashSuccess($message));
    }

    return redirect()
        ->route($routeName, $parameters)
        ->with(flashSuccess($message));
}

function redirect_error(string $message, ?string $routeName = null): RedirectResponse
{
    return !is_null($routeName)
        ? redirect()->route($routeName)->with(flashError($message))
        : back()->with(flashError($message));
}

function redirect_report_error(?string $message = null): RedirectResponse
{
    error_notification($message ?? __('general.responses.report.general.error'))->send();
    return redirect()->route('filament.app.pages.list-reports');
}

function redirect_import_error(?string $message = null): RedirectResponse
{
    return redirect_error($message ?? __('general.imports.general.error'), 'imports.index');
}

function flashSuccess(string $message): void
{
    session()->flash('success', $message);
}

function flashError(string $message): void
{
    session()->flash('error', $message);
}
