<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ServiceOrdersExport extends BaseExport implements FromArray, WithHeadings
{
    public function __construct(private array $reportData) {}

    public function headings(): array
    {
        return [
            'Código',
            'Doc./NF',
            'Razão social',
            'Nome fantasia',
            'CPF/CNPJ',
            'Equipamento',
            'Tipo de serviço',
            'Técnico',
            'Status',
            'Iniciada em',
            'Finalizada em',
            'Data de emissão',
        ];
    }

    public function array(): array
    {
        return $this->reportData;
    }
}
