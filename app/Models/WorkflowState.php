<?php

namespace App\Models;

use Heloufir\FilamentKanban\Interfaces\KanbanStatusModel;
use Helou<PERSON>r\FilamentKanban\ValueObjects\KanbanStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Workflow state model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $workflow_id
 * @property  int $sequence
 * @property  string $name
 * @property  bool $characterizes_service_order_execution
 * @property  bool $characterizes_resolution
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Workflow $workflow
 */
class WorkflowState extends Model implements KanbanStatusModel
{
    protected $fillable = [
        'workflow_id',
        'sequence',
        'name',
        'characterizes_service_order_execution',
        'characterizes_resolution',
    ];

    protected $casts = [
        'workflow_id' => 'int',
        'sequence' => 'int',
        'characterizes_service_order_execution' => 'bool',
        'characterizes_resolution' => 'bool',
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function toKanbanStatus(): KanbanStatus
    {
        return KanbanStatus::make()
            ->id($this->id)
            ->title($this->name);
    }
}
