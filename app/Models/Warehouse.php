<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Warehouse model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\WarehouseProduct[] $warehouseProducts
 */
class Warehouse extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Load the warehouse products relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function warehouseProducts(): HasMany
    {
        return $this->hasMany(WarehouseProduct::class);
    }
}
