<?php

namespace App\Models;

use App\Models\Concerns\Protocol\HandlesProtocolRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Protocol model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $code
 * @property  string $invoice_number
 * @property  int $customer_id
 * @property  string $description
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ProtocolEquipment[] $protocolEquipment
 * @property  \Illuminate\Support\Collection|\App\Models\ProtocolServiceOrder[] $protocolServiceOrders
 * @property  \Illuminate\Support\Collection|\App\Models\Quote[] $quotes
 */
class Protocol extends Model
{
    use HandlesProtocolRelationships;

    protected $fillable = [
        'code',
        'invoice_number',
        'customer_id',
        'description',
    ];

    protected static function booted()
    {
        static::creating(function (self $protocol): void {
            if (!$protocol->code) {
            }
        });
    }
}
