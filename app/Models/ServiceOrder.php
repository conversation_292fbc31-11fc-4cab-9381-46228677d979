<?php

namespace App\Models;

use App\Actions\ServiceOrder\Integrations\ErpFlex\CreateServiceOrderInErpFlex;
use App\Enums\ServiceOrderStatusEnum;
use App\Enums\WorkflowTypeEnum;
use App\Models\Concerns\ServiceOrder\HandlesServiceOrderAttributes;
use App\Models\Concerns\ServiceOrder\HandlesServiceOrderRelationships;
use Heloufir\FilamentKanban\Interfaces\KanbanRecordModel;
use Heloufir\FilamentKanban\ValueObjects\KanbanRecord;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Service order model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $code
 * @property  int $protocol_id
 * @property  int $ticket_id
 * @property  int $contract_item_id
 * @property  int $customer_id
 * @property  int $equipment_id
 * @property  int $service_type_id
 * @property  int $employee_id
 * @property  int $workflow_id
 * @property  int $workflow_state_id
 * @property  int $quantity
 * @property  string $description
 * @property  string $visit_additional_info
 * @property  int $estimated_duration_in_minutes
 * @property  string $status
 * @property  \Carbon\Carbon $started_at
 * @property  \Carbon\Carbon $finished_at
 * @property  \Carbon\Carbon $cancelled_at
 * @property  string $cancellation_additional_info
 * @property  \Carbon\Carbon $scheduled_to
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $token
 * @property  string $friendly_status
 * @property  string $friendly_started_at
 * @property  string $friendly_finished_at
 * @property  bool $in_warranty
 *
 * @property  \App\Models\Protocol $protocol
 * @property  \App\Models\Ticket $ticket
 * @property  \App\Models\ContractItem $contractItem
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Equipment $equipment
 * @property  \App\Models\ServiceType $serviceType
 * @property  \App\Models\User $employee
 * @property  \App\Models\Workflow $workflow
 * @property  \App\Models\WorkflowState $workflowState
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderAttachment[] $serviceOrderAttachments
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderChecklist[] $serviceOrderChecklists
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderWorkflowStateTransition[] $serviceOrderWorkflowStateTransitions
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderExecutionChecklistStep[] $serviceOrderExecutionChecklistSteps
 */
class ServiceOrder extends Model implements KanbanRecordModel
{
    use HandlesServiceOrderAttributes;
    use HandlesServiceOrderRelationships;

    protected $fillable = [
        'code',
        'protocol_id',
        'ticket_id',
        'contract_item_id',
        'customer_id',
        'equipment_id',
        'service_type_id',
        'employee_id',
        'workflow_id',
        'workflow_state_id',
        'quantity',
        'description',
        'visit_additional_info',
        'estimated_duration_in_minutes',
        'status',
        'started_at',
        'finished_at',
        'cancelled_at',
        'cancellation_additional_info',
        'scheduled_to',
    ];

    protected $appends = [
        'friendly_status',
        'in_warranty',
    ];

    protected $casts = [
        'quantity' => 'int',
        'estimated_duration_in_minutes' => 'int',
    ];

    protected $with = [
        'serviceType',
    ];

    protected static function booted(): void
    {
        static::creating(function (self $serviceOrder): void {
            /** @var \App\Models\WorkflowState $workflowState */
            $workflowState = WorkflowState::query()
                ->whereRelation('workflow', function (Builder $query): Builder {
                    return $query
                        ->where('type', WorkflowTypeEnum::ServiceOrder->value)
                        ->where('active', true);
                })
                ->orderBy('sequence')
                ->first();

            $serviceOrder->fill([
                'workflow_id' => $workflowState->workflow_id,
                'workflow_state_id' => $workflowState->id,
                'status' => $serviceOrder->status ?? ServiceOrderStatusEnum::Pending->value,
            ]);
        });

        static::created(function (self $serviceOrder): void {
            CreateServiceOrderInErpFlex::dispatch($serviceOrder, auth()->id());
        });
    }

    public function toKanbanRecord(): KanbanRecord
    {
        return KanbanRecord::make($this)
            ->deletable(false)
            ->sortable(true)
            ->editable(true)
            ->viewable(true)
            ->id($this->id)
            ->title($this->code)
            ->status($this->workflowState->toKanbanStatus());
    }

    public function statusColumn(): string
    {
        return 'workflow_state_id';
    }

    public function sortColumn(): string
    {
        return 'code';
    }
}
