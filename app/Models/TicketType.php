<?php

namespace App\Models;

use App\Models\Concerns\TicketType\HandlesTicketTypeRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Ticket type model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_type_id
 * @property  string $name
 * @property  string $priority
 * @property  int $sla_hours
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceType $serviceType
 *
 * @property  \Illuminate\Support\Collection|\App\Models\Ticket[] $tickets
 */
class TicketType extends Model
{
    use HandlesTicketTypeRelationships;

    protected $fillable = [
        'service_type_id',
        'name',
        'priority',
        'sla_hours',
    ];
}
