<?php

namespace App\Models;

use App\Models\Concerns\ServiceOrderExecutionChecklistStep\HandlesServiceOrderExecutionChecklistStepAttributes;
use App\Models\Concerns\ServiceOrderExecutionChecklistStep\HandlesServiceOrderExecutionChecklistStepRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Service order execution checklist step model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_id
 * @property  int $checklist_id
 * @property  string $checklist_name
 * @property  int $sequence
 * @property  string $checklist_step_name
 * @property  string $data_type
 * @property  string $instructions
 * @property  string $collected_value
 * @property  array $collected_extras
 * @property  string $comments
 * @property  bool $is_checklist_final_step
 * @property  bool $requires_comment
 * @property  bool $has_products
 * @property  bool $has_needs
 * @property  bool $shows_checklist_in_quote
 * @property  bool $shows_in_quote
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_data_type
 *
 * @property  \App\Models\ServiceOrder $serviceOrder
 * @property  \App\Models\Checklist $checklist
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderExecutionChecklistStepOption[] $serviceOrderExecutionChecklistStepOptions
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderExecutionChecklistStepProduct[] $serviceOrderExecutionChecklistStepProducts
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrderExecutionChecklistStepNeed[] $serviceOrderExecutionChecklistStepNeeds
 */
class ServiceOrderExecutionChecklistStep extends Model
{
    use HandlesServiceOrderExecutionChecklistStepAttributes;
    use HandlesServiceOrderExecutionChecklistStepRelationships;

    protected $fillable = [
        'service_order_id',
        'checklist_id',
        'checklist_name',
        'sequence',
        'checklist_step_name',
        'data_type',
        'instructions',
        'collected_value',
        'collected_extras',
        'comments',
        'is_checklist_final_step',
        'requires_comment',
        'has_products',
        'has_needs',
        'shows_checklist_in_quote',
        'shows_in_quote',
    ];

    protected $casts = [
        'sequence' => 'int',
        'collected_extras' => 'array',
        'is_checklist_final_step' => 'bool',
        'requires_comment' => 'bool',
        'has_products' => 'bool',
        'has_needs' => 'bool',
        'shows_checklist_in_quote' => 'bool',
        'shows_in_quote' => 'bool',
    ];
}
