<?php

namespace App\Models;

use App\Models\Concerns\ThirdPartyMeasuringUnit\HandlesThirdPartyMeasuringUnitRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Third party measuring unit model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $integration_type_id
 * @property  int $measuring_unit_id
 * @property  string $third_party_id
 * @property  array $third_party_api_read_data
 * @property  array $third_party_db_read_data
 * @property  array $third_party_sent_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\MeasuringUnit $equipmentType
 */
class ThirdPartyMeasuringUnit extends Model
{
    use HandlesThirdPartyMeasuringUnitRelationships;

    protected $fillable = [
        'integration_type_id',
        'measuring_unit_id',
        'third_party_id',
        'third_party_api_read_data',
        'third_party_db_read_data',
        'third_party_sent_data',
    ];

    protected $casts = [
        'third_party_api_read_data' => 'array',
        'third_party_db_read_data' => 'array',
        'third_party_sent_data' => 'array',
    ];
}
