<?php

namespace App\Models;

use App\Models\Concerns\ThirdPartyWarehouseProduct\HandlesThirdPartyWarehouseProductRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Third party warehouse product model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $integration_type_id
 * @property  int $warehouse_product_id
 * @property  string $third_party_id
 * @property  array $third_party_api_read_data
 * @property  array $third_party_db_read_data
 * @property  array $third_party_sent_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\WarehouseProduct $warehouse
 */
class ThirdPartyWarehouseProduct extends Model
{
    use HandlesThirdPartyWarehouseProductRelationships;

    protected $fillable = [
        'integration_type_id',
        'warehouse_product_id',
        'third_party_id',
        'third_party_api_read_data',
        'third_party_db_read_data',
        'third_party_sent_data',
    ];

    protected $casts = [
        'third_party_api_read_data' => 'array',
        'third_party_db_read_data' => 'array',
        'third_party_sent_data' => 'array',
    ];
}
