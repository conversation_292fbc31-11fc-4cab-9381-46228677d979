<?php

namespace App\Models;

use App\Models\Concerns\NotificationSetting\HandlesNotificationSettingRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Notification setting model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $user_id
 * @property  bool $contract_termination
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\User $user
 */
class NotificationSetting extends Model
{
    use HandlesNotificationSettingRelationships;

    protected $fillable = [
        'user_id',
        'contract_termination',
    ];

    protected $casts = [
        'contract_termination' => 'bool',
    ];
}
