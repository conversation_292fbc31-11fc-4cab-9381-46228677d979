<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Stock movement model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $item_id
 * @property  string $item_type
 * @property  int $origin_warehouse_id
 * @property  int $destination_warehouse_id
 * @property  float $quantity
 * @property  string $additional_info
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Product $item
 * @property  \App\Models\Warehouse $originWarehouse
 * @property  \App\Models\Warehouse $destinationWarehouse
 */
class StockMovement extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'item_id',
        'item_type',
        'origin_warehouse_id',
        'destination_warehouse_id',
        'quantity',
        'additional_info',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'float',
    ];

    /**
     * Load the item relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function item(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Load the origin warehouse relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function originWarehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'origin_warehouse_id');
    }

    /**
     * Load the destination warehouse relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function destinationWarehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'destination_warehouse_id');
    }
}
