<?php

namespace App\Models;

use App\Models\Concerns\EquipmentType\HandlesEquipmentTypeRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Equipment type model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\EquipmentTypeCustomField[] $equipmentTypeCustomFields
 * @property  \Illuminate\Support\Collection|\App\Models\Equipment[] $equipment
 * @property  \Illuminate\Support\Collection|\App\Models\ThirdPartyEquipmentType[] $thirdPartyEquipmentTypes
 */
class EquipmentType extends Model
{
    use HandlesEquipmentTypeRelationships;

    protected $fillable = [
        'name',
    ];
}
