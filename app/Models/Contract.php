<?php

namespace App\Models;

use App\Enums\ContractStatusEnum;
use App\Models\Concerns\Contract\HandlesContractAttributes;
use App\Models\Concerns\Contract\HandlesContractRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
/**
 * Contract model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  string $code
 * @property  string $type
 * @property  string $status
 * @property  \Carbon\Carbon $started_at
 * @property  \Carbon\Carbon $ended_at
 * @property  \Carbon\Carbon $signed_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_type
 * @property  string $friendly_status
 *
 * @property  \App\Models\Customer $customer
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ContractItem[] $contractItems
 * @property  \Illuminate\Support\Collection|\App\Models\ContractOperator[] $contractOperators
 * @property  \Illuminate\Support\Collection|\App\Models\ContractHistory[] $contractHistories
 */
class Contract extends Model
{
    use HandlesContractAttributes;
    use HandlesContractRelationships;
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'code',
        'type',
        'status',
        'started_at',
        'ended_at',
        'signed_at',
    ];

    protected static function booted()
    {
        static::creating(function (self $contract): void {
            $contract->status ??= ContractStatusEnum::Active->value;
        });
    }
}
