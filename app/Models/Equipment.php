<?php

namespace App\Models;

use App\Models\Concerns\Equipment\HandlesEquipmentRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Equipment model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $equipment_type_id
 * @property  int $customer_id
 * @property  string $code
 * @property  string $name
 * @property  string $serial_number
 * @property  string $additional_info
 * @property  \Carbon\Carbon $manufactured_at
 * @property  \Carbon\Carbon $warranty_expires_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\EquipmentType $equipmentType
 * @property  \App\Models\Customer $customer
 *
 * @property  \Illuminate\Support\Collection|\App\Models\EquipmentCustomer[] $equipmentCustomers
 * @property  \Illuminate\Support\Collection|\App\Models\ThirdPartyEquipment[] $thirdPartyEquipment
 */
class Equipment extends Model
{
    use HandlesEquipmentRelationships;

    protected $fillable = [
        'equipment_type_id',
        'customer_id',
        'code',
        'name',
        'serial_number',
        'additional_info',
        'manufactured_at',
        'warranty_expires_at',
    ];
}
