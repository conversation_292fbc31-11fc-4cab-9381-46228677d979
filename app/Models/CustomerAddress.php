<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Customer address model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  string $type
 * @property  string $zipcode
 * @property  string $address
 * @property  string $number
 * @property  string $complement
 * @property  string $district
 * @property  int $city_id
 * @property  string $city
 * @property  int $state_id
 * @property  string $state
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 */
class CustomerAddress extends Model
{
    protected $fillable = [
        'customer_id',
        'type',
        'zipcode',
        'address',
        'number',
        'complement',
        'district',
        'city_id',
        'city',
        'state_id',
        'state',
    ];
}
