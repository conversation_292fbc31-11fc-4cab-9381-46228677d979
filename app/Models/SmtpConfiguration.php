<?php

namespace App\Models;

use App\Models\Concerns\SmtpConfiguration\HandlesSmtpConfigurationAttributes;
use App\Models\Concerns\SmtpConfiguration\HandlesSmtpConfigurationRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * SMTP configuration model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $host
 * @property  string $port
 * @property  string $username
 * @property  string $password
 * @property  string $encryption
 * @property  string $from_address
 * @property  string $from_name
 * @property  bool $active
 * @property  \DateTime $created_at
 * @property  \DateTime $updated_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\EmailTemplate[] $emailTemplates
 */
class SmtpConfiguration extends Model
{
    use HandlesSmtpConfigurationAttributes;
    use HandlesSmtpConfigurationRelationships;

    protected $fillable = [
        'host',
        'port',
        'username',
        'password',
        'encryption',
        'from_address',
        'from_name',
        'active',
    ];

    protected $casts = [
        'active' => 'bool',
    ];
}