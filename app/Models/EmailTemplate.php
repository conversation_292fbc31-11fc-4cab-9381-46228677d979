<?php

namespace App\Models;

use App\Models\Concerns\EmailTemplate\HandlesEmailTemplateRelationships;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Email template model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $smtp_configuration_id
 * @property  string $name
 * @property  string $email_template
 * @property  bool $uses_for_service_order_reporting
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\SmtpConfiguration $smtpConfiguration
 */
class EmailTemplate extends Model
{
    use HandlesEmailTemplateRelationships;

    protected $fillable = [
        'smtp_configuration_id',
        'name',
        'email_template',
        'uses_for_service_order_reporting',
    ];

    protected $casts = [
        'uses_for_service_order_reporting' => 'bool',
    ];
}
