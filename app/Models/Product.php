<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Product model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $product_type_id
 * @property  int $measuring_unit_id
 * @property  string $code
 * @property  string $name
 * @property  float $default_amount
 * @property  float $available_quantity
 * @property  string $additional_info
 * @property  string $image_path
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_default_amount
 *
 * @property  \App\Models\ProductType $productType
 * @property  \App\Models\MeasuringUnit $measuringUnit
 *
 * @property  \Illuminate\Support\Collection|\App\Models\WarehouseProduct[] $productWarehouses
 */
class Product extends Model
{
    protected $fillable = [
        'product_type_id',
        'measuring_unit_id',
        'code',
        'name',
        'default_amount',
        'available_quantity',
        'additional_info',
        'image_path',
        'active',
    ];

    protected $casts = [
        'default_amount' => 'float',
        'available_quantity' => 'float',
        'active' => 'bool',
    ];

    /**
     * Mutator for the "default amount" attribute.
     *
     * @return void
     */
    public function setDefaultAmountAttribute(mixed $value)
    {
        $this->attributes['default_amount'] = unmask_money($value);
    }

    /**
     * Accessor for the "friendly default amount" attribute.
     *
     * @return string
     */
    public function getFriendlyDefaultAmountAttribute(): string
    {
        return format_money($this->default_amount);
    }

    /**
     * Load the product type relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    /**
     * Load the measuring unit relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function measuringUnit(): BelongsTo
    {
        return $this->belongsTo(MeasuringUnit::class);
    }

    /**
     * Load the product warehouses relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function productWarehouses(): HasMany
    {
        return $this->hasMany(WarehouseProduct::class);
    }
}
