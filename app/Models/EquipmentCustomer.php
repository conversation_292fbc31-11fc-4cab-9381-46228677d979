<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Equipment customer model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $equipment_id
 * @property  int $customer_id
 * @property  \Carbon\Carbon $term_started_at
 * @property  \Carbon\Carbon $term_ended_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Equipment $equipment
 * @property  \App\Models\Customer $customer
 */
class EquipmentCustomer extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'equipment_id',
        'customer_id',
        'term_started_at',
        'term_ended_at',
    ];

    /**
     * Load the equipment relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
}
