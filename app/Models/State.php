<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

/**
 * State model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $region_id
 * @property  string $abbreviation
 * @property  string $name
 * @property  \DateTime $created_at
 * @property  \DateTime $updated_at
 *
 * @property  \App\Models\Region $region
 * @property  \Illuminate\Support\Collection|\App\Models\City[] $cities
 */
class State extends Model
{
    use CentralConnection;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'region_id',
        'abbreviation',
        'name'
    ];

    /**
     * Load the region relationship.
     *
     * @return mixed
     */
    public function region(): mixed
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Load the cities relationship.
     *
     * @return mixed
     */
    public function cities(): mixed
    {
        return $this->hasMany(City::class);
    }
}
