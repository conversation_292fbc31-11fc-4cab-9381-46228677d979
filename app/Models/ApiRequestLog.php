<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * API request log model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $model_id
 * @property  string $model_type
 * @property  string $service_name
 * @property  string $service_method
 * @property  bool $success
 * @property  string $status_code
 * @property  string $response_headers
 * @property  string $response_body
 * @property  string $method
 * @property  string $url
 * @property  string $request_headers
 * @property  string $request_body
 * @property  string $error_description
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class ApiRequestLog extends Model
{
    protected $fillable = [
        'model_id',
        'model_type',
        'service_name',
        'service_method',
        'success',
        'status_code',
        'response_headers',
        'response_body',
        'method',
        'url',
        'request_headers',
        'request_body',
        'error_description',
    ];
}
