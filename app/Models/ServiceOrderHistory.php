<?php

namespace App\Models;

use App\Models\Concerns\ServiceOrderHistory\HandlesServiceOrderHistoryRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Service order history model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_id
 * @property  int $user_id
 * @property  string $user_name
 * @property  string $action
 * @property  array $old_additional_attributes
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceOrder $serviceOrder
 * @property  \App\Models\User $user
 */
class ServiceOrderHistory extends Model
{
    use HandlesServiceOrderHistoryRelationships;

    protected $fillable = [
        'service_order_id',
        'user_id',
        'user_name',
        'action',
        'old_additional_attributes',
    ];

    protected $casts = [
        'old_additional_attributes' => 'array',
    ];

    protected static function booted(): void
    {
        static::creating(function (self $serviceOrderHistory): void {
            $serviceOrderHistory->fill([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
            ]);
        });
    }
}
