<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Integration setting model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $integration_type_id
 * @property  bool $active
 * @property  array $settings
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class IntegrationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'integration_type_id',
        'active',
        'settings',
    ];

    protected $casts = [
        'settings' => 'array',
    ];
}
