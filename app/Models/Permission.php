<?php

namespace App\Models;

/**
 * Permission model.
 *
 * @package App\Models
 */
class Permission extends \Spatie\Permission\Models\Permission
{
    public const GET_SERVICE_ORDERS = 'get_service_orders';
    public const CREATE_SERVICE_ORDERS = 'create_service_orders';
    public const UPDATE_SERVICE_ORDERS = 'update_service_orders';
    public const DELETE_SERVICE_ORDERS = 'delete_service_orders';
    public const REOPEN_SERVICE_ORDERS = 'reopen_service_orders';

    public const GET_PROTOCOLS = 'get_protocols';
    public const CREATE_PROTOCOLS = 'create_protocols';
    public const UPDATE_PROTOCOLS = 'update_protocols';
    public const DELETE_PROTOCOLS = 'delete_protocols';

    public const GET_CONTRACTS = 'get_contracts';
    public const CREATE_CONTRACTS = 'create_contracts';
    public const UPDATE_CONTRACTS = 'update_contracts';
    public const DELETE_CONTRACTS = 'delete_contracts';

    public const GET_QUOTES = 'get_quotes';
    public const CREATE_QUOTES = 'create_quotes';
    public const UPDATE_QUOTES = 'update_quotes';
    public const DELETE_QUOTES = 'delete_quotes';

    public const GET_CHECKLISTS = 'get_checklists';
    public const CREATE_CHECKLISTS = 'create_checklists';
    public const UPDATE_CHECKLISTS = 'update_checklists';
    public const DELETE_CHECKLISTS = 'delete_checklists';

    public const GET_SERVICE_TYPES = 'get_service_types';
    public const CREATE_SERVICE_TYPES = 'create_service_types';
    public const UPDATE_SERVICE_TYPES = 'update_service_types';
    public const DELETE_SERVICE_TYPES = 'delete_service_types';

    public const GET_TICKETS = 'get_tickets';
    public const CREATE_TICKETS = 'create_tickets';
    public const UPDATE_TICKETS = 'update_tickets';
    public const DELETE_TICKETS = 'delete_tickets';

    public const GET_TICKET_TYPES = 'get_ticket_types';
    public const CREATE_TICKET_TYPES = 'create_ticket_types';
    public const UPDATE_TICKET_TYPES = 'update_ticket_types';
    public const DELETE_TICKET_TYPES = 'delete_ticket_types';

    public const GET_EQUIPMENT = 'get_equipment';
    public const CREATE_EQUIPMENT = 'create_equipment';
    public const UPDATE_EQUIPMENT = 'update_equipment';
    public const DELETE_EQUIPMENT = 'delete_equipment';

    public const GET_EQUIPMENT_TYPES = 'get_equipment_types';
    public const CREATE_EQUIPMENT_TYPES = 'create_equipment_types';
    public const UPDATE_EQUIPMENT_TYPES = 'update_equipment_types';
    public const DELETE_EQUIPMENT_TYPES = 'delete_equipment_types';

    public const GET_PRODUCTS = 'get_products';
    public const CREATE_PRODUCTS = 'create_products';
    public const UPDATE_PRODUCTS = 'update_products';
    public const DELETE_PRODUCTS = 'delete_products';

    public const GET_STOCK_MOVEMENTS = 'get_stock_movements';
    public const CREATE_STOCK_MOVEMENTS = 'create_stock_movements';
    public const UPDATE_STOCK_MOVEMENTS = 'update_stock_movements';
    public const DELETE_STOCK_MOVEMENTS = 'delete_stock_movements';

    public const GET_PRODUCT_TYPES = 'get_product_types';
    public const CREATE_PRODUCT_TYPES = 'create_product_types';
    public const UPDATE_PRODUCT_TYPES = 'update_product_types';
    public const DELETE_PRODUCT_TYPES = 'delete_product_types';

    public const GET_WAREHOUSES = 'get_warehouses';
    public const CREATE_WAREHOUSES = 'create_warehouses';
    public const UPDATE_WAREHOUSES = 'update_warehouses';
    public const DELETE_WAREHOUSES = 'delete_warehouses';

    public const GET_MEASURING_UNITS = 'get_measuring_units';
    public const CREATE_MEASURING_UNITS = 'create_measuring_units';
    public const UPDATE_MEASURING_UNITS = 'update_measuring_units';
    public const DELETE_MEASURING_UNITS = 'delete_measuring_units';

    public const GET_CUSTOMERS = 'get_customers';
    public const CREATE_CUSTOMERS = 'create_customers';
    public const UPDATE_CUSTOMERS = 'update_customers';
    public const DELETE_CUSTOMERS = 'delete_customers';

    public const GET_USERS = 'get_users';
    public const CREATE_USERS = 'create_users';
    public const UPDATE_USERS = 'update_users';
    public const DELETE_USERS = 'delete_users';

    public const GET_ROLES = 'get_roles';
    public const CREATE_ROLES = 'create_roles';
    public const UPDATE_ROLES = 'update_roles';
    public const DELETE_ROLES = 'delete_roles';

    public static function translate(string $permission)
    {
        return __('permissions.' . $permission);
    }

    public static function getAvailablePermissions(): array
    {
        $permissionReflectionClass = new \ReflectionClass(self::class);

        return array_filter(
            array_values($permissionReflectionClass->getConstants()),
            fn ($item) => !in_array($item, ['created_at', 'updated_at'])
        );
    }

    public static function getMapped(): array
    {
        return [
            'roles' => [
                'permission_name' => 'Perfis',
                self::GET_ROLES => 'Listar',
                self::CREATE_ROLES => 'Criar',
                self::UPDATE_ROLES => 'Atualizar',
                self::DELETE_ROLES => 'Excluir',
            ],
            'service_orders' => [
                'permission_name' => 'Ordens de serviço',
                self::GET_SERVICE_ORDERS => 'Listar',
                self::CREATE_SERVICE_ORDERS => 'Criar',
                self::UPDATE_SERVICE_ORDERS => 'Atualizar',
                self::DELETE_SERVICE_ORDERS => 'Excluir',
            ],
            'checklists' => [
                'permission_name' => 'Roteiros',
                self::GET_CHECKLISTS => 'Listar',
                self::CREATE_CHECKLISTS => 'Criar',
                self::UPDATE_CHECKLISTS => 'Atualizar',
                self::DELETE_CHECKLISTS => 'Excluir',
            ],
            'products' => [
                'permission_name' => 'Produtos',
                self::GET_PRODUCTS => 'Listar',
                self::CREATE_PRODUCTS => 'Criar',
                self::UPDATE_PRODUCTS => 'Atualizar',
                self::DELETE_PRODUCTS => 'Excluir',
            ],
            'product_types' => [
                'permission_name' => 'Tipos de produto',
                self::GET_PRODUCT_TYPES => 'Listar',
                self::CREATE_PRODUCT_TYPES => 'Criar',
                self::UPDATE_PRODUCT_TYPES => 'Atualizar',
                self::DELETE_PRODUCT_TYPES => 'Excluir',
            ],
            'warehouses' => [
                'permission_name' => 'Armazéns',
                self::GET_WAREHOUSES => 'Listar',
                self::CREATE_WAREHOUSES => 'Criar',
                self::UPDATE_WAREHOUSES => 'Atualizar',
                self::DELETE_WAREHOUSES => 'Excluir',
            ],
            'measuring_units' => [
                'permission_name' => 'Unidades de medida',
                self::GET_CHECKLISTS => 'Listar',
                self::CREATE_CHECKLISTS => 'Criar',
                self::UPDATE_CHECKLISTS => 'Atualizar',
                self::DELETE_CHECKLISTS => 'Excluir',
            ],
        ];
    }
}
