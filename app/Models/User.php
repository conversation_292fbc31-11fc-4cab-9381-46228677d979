<?php

namespace App\Models;

use App\Models\Concerns\User\HandlesUserRelationships;
use App\Notifications\App\User\ResetPassword;
use App\Notifications\App\User\UserCreated;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Heloufir\FilamentKanban\Interfaces\KanbanResourceModel;
use Heloufir\FilamentKanban\ValueObjects\KanbanResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

/**
 * User model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  string $email
 * @property  string $password
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 *
 * @property  \Illuminate\Support\Collection|\App\Models\UserFile[] $userFiles
 * @property  \Illuminate\Support\Collection|\App\Models\NotificationSetting[] $notificationSettings
 */
class User extends Authenticatable implements FilamentUser, KanbanResourceModel
{
    use HandlesUserRelationships;
    use HasFactory;
    use Notifiable;
    use HasRoles;

    /**
     * Temporary storage for plain text password to send in notification.
     */
    private ?string $plainTextPassword = null;

    protected $fillable = [
        'name',
        'email',
        'password',
        'active',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'active' => 'bool',
        ];
    }

    /**
     * Set the password attribute and store plain text for notification.
     */
    public function setPasswordAttribute($value): void
    {
        // Store plain text password for notification before hashing
        if (!empty($value)) {
            $this->plainTextPassword = $value;
        }

        // Hash the password
        $this->attributes['password'] = bcrypt($value);
    }

    protected static function booted(): void
    {
        static::created(function (User $user) {
            // Send user created notification with plain text password
            if ($user->plainTextPassword) {
                $accessUrl = $user->buildAccessUrl();
                $user->notify(new UserCreated($user->plainTextPassword, $accessUrl));
            }
        });
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPassword($token));
    }

    public function toKanbanResource(): KanbanResource
    {
        return KanbanResource::make()
            ->id($this->id)
            ->name($this->name);
    }

    public function toResource(): KanbanResource
    {
        return $this->toKanbanResource();
    }

    /**
     * Build the access URL for the user based on their type.
     */
    private function buildAccessUrl(): string
    {
        // Get the tenant domain
        $tenantDomain = $this->getTenantDomain();

        // Check if user has a customer relationship (customer user)
        if ($this->customer()->exists()) {
            return "https://{$tenantDomain}/customer";
        }

        // Internal user - use app panel
        return "https://{$tenantDomain}/app";
    }

    /**
     * Get the current tenant domain.
     */
    private function getTenantDomain(): string
    {
        // If we're in a tenant context, get the domain from tenant
        if (tenant()) {
            $domain = tenant()->domains()->first();
            return $domain ? $domain->domain : 'localhost';
        }

        // Fallback to app URL if not in tenant context
        $appUrl = config('app.url');
        return parse_url($appUrl, PHP_URL_HOST) ?? 'localhost';
    }
}
