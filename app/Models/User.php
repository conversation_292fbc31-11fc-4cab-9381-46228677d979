<?php

namespace App\Models;

use App\Models\Concerns\User\HandlesUserRelationships;
use App\Notifications\App\User\ResetPassword;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Heloufir\FilamentKanban\Interfaces\KanbanResourceModel;
use Heloufir\FilamentKanban\ValueObjects\KanbanResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

/**
 * User model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  string $email
 * @property  string $password
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 *
 * @property  \Illuminate\Support\Collection|\App\Models\UserFile[] $userFiles
 * @property  \Illuminate\Support\Collection|\App\Models\NotificationSetting[] $notificationSettings
 */
class User extends Authenticatable implements FilamentUser, KanbanResourceModel
{
    use HandlesUserRelationships;
    use HasFactory;
    use Notifiable;
    use HasRoles;

    protected $fillable = [
        'name',
        'email',
        'password',
        'active',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'active' => 'bool',
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPassword($token));
    }

    public function toKanbanResource(): KanbanResource
    {
        return KanbanResource::make()
            ->id($this->id)
            ->name($this->name);
    }
}
