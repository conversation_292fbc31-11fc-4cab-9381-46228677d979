<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Checklist step option model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $checklist_step_id
 * @property  int $sequence
 * @property  string $value
 * @property  bool $requires_comment
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ChecklistStep $checklistStep
 */
class ChecklistStepOption extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'checklist_step_id',
        'sequence',
        'value',
        'requires_comment',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'checklist_step_id' => 'int',
        'sequence' => 'int',
        'requires_comment' => 'bool',
    ];

    /**
     * Load the checklist step relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function checklistStep(): BelongsTo
    {
        return $this->belongsTo(ChecklistStep::class);
    }
}
