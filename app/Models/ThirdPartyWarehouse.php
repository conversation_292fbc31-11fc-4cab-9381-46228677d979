<?php

namespace App\Models;

use App\Models\Concerns\ThirdPartyWarehouse\HandlesThirdPartyWarehouseRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Third party warehouse model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $integration_type_id
 * @property  int $warehouse_id
 * @property  string $third_party_id
 * @property  array $third_party_api_read_data
 * @property  array $third_party_db_read_data
 * @property  array $third_party_sent_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Warehouse $warehouse
 */
class ThirdPartyWarehouse extends Model
{
    use HandlesThirdPartyWarehouseRelationships;

    protected $fillable = [
        'integration_type_id',
        'warehouse_id',
        'third_party_id',
        'third_party_api_read_data',
        'third_party_db_read_data',
        'third_party_sent_data',
    ];

    protected $casts = [
        'third_party_api_read_data' => 'array',
        'third_party_db_read_data' => 'array',
        'third_party_sent_data' => 'array',
    ];
}
