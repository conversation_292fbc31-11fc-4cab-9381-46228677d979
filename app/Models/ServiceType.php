<?php

namespace App\Models;

use App\Models\Concerns\ServiceType\HandlesServiceTypeAttributes;
use Illuminate\Database\Eloquent\Model;

/**
 * Service type model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  int $estimated_duration_in_minutes
 * @property  int $warranty_day_count
 * @property  float $default_amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_default_amount
 */
class ServiceType extends Model
{
    use HandlesServiceTypeAttributes;

    protected $fillable = [
        'name',
        'estimated_duration_in_minutes',
        'warranty_day_count',
        'default_amount',
    ];

    protected $casts = [
        'estimated_duration_in_minutes' => 'int',
        'warranty_day_count' => 'int',
        'default_amount' => 'float',
    ];
}
