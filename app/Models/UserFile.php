<?php

namespace App\Models;

use App\Models\Concerns\UserFile\HandlesUserFileRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * User file model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $user_id
 * @property  string $path
 * @property  string $provider
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\User $user
 */
class UserFile extends Model
{
    use HandlesUserFileRelationships;

    protected $fillable = [
        'user_id',
        'path',
        'provider',
    ];
}
