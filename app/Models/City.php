<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

/**
 * City model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $state_id
 * @property  string $name
 * @property  \DateTime $created_at
 * @property  \DateTime $updated_at
 *
 * @property  \App\Models\State $state
 */
class City extends Model
{
    use CentralConnection;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'state_id',
        'name'
    ];

    /**
     * Load the state relationship.
     *
     * @return mixed
     */
    public function state(): mixed
    {
        return $this->belongsTo(State::class);
    }
}
