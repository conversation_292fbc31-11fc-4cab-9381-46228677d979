<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Service order workflow state transition model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_id
 * @property  int $origin_workflow_state_id
 * @property  int $destination_workflow_state_id
 * @property  int $transitioned_by_user_id
 * @property  string $transitioned_by_user_name
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceOrder $serviceOrder
 * @property  \App\Models\WorkflowState $originWorkflowState
 * @property  \App\Models\WorkflowState $destinationWorkflowState
 * @property  \App\Models\User $transitionUser
 */
class ServiceOrderWorkflowStateTransition extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'service_order_id',
        'origin_workflow_state_id',
        'destination_workflow_state_id',
        'transitioned_by_user_id',
        'transitioned_by_user_name',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'service_order_id' => 'int',
        'origin_workflow_state_id' => 'int',
        'destination_workflow_state_id' => 'int',
        'transitioned_by_user_id' => 'int',
    ];

    /**
     * Load the service order relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function serviceOrder(): BelongsTo
    {
        return $this->belongsTo(ServiceOrder::class);
    }

    /**
     * Load the origin workflow state relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function originWorkflowState(): BelongsTo
    {
        return $this->belongsTo(WorkflowState::class, 'origin_workflow_state_id');
    }

    /**
     * Load the destination workflow state relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function destinationWorkflowState(): BelongsTo
    {
        return $this->belongsTo(WorkflowState::class, 'destination_workflow_state_id');
    }

    /**
     * Load the transition user relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function transitionUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'transitioned_by_user_id');
    }
}
