<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Measuring unit model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class MeasuringUnit extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];
}
