<?php

namespace App\Models;

use App\Enums\QuoteStatusEnum;
use App\Models\Concerns\Quote\HandlesQuoteAttributes;
use App\Models\Concerns\Quote\HandlesQuoteRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Quote model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  int $protocol_id
 * @property  \Carbon\Carbon $issued_at
 * @property  string $status
 * @property  string $general_conditions
 * @property  float $amount
 * @property  float $discount_percentage
 * @property  float $discount_subtotal
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_status
 * @property  string $friendly_amount
 * @property  string $friendly_issued_at
 * @property  string $friendly_discount_percentage
 * @property  string $friendly_discount_subtotal
 *
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Protocol $protocol
 *
 * @property  \Illuminate\Support\Collection|\App\Models\QuoteItem[] $quoteItems
 */
class Quote extends Model
{
    use HandlesQuoteAttributes;
    use HandlesQuoteRelationships;

    protected $fillable = [
        'customer_id',
        'protocol_id',
        'issued_at',
        'status',
        'general_conditions',
        'amount',
        'discount_percentage',
        'discount_subtotal',
    ];

    protected $appends = [
        'friendly_status',
        'friendly_amount',
        'friendly_issued_at',
        'friendly_discount_percentage',
        'friendly_discount_subtotal',
    ];

    protected $casts = [
        'amount' => 'float',
        'discount_percentage' => 'float',
        'discount_subtotal' => 'float',
    ];

    public static function booted(): void
    {
        static::creating(function (self $quote): void {
            $quote->status ??= QuoteStatusEnum::Pending->value;
        });
    }
}
