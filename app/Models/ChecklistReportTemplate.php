<?php

namespace App\Models;

use App\Models\Concerns\ChecklistReportTemplate\HandlesChecklistReportTemplateRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Checklist report template model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $checklist_id
 * @property  string $template
 * @property  string $orientation
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Checklist $checklist
 */
class ChecklistReportTemplate extends Model
{
    use HandlesChecklistReportTemplateRelationships;

    protected $fillable = [
        'checklist_id',
        'template',
        'orientation',
    ];
}
