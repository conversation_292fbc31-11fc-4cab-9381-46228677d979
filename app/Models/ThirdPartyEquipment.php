<?php

namespace App\Models;

use App\Models\Concerns\ThirdPartyEquipment\HandlesThirdPartyEquipmentRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Third party equipment model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $integration_type_id
 * @property  int $equipment_id
 * @property  string $third_party_id
 * @property  array $third_party_api_read_data
 * @property  array $third_party_db_read_data
 * @property  array $third_party_sent_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Equipment $equipment
 */
class ThirdPartyEquipment extends Model
{
    use HandlesThirdPartyEquipmentRelationships;

    protected $fillable = [
        'integration_type_id',
        'equipment_id',
        'third_party_id',
        'third_party_api_read_data',
        'third_party_db_read_data',
        'third_party_sent_data',
    ];

    protected $casts = [
        'third_party_api_read_data' => 'array',
        'third_party_db_read_data' => 'array',
        'third_party_sent_data' => 'array',
    ];
}
