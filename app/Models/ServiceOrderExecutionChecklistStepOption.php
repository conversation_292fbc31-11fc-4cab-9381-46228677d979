<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Service order execution checklist step option model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_execution_checklist_step_id
 * @property  int $sequence
 * @property  string $value
 * @property  bool $requires_comment
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep
 */
class ServiceOrderExecutionChecklistStepOption extends Model
{
    protected $fillable = [
        'service_order_execution_checklist_step_id',
        'sequence',
        'value',
        'requires_comment',
    ];

    protected $casts = [
        'service_order_execution_checklist_step_id' => 'int',
        'sequence' => 'int',
        'requires_comment' => 'bool',
    ];

    public function serviceOrderExecutionChecklistStep(): BelongsTo
    {
        return $this->belongsTo(ServiceOrderExecutionChecklistStep::class);
    }
}
