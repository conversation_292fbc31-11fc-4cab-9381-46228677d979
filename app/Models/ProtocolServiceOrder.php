<?php

namespace App\Models;

use App\Models\Concerns\ProtocolServiceOrder\HandlesProtocolServiceOrderRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Protocol service order model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $protocol_id
 * @property  int $service_order_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Protocol $protocol
 * @property  \App\Models\ServiceOrder $serviceOrder
 */
class ProtocolServiceOrder extends Model
{
    use HandlesProtocolServiceOrderRelationships;

    protected $fillable = [
        'protocol_id',
        'service_order_id',
    ];
}
