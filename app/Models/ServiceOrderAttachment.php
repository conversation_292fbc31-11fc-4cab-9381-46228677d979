<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Service order attachment model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_id
 * @property  string $filename
 * @property  string $path
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceOrder $serviceOrder
 */
class ServiceOrderAttachment extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'service_order_id',
        'filename',
        'path',
    ];

    /**
     * Load the service order relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function serviceOrder(): BelongsTo
    {
        return $this->belongsTo(ServiceOrder::class);
    }
}
