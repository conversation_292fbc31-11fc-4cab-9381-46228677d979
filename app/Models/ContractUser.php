<?php

namespace App\Models;

use App\Models\Concerns\ContractUser\HandlesContractUserRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Contract user model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $contract_id
 * @property  int $user_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Contract $contract
 * @property  \App\Models\User $user
 */
class ContractUser extends Model
{
    use HandlesContractUserRelationships;

    protected $fillable = [
        'contract_id',
        'user_id',
    ];
}
