<?php

namespace App\Models;

use App\Models\Concerns\ServiceOrderExecutionChecklistStepProduct\HandlesServiceOrderExecutionChecklistStepProductRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Service order execution checklist step product model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_execution_checklist_step_id
 * @property  int $product_id
 * @property  int $origin_warehouse_id
 * @property  int $stock_movement_id
 * @property  float $quantity
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep
 * @property  \App\Models\Product $product
 * @property  \App\Models\Warehouse $originWarehouse
 * @property  \App\Models\StockMovement $stockMovement
 */
class ServiceOrderExecutionChecklistStepProduct extends Model
{
    use HandlesServiceOrderExecutionChecklistStepProductRelationships;

    protected $fillable = [
        'service_order_execution_checklist_step_id',
        'product_id',
        'origin_warehouse_id',
        'stock_movement_id',
        'quantity',
    ];

    protected $casts = [
        'quantity' => 'float',
    ];
}
