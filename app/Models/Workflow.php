<?php

namespace App\Models;

use App\Enums\WorkflowTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Workflow model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  string $type
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_type
 *
 * @property  \Illuminate\Support\Collection|\App\Models\WorkflowState[] $workflowStates
 */
class Workflow extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'type',
        'active',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'active' => 'bool',
    ];

    /**
     * Accessor for the "friendly type" attribute.
     *
     * @return string
     */
    public function getFriendlyTypeAttribute(): string
    {
        return WorkflowTypeEnum::getTranslated()[$this->type];
    }

    /**
     * Load the workflow states relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function workflowStates(): HasMany
    {
        return $this->hasMany(WorkflowState::class);
    }
}
