<?php

namespace App\Models\Concerns\ServiceOrderHistory;

use App\Models\ServiceOrder;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesServiceOrderHistoryRelationships
{
    public function serviceOrder(): BelongsTo
    {
        return $this->belongsTo(ServiceOrder::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
