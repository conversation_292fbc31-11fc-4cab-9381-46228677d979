<?php

namespace App\Models\Concerns\Checklist;

use App\Models\ChecklistReportTemplate;
use App\Models\ChecklistStep;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesChecklistRelationships
{
    public function checklistSteps(): HasM<PERSON>
    {
        return $this->hasMany(ChecklistStep::class);
    }

    public function checklistReportTemplates(): Has<PERSON><PERSON>
    {
        return $this->hasMany(ChecklistReportTemplate::class);
    }
}
