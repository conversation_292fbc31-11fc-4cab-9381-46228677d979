<?php

namespace App\Models\Concerns\SmtpConfiguration;

trait HandlesSmtpConfigurationAttributes
{
    public function setUsernameAttribute(mixed $value): void
    {
        $this->attributes['username'] = ouess_aes256cbc_encrypt($value);
    }

    public function getUsernameAttribute(): string
    {
        return ouess_aes256cbc_decrypt($this->attributes['username']);
    }

    public function setPasswordAttribute(mixed $value): void
    {
        $this->attributes['password'] = ouess_aes256cbc_encrypt($value);
    }

    public function getPasswordAttribute(): string
    {
        return ouess_aes256cbc_decrypt($this->attributes['password']);
    }
}