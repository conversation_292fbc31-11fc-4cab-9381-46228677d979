<?php

namespace App\Models\Concerns\ServiceOrderExecutionChecklistStepProduct;

use App\Models\Product;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\StockMovement;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesServiceOrderExecutionChecklistStepProductRelationships
{
    public function serviceOrderExecutionChecklistStep(): BelongsTo
    {
        return $this->belongsTo(ServiceOrderExecutionChecklistStep::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function originWarehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'origin_warehouse_id');
    }

    public function stockMovement(): BelongsTo
    {
        return $this->belongsTo(StockMovement::class);
    }
}
