<?php

namespace App\Models\Concerns\ServiceOrderExecutionChecklistStep;

use App\Models\Checklist;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderExecutionChecklistStepNeed;
use App\Models\ServiceOrderExecutionChecklistStepOption;
use App\Models\ServiceOrderExecutionChecklistStepProduct;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesServiceOrderExecutionChecklistStepRelationships
{
    public function serviceOrder(): BelongsTo
    {
        return $this->belongsTo(ServiceOrder::class);
    }

    public function checklist(): BelongsTo
    {
        return $this->belongsTo(Checklist::class);
    }

    public function serviceOrderExecutionChecklistStepOptions(): HasMany
    {
        return $this->hasMany(ServiceOrderExecutionChecklistStepOption::class);
    }

    public function serviceOrderExecutionChecklistStepProducts(): HasMany
    {
        return $this->hasMany(ServiceOrderExecutionChecklistStepProduct::class);
    }

    public function serviceOrderExecutionChecklistStepNeeds(): HasMany
    {
        return $this->hasMany(ServiceOrderExecutionChecklistStepNeed::class);
    }
}
