<?php

namespace App\Models\Concerns\User;

use App\Models\Customer;
use App\Models\NotificationSetting;
use App\Models\UserFile;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HandlesUserRelationships
{
    public function customer(): HasOne
    {
        return $this->hasOne(Customer::class);
    }

    public function userFiles(): HasMany
    {
        return $this->hasMany(UserFile::class);
    }

    public function notificationSetting(): HasOne
    {
        return $this->hasOne(NotificationSetting::class);
    }
}
