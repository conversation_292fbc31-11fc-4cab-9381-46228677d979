<?php

namespace App\Models\Concerns\Customer;

use App\Enums\CustomerAddressTypeEnum;
use Illuminate\Support\Str;

trait HandlesCustomerAttributes
{
    public function setTaxIdNumberAttribute(string $value)
    {
        $this->attributes['tax_id_number'] = Str::remove(['.', '/', '-'], $value);
    }

    public function getFriendlyTaxIdNumberAttribute(): string
    {
        return strlen($this->tax_id_number) === 11
            ? mask_cpf($this->tax_id_number)
            : mask_cnpj($this->tax_id_number);
    }

    public function getFullAddressAttribute(): string
    {
        $mainAddress = $this->customerAddresses()
            ->where('type', CustomerAddressTypeEnum::Contact->value)
            ->first();

        if (!$mainAddress) {
            return '';
        }

        return "$mainAddress->address, $mainAddress->number $mainAddress->complement, $mainAddress->district | CEP: $mainAddress->zipcode - {$mainAddress->city} / {$mainAddress->state}";
    }
}
