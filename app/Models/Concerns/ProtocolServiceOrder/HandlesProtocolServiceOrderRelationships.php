<?php

namespace App\Models\Concerns\ProtocolServiceOrder;

use App\Models\Protocol;
use App\Models\ServiceOrder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesProtocolServiceOrderRelationships
{
    public function protocol(): BelongsTo
    {
        return $this->belongsTo(Protocol::class);
    }

    public function serviceOrder(): BelongsTo
    {
        return $this->belongsTo(ServiceOrder::class);
    }
}
