<?php

namespace App\Models\Concerns\ServiceOrder;

use App\Enums\ServiceOrderStatusEnum;

trait HandlesServiceOrderAttributes
{
    public function getTokenAttribute(): string
    {
        return base64_encode($this->id . ';' . carbon($this->created_at)->format('Y-m-d H:i:s'));
    }

    public function getFriendlyStatusAttribute(): string
    {
        return ServiceOrderStatusEnum::getTranslated()[$this->status];
    }

    public function getFriendlyStartedAtAttribute(): ?string
    {
        return $this->started_at
            ? format_date($this->started_at)
            : null;
    }

    public function getFriendlyFinishedAtAttribute(): ?string
    {
        return $this->finished_at
            ? format_date($this->finished_at)
            : null;
    }

    public function getInWarrantyAttribute(): bool
    {
        if (!$this->finished_at) {
            return false;
        }

        return carbon($this->finished_at)->addDays($this->serviceType->warranty_day_count)->startOfDay()->gte(now()->startOfDay());
    }
}
