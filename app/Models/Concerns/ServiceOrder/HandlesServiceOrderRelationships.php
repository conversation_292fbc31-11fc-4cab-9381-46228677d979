<?php

namespace App\Models\Concerns\ServiceOrder;

use App\Models\ContractItem;
use App\Models\Customer;
use App\Models\Equipment;
use App\Models\Protocol;
use App\Models\ServiceOrderAttachment;
use App\Models\ServiceOrderChecklist;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderWorkflowStateTransition;
use App\Models\ServiceType;
use App\Models\ThirdPartyServiceOrder;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Workflow;
use App\Models\WorkflowState;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesServiceOrderRelationships
{
    public function protocol(): BelongsTo
    {
        return $this->belongsTo(Protocol::class);
    }

    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    public function contractItem(): BelongsTo
    {
        return $this->belongsTo(ContractItem::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'employee_id');
    }

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function WorkflowState(): BelongsTo
    {
        return $this->belongsTo(WorkflowState::class);
    }

    public function serviceOrderAttachments(): HasMany
    {
        return $this->hasMany(ServiceOrderAttachment::class);
    }

    public function serviceOrderChecklists(): HasMany
    {
        return $this->hasMany(ServiceOrderChecklist::class);
    }

    public function serviceOrderWorkflowStateTransitions(): HasMany
    {
        return $this->hasMany(ServiceOrderWorkflowStateTransition::class);
    }

    public function serviceOrderExecutionChecklistSteps(): HasMany
    {
        return $this->hasMany(ServiceOrderExecutionChecklistStep::class);
    }

    public function thirdPartyServiceOrders(): HasMany
    {
        return $this->hasMany(ThirdPartyServiceOrder::class);
    }
}
