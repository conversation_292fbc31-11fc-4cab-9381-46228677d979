<?php

namespace App\Models\Concerns\Contract;

use App\Models\ContractItem;
use App\Models\ContractUser;
use App\Models\Customer;
use App\Models\ThirdPartyContract;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesContractRelationships
{
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function contractItems(): HasMany
    {
        return $this->hasMany(ContractItem::class);
    }

    public function contractOperators(): HasMany
    {
        return $this->hasMany(ContractUser::class);
    }

    public function thirdPartyContracts(): HasMany
    {
        return $this->hasMany(ThirdPartyContract::class);
    }
}
