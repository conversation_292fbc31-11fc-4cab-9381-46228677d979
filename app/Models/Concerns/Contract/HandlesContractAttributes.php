<?php

namespace App\Models\Concerns\Contract;

use App\Enums\ContractStatusEnum;
use App\Enums\ContractTypeEnum;

trait HandlesContractAttributes
{
    public function getFriendlyTypeAttribute(): string
    {
        return ContractTypeEnum::getTranslated()[$this->type];
    }

    public function getFriendlyStatusAttribute(): string
    {
        return ContractStatusEnum::getTranslated()[$this->status];
    }
}
