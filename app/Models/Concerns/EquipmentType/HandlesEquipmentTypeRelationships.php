<?php

namespace App\Models\Concerns\EquipmentType;

use App\Models\Equipment;
use App\Models\EquipmentTypeCustomField;
use App\Models\ThirdPartyEquipmentType;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesEquipmentTypeRelationships
{
    public function equipmentTypeCustomFields(): HasMany
    {
        return $this->hasMany(EquipmentTypeCustomField::class);
    }

    public function equipment(): HasMany
    {
        return $this->hasMany(Equipment::class);
    }

    public function thirdPartyEquipmentTypes(): HasMany
    {
        return $this->hasMany(ThirdPartyEquipmentType::class);
    }
}
