<?php

namespace App\Models\Concerns\Equipment;

use App\Models\Customer;
use App\Models\EquipmentCustomer;
use App\Models\EquipmentType;
use App\Models\ThirdPartyEquipment;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesEquipmentRelationships
{
    public function equipmentType(): BelongsTo
    {
        return $this->belongsTo(EquipmentType::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function equipmentCustomers(): HasMany
    {
        return $this->hasMany(EquipmentCustomer::class);
    }

    public function thirdPartyEquipment(): HasMany
    {
        return $this->hasMany(ThirdPartyEquipment::class);
    }
}
