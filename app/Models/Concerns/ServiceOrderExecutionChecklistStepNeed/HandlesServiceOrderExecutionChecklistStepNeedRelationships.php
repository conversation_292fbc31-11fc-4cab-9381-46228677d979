<?php

namespace App\Models\Concerns\ServiceOrderExecutionChecklistStepNeed;

use App\Models\ServiceOrderExecutionChecklistStep;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesServiceOrderExecutionChecklistStepNeedRelationships
{
    public function serviceOrderExecutionChecklistStep(): BelongsTo
    {
        return $this->belongsTo(ServiceOrderExecutionChecklistStep::class);
    }

    public function need(): BelongsTo
    {
        return $this->morphTo();
    }
}
