<?php

namespace App\Models\Concerns\QuoteItem;

trait HandlesQuoteItemAttributes
{
    public function getFriendlyUnitAmountAttribute(): string
    {
        return format_money($this->unit_amount);
    }

    public function getFriendlyTotalAmountAttribute(): string
    {
        return format_money($this->total_amount);
    }

    public function getFriendlyDiscountPercentageAttribute(): string
    {
        return format_percentage($this->discount_percentage);
    }

    public function getFriendlyDiscountSubtotalAttribute(): string
    {
        return format_money($this->discount_subtotal);
    }

    public function setUnitAmountAttribute(mixed $value): void
    {
        $this->attributes['unit_amount'] = unmask_money($value);
    }

    public function setTotalAmountAttribute(mixed $value): void
    {
        $this->attributes['total_amount'] = unmask_money($value);
    }

    public function setDiscountPercentageAttribute(mixed $value): void
    {
        $this->attributes['discount_percentage'] = unmask_percentage($value);
    }

    public function setDiscountSubtotalAttribute(mixed $value): void
    {
        $this->attributes['discount_subtotal'] = unmask_money($value);
    }
}
