<?php

namespace App\Models\Concerns\QuoteItem;

use App\Models\Equipment;
use App\Models\Product;
use App\Models\Quote;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderExecutionChecklistStep;
use App\Models\ServiceOrderExecutionChecklistStepNeed;
use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesQuoteItemRelationships
{
    public function quote(): BelongsTo
    {
        return $this->belongsTo(Quote::class);
    }

    public function serviceOrder(): BelongsTo
    {
        return $this->belongsTo(ServiceOrder::class);
    }

    public function serviceOrderExecutionChecklistStep(): BelongsTo
    {
        return $this->belongsTo(ServiceOrderExecutionChecklistStep::class);
    }

    public function serviceOrderExecutionChecklistStepNeed(): BelongsTo
    {
        return $this->belongsTo(ServiceOrderExecutionChecklistStepNeed::class);
    }

    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
