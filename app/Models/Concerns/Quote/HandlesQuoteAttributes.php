<?php

namespace App\Models\Concerns\Quote;

use App\Enums\QuoteStatusEnum;

trait HandlesQuoteAttributes
{
    public function getTokenAttribute(): string
    {
        return base64_encode($this->id . ';' . carbon($this->created_at)->format('Y-m-d H:i:s'));
    }

    public function getFriendlyStatusAttribute(): string
    {
        return QuoteStatusEnum::getTranslated()[$this->status];
    }

    public function getFriendlyAmountAttribute(): string
    {
        return format_money($this->amount);
    }

    public function getFriendlyDiscountPercentageAttribute(): string
    {
        return format_percentage($this->discount_percentage);
    }

    public function getFriendlyDiscountSubtotalAttribute(): string
    {
        return format_money($this->discount_subtotal);
    }

    public function getFriendlyIssuedAtAttribute(): string
    {
        return format_date($this->issued_at);
    }

    public function setAmountAttribute(mixed $value): void
    {
        $this->attributes['amount'] = unmask_money($value);
    }

    public function setDiscountSubtotalAttribute(mixed $value): void
    {
        $this->attributes['discount_subtotal'] = unmask_money($value);
    }

    public function setDiscountPercentageAttribute(mixed $value): void
    {
        $this->attributes['discount_percentage'] = unmask_percentage($value);
    }
}
