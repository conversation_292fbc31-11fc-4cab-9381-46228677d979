<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Customer contact model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  string $type
 * @property  string $name
 * @property  string $email
 * @property  string $phone_1
 * @property  string $phone_2
 * @property  bool $main
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 */
class CustomerContact extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'type',
        'name',
        'email',
        'phone_1',
        'phone_2',
        'main',
        'active',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'main' => 'bool',
        'active' => 'bool',
    ];

    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
}
