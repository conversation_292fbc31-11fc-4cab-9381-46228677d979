<?php

namespace App\Models;

use App\Models\Concerns\ThirdPartyCustomer\HandlesThirdPartyCustomerRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Third party customer model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $integration_type_id
 * @property  int $customer_id
 * @property  string $third_party_id
 * @property  array $third_party_api_read_data
 * @property  array $third_party_db_read_data
 * @property  array $third_party_sent_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 */
class ThirdPartyCustomer extends Model
{
    use HandlesThirdPartyCustomerRelationships;

    protected $fillable = [
        'integration_type_id',
        'customer_id',
        'third_party_id',
        'third_party_api_read_data',
        'third_party_db_read_data',
        'third_party_sent_data',
    ];

    protected $casts = [
        'third_party_api_read_data' => 'array',
        'third_party_db_read_data' => 'array',
        'third_party_sent_data' => 'array',
    ];
}
