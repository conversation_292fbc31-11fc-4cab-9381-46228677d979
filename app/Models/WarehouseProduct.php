<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Warehouse product model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $warehouse_id
 * @property  int $product_id
 * @property  float $available_amount
 * @property  float $minimum_amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Warehouse $warehouse
 * @property  \App\Models\Product $product
 */
class WarehouseProduct extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'warehouse_id',
        'product_id',
        'available_amount',
        'minimum_amount',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'warehouse_id' => 'int',
        'product_id' => 'int',
        'available_amount' => 'float',
        'minimum_amount' => 'float',
    ];

    /**
     * Load the warehouse relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::saved(function (self $warehouseProduct): void {
            $warehouseProduct->product->update([
                'available_quantity' => $warehouseProduct->product->productWarehouses->sum(function (WarehouseProduct $warehouseProduct): float {
                    return $warehouseProduct->available_amount;
                }),
            ]);
        });
    }
}
