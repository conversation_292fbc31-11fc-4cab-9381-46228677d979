<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * ERPFlex parameter model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $equipment_serial_number_field
 * @property  int $service_order_default_used_product_stock_nature_id
 * @property  int $contract_item_default_nature_id
 * @property  int $quote_default_salesman_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class ErpFlexParameter extends Model
{
    protected $fillable = [
        'equipment_serial_number_field',
        'service_order_default_used_product_stock_nature_id',
        'contract_item_default_nature_id',
        'quote_default_salesman_id',
    ];

    protected $casts = [
        'service_order_default_used_product_stock_nature_id' => 'int',
        'contract_item_default_nature_id' => 'int',
        'quote_default_salesman_id' => 'int',
    ];
}
