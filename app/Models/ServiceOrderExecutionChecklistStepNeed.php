<?php

namespace App\Models;

use App\Models\Concerns\ServiceOrderExecutionChecklistStepNeed\HandlesServiceOrderExecutionChecklistStepNeedRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Service order execution checklist step need model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_execution_checklist_step_id
 * @property  int $need_id
 * @property  string $need_type
 * @property  float $quantity
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep
 * @property  \App\Models\Product|App\Models\Service $need
 */
class ServiceOrderExecutionChecklistStepNeed extends Model
{
    use HandlesServiceOrderExecutionChecklistStepNeedRelationships;

    protected $fillable = [
        'service_order_execution_checklist_step_id',
        'need_id',
        'need_type',
        'quantity',
    ];

    protected $casts = [
        'quantity' => 'float',
    ];
}
