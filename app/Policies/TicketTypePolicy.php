<?php

namespace App\Policies;

use App\Models\Permission;
use App\Models\TicketType;
use App\Models\User;

class TicketTypePolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can(Permission::GET_TICKET_TYPES);
    }

    public function view(User $user, TicketType $ticketType): bool
    {
        return $user->can(Permission::GET_TICKET_TYPES);
    }

    public function create(User $user): bool
    {
        return $user->can(Permission::CREATE_TICKET_TYPES);
    }

    public function update(User $user, TicketType $ticketType): bool
    {
        return $user->can(Permission::UPDATE_TICKET_TYPES);
    }

    public function delete(User $user, TicketType $ticketType): bool
    {
        return $user->can(Permission::DELETE_TICKET_TYPES);
    }
}
