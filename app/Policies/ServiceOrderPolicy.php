<?php

namespace App\Policies;

use App\Enums\ServiceOrderStatusEnum;
use App\Models\Permission;
use App\Models\ServiceOrder;
use App\Models\User;

class ServiceOrderPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo(Permission::GET_SERVICE_ORDERS);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ServiceOrder $serviceOrder): bool
    {
        return $user->hasPermissionTo(Permission::GET_SERVICE_ORDERS);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo(Permission::CREATE_SERVICE_ORDERS);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ServiceOrder $serviceOrder): bool
    {
        return $user->hasPermissionTo(Permission::UPDATE_SERVICE_ORDERS)
            && in_array($serviceOrder->status, [ServiceOrderStatusEnum::Pending->value, ServiceOrderStatusEnum::Scheduled->value]);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ServiceOrder $serviceOrder): bool
    {
        return $user->hasPermissionTo(Permission::DELETE_SERVICE_ORDERS)
            && in_array($serviceOrder->status, [ServiceOrderStatusEnum::Pending->value, ServiceOrderStatusEnum::Scheduled->value]);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ServiceOrder $serviceOrder): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ServiceOrder $serviceOrder): bool
    {
        return false;
    }
}
