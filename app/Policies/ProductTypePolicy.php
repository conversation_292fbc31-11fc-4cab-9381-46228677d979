<?php

namespace App\Policies;

use App\Models\Permission;
use App\Models\ProductType;
use App\Models\User;

class ProductTypePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo(Permission::GET_PRODUCT_TYPES);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProductType $productType): bool
    {
        return $user->hasPermissionTo(Permission::GET_PRODUCT_TYPES);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo(Permission::CREATE_PRODUCT_TYPES);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProductType $productType): bool
    {
        return $user->hasPermissionTo(Permission::UPDATE_PRODUCT_TYPES);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProductType $productType): bool
    {
        return $user->hasPermissionTo(Permission::DELETE_PRODUCT_TYPES);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProductType $productType): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProductType $productType): bool
    {
        return false;
    }
}
