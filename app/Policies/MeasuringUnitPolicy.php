<?php

namespace App\Policies;

use App\Models\MeasuringUnit;
use App\Models\Permission;
use App\Models\User;

class MeasuringUnitPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo(Permission::GET_MEASURING_UNITS);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MeasuringUnit $measuringUnit): bool
    {
        return $user->hasPermissionTo(Permission::GET_MEASURING_UNITS);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo(Permission::CREATE_MEASURING_UNITS);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MeasuringUnit $measuringUnit): bool
    {
        return $user->hasPermissionTo(Permission::UPDATE_MEASURING_UNITS);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MeasuringUnit $measuringUnit): bool
    {
        return $user->hasPermissionTo(Permission::DELETE_MEASURING_UNITS);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MeasuringUnit $measuringUnit): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MeasuringUnit $measuringUnit): bool
    {
        return false;
    }
}
