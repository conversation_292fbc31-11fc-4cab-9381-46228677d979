<?php

namespace App\Policies;

use App\Models\Checklist;
use App\Models\Permission;
use App\Models\User;

class ChecklistPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo(Permission::GET_CHECKLISTS);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Checklist $checklist): bool
    {
        return $user->hasPermissionTo(Permission::GET_CHECKLISTS);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo(Permission::CREATE_CHECKLISTS);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Checklist $checklist): bool
    {
        return $user->hasPermissionTo(Permission::UPDATE_CHECKLISTS);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Checklist $checklist): bool
    {
        return $user->can(Permission::DELETE_CHECKLISTS);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Checklist $checklist): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Checklist $checklist): bool
    {
        return false;
    }
}
