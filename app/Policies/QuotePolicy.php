<?php

namespace App\Policies;

use App\Models\Quote;
use App\Models\Permission;
use App\Models\User;

class QuotePolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can(Permission::GET_QUOTES);
    }

    public function view(User $user, Quote $quote): bool
    {
        return $user->can(Permission::GET_QUOTES);
    }

    public function create(User $user): bool
    {
        return $user->can(Permission::CREATE_QUOTES);
    }

    public function update(User $user, Quote $quote): bool
    {
        return $user->can(Permission::UPDATE_QUOTES);
    }

    public function delete(User $user, Quote $quote): bool
    {
        return $user->can(Permission::DELETE_QUOTES);
    }
}
