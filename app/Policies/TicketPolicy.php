<?php

namespace App\Policies;

use App\Models\Permission;
use App\Models\Ticket;
use App\Models\User;

class TicketPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can(Permission::GET_TICKETS);
    }

    public function view(User $user, Ticket $ticket): bool
    {
        return $user->can(Permission::GET_TICKETS);
    }

    public function create(User $user): bool
    {
        return $user->can(Permission::CREATE_TICKETS);
    }

    public function update(User $user, Ticket $ticket): bool
    {
        return $user->can(Permission::UPDATE_TICKETS);
    }

    public function delete(User $user, Ticket $ticket): bool
    {
        return $user->can(Permission::DELETE_TICKETS);
    }
}
