<?php

namespace App\Integrations;

use App\Models\IntegrationSetting;
use Illuminate\Database\Eloquent\Collection;

class IntegrationServiceManager
{
    /**
     * Retrieves integration setting by its permission.
     *
     * @param  string $permission
     * @param  int|null $typeId
     * @return \Illuminate\Support\Collection|\App\Models\IntegrationSetting[]
     */
    public function loadByPermission(string $permission, ?int $typeId = null): Collection
    {
        $permission = 'settings->' . str_replace('.', '->', $permission);

        return IntegrationSetting::query()
            ->where('active', true)
            ->whereJsonContains($permission, true)
            ->when(!is_null($typeId), function ($query) use ($typeId) {
                $query->where('integration_type_id', $typeId);
            })
            ->get();
    }

    /**
     * Get the tenant's integration settings.
     *
     * @return \App\Models\IntegrationSetting
     */
    public function integration_settings(int $typeId = 1): IntegrationSetting
    {
        /** @var \App\Models\IntegrationSetting $integrationSettings */
        $integrationSettings = IntegrationSetting::query()
            ->where('integration_type_id', $typeId)
            ->first();

        if (is_null($integrationSettings)) {
            return IntegrationSetting::create([
                'integration_type_id' => $typeId,
                'settings' => [],
            ]);
        }

        return IntegrationSetting::first();
    }
}
