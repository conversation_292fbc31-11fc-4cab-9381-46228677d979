<?php

namespace App\Integrations\Api\Ibge\Core;

use App\Integrations\Core\BaseApiClient;

class IbgeApiV1Client extends BaseApiClient
{
    /**
     * Create a new instance.
     */
    public function __construct()
    {
        parent::__construct(config('services.ibge.api.url'));
    }

    /**
     * Execute a get request in the server.
     *
     * @param  string $path
     * @return mixed
     */
    public function get(string $path): mixed
    {
        return json_decode(
            $this->client->get($path)
                ->getBody()
                ->getContents()
        );
    }
}
