<?php

namespace App\Integrations;

use Illuminate\Support\Facades\Facade;

/**
 * Integration service facade.
 *
 * @package App\Integrations
 * @method  static \Illuminate\Support\Collection|\App\Models\IntegrationSetting[]  loadByPermission(string $permission, ?int $typeId = null)
 */
class IntegrationService extends Facade
{
    /** @inheritDoc */
    protected static function getFacadeAccessor()
    {
        return IntegrationServiceManager::class;
    }
}
