<?php

namespace App\Enums;

enum TicketStatusEnum: string
{
    case Pending = 'pending';
    case InProgress = 'in_progress';
    case Resolved = 'resolved';
    case Closed = 'closed';
    case Cancelled = 'cancelled';

    public static function getTranslated(): array
    {
        return [
            self::Pending->value => 'Pendente',
            self::InProgress->value => 'Em andamento',
            self::Resolved->value => 'Resolvido',
            self::Closed->value => 'Fechado',
            self::Cancelled->value => 'Cancelado',
        ];
    }

    public static function getTableColors(): array
    {
        return [
            self::Pending->value => 'warning',
            self::InProgress->value => 'primary',
            self::Resolved->value => 'success',
            self::Closed->value => 'success',
            self::Cancelled->value => 'gray',
        ];
    }
}