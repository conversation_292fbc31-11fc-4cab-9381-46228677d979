<?php

namespace App\Enums;

enum ReportEnum: string
{
    case ServiceOrders = 'service_orders';
    case ServiceOrdersExecution = 'service_orders_execution';

    public static function getTranslated(): array
    {
        return [
            self::ServiceOrders->value => 'Ordens de serviço',
            self::ServiceOrdersExecution->value => 'Execução de ordens de serviço',
        ];
    }

    public static function getMapped(): array
    {
        return [
            'general' => [
                'title' => 'Geral',
                'items' => [
                    ReportEnum::ServiceOrders->value => 'Ordens de serviço',
                    ReportEnum::ServiceOrdersExecution->value => 'Execução de ordens de serviço',
                ]
            ]
        ];
    }
}
