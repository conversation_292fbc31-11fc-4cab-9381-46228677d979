<?php

namespace App\Enums;

enum ContractItemPeriodEnum: string
{
    case Days = 'days';
    case Weeks = 'weeks';
    case Months = 'months';
    case Years = 'years';

    public static function getTranslated(): array
    {
        return [
            self::Days->value => 'Dias',
            self::Weeks->value => 'Semanas',
            self::Months->value => 'Meses',
            self::Years->value => 'Anos',
        ];
    }
}
