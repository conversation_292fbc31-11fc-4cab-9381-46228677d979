<?php

namespace App\Enums;

enum QuoteStatusEnum: string
{
    case Pending = 'pending';
    case Approved = 'approved';
    case Rejected = 'rejected';

    public static function getTranslated(): array
    {
        return [
            self::Pending->value => 'Pendente',
            self::Approved->value => 'Aprovado',
            self::Rejected->value => 'Rejeitado',
        ];
    }

    public static function getTableColors(): array
    {
        return [
            self::Pending->value => 'warning',
            self::Approved->value => 'success',
            self::Rejected->value => 'danger',
        ];
    }
}
