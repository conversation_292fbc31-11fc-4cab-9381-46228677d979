<?php

namespace App\Enums;

enum ServiceOrderStatusEnum: string
{
    case Pending = 'pending';
    case Scheduled = 'scheduled';
    case Executing = 'executing';
    case Finished = 'finished';
    case Cancelled = 'cancelled';

    public static function getTranslated(): array
    {
        return [
            self::Pending->value => 'Pendente',
            self::Scheduled->value => 'Agendada',
            self::Executing->value => 'Em execução',
            self::Finished->value => 'Finalizada',
            self::Cancelled->value => 'Cancelada',
        ];
    }

    public static function getTableColors(): array
    {
        return [
            self::Finished->value => 'success',
            self::Cancelled->value => 'gray',
            self::Scheduled->value => 'info',
            self::Pending->value => 'warning',
            self::Executing->value => 'primary',
        ];
    }
}
