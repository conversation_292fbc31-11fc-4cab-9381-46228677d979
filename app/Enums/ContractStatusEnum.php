<?php

namespace App\Enums;

enum ContractStatusEnum: string
{
    case Active = 'active';
    case Inactive = 'inactive';
    case Cancelled = 'cancelled';

    public static function getTranslated(): array
    {
        return [
            self::Active->value => 'Ativo',
            self::Inactive->value => 'Inativo',
            self::Cancelled->value => 'Cancelado',
        ];
    }

    public static function getTableColors(): array
    {
        return [
            self::Active->value => 'success',
            self::Inactive->value => 'gray',
            self::Cancelled->value => 'danger',
        ];
    }
}
