<?php

namespace App\Notifications\ServiceOrder;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ServiceOrdersReportNotification extends Notification
{
    use Queueable;

    public function __construct(
        private array $files
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage())
            ->subject(config('app.name') . ' - ' . (count($this->files) > 1 ? 'laudos' : 'laudo') . ' de OS')
            ->greeting("Olá, tudo bem?")
            ->line("O(s) arquivo(s) de OS encontra(m)-se anexado(s) a este email.");

        foreach ($this->files as $file) {
            $message->attach(storage_path("app/{$file['filename']}"), [
                'as' => "{$file['download_filename']}.pdf",
                'mime' => 'application/pdf',
            ]);
        }

        return $message
            ->salutation('Obrigado!');
    }
}
