<?php

namespace App\Notifications\App\User;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPassword extends \Filament\Notifications\Auth\ResetPassword
{
    use Queueable;

    /**
     * Get the reset password notification mail message for the given URL.
     *
     * @param  string  $url
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    protected function buildMailMessage($url)
    {
        return (new MailMessage)
            ->subject('Redefinição de senha')
            ->greeting('Olá!')
            ->line('Você está recebendo este e-mail porque foi requisitada uma redefinição de senha para a sua conta.')
            ->action('Redefinir senha', $url)
            ->line('Este link de redefinição de senha irá expirar em 60 minutos.')
            ->line('Se não foi você quem requisitou a redefinição de senha, sem problemas - nenhuma ação posterior é necessária.')
            ->salutation('<PERSON><PERSON><PERSON> por utilizar nossos serviços!');
    }
}
