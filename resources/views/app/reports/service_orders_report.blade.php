<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>Ouess - Ordens de serviço</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ global_asset('css/app/report-default.css') }}">
</head>

<body>
    <x-report.report>
        <x-slot name="headerInfo">
            <p style="margin-top: 0; margin-bottom: 0;">Ordens de serviço</p>

            <p style="margin-top: 0; margin-bottom: 0;"><strong>Data de:</strong> {{ $dateFrom }}</p>
            <p style="margin-top: 0; margin-bottom: 0;"><strong>Data até:</strong> {{ $dateTo }}</p>

            @if (isset($customerId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Razão social:</strong> {{ $customerName }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Nome fantasia:</strong> {{ $customerTradingName }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>CNPJ:</strong> {{ $customerTaxIdNumber }}</p>
            @endif

            @if (isset($equipmentId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Equipamento:</strong> {{ $equipmentName }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Número de série:</strong> {{ $equipmentSerialNumber }}</p>
            @endif

            @if (isset($serviceTypeId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Tipo de serviço:</strong> {{ $serviceTypeName }}</p>
            @endif

            @if (isset($employeeId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Técnico:</strong> {{ $employeeName }}</p>
            @endif

            @if (isset($status))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Status:</strong> {{ $status }}</p>
            @endif

            <p style="margin-top: 0; margin-bottom: 0;"><strong>Emissão:</strong> {{ get_timezoned_issued_at() }}</p>
        </x-slot>

        <x-slot name="body">
            <table class="table-sm table-borderless table-striped table" style="margin-top: 0.5rem;">
                <thead>
                    <tr>
                        <th scope="col" style="text-align: left;">Código</th>
                        <th scope="col" style="text-align: left;">Doc./NF</th>
                        <th scope="col" style="text-align: left;">Razão<br>social</th>
                        <th scope="col" style="text-align: left;">Nome fantasia</th>
                        <th scope="col" style="text-align: left;">CNPJ</th>
                        <th scope="col" style="text-align: left;">Equipamento</th>
                        <th scope="col" style="text-align: left;">Tipo de<br>serviço</th>
                        <th scope="col" style="text-align: left;">Técnico</th>
                        <th scope="col" style="text-align: left;">Status</th>
                        <th scope="col" style="text-align: left;">Iniciada<br>em</th>
                        <th scope="col" style="text-align: left;">Finalizada<br>em</th>
                        <th scope="col" style="text-align: left;">Data de<br>emissão</th>
                    </tr>
                </thead>
                @foreach ($serviceOrders as $serviceOrder)
                    <tr>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['code'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['protocol_invoice_number'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['customer_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['customer_trading_name'] }}</td>
                        <td scope="col" style="text-align: left; width:130px;">{{ $serviceOrder['customer_tax_id_number'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['equipment_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['service_type_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['employee_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['status'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['started_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['finished_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $serviceOrder['created_at'] }}</td>
                    </tr>
                @endforeach
            </table>
        </x-slot>
    </x-report.report>
</body>
