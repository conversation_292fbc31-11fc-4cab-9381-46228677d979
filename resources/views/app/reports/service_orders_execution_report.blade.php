<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>Ouess - Execução de ordens de serviço</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ global_asset('css/app/report-default.css') }}">
</head>

<body>
    <x-report.report>
        <x-slot name="headerInfo">
            <p style="margin-top: 0; margin-bottom: 0;">Execução de ordens de serviço</p>

            <p style="margin-top: 0; margin-bottom: 0;"><strong>Data de:</strong> {{ $dateFrom }}</p>
            <p style="margin-top: 0; margin-bottom: 0;"><strong>Data até:</strong> {{ $dateTo }}</p>
            <p style="margin-top: 0; margin-bottom: 0;"><strong>Roteiro:</strong> {{ $checklistName }}</p>

            @if (isset($customerId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Razão social:</strong> {{ $customerName }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Nome fantasia:</strong> {{ $customerTradingName }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>CNPJ:</strong> {{ $customerTaxIdNumber }}</p>
            @endif

            @if (isset($equipmentId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Equipamento:</strong> {{ $equipmentName }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Número de série:</strong> {{ $equipmentSerialNumber }}</p>
            @endif

            @if (isset($serviceTypeId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Tipo de serviço:</strong> {{ $serviceTypeName }}</p>
            @endif

            @if (isset($employeeId))
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Técnico:</strong> {{ $employeeName }}</p>
            @endif

            <p style="margin-top: 0; margin-bottom: 0;"><strong>Emissão:</strong> {{ get_timezoned_issued_at() }}</p>
        </x-slot>

        <x-slot name="body">
            @foreach ($serviceOrders as $serviceOrder)
                <div style="margin-top: 1rem;">
                    <p style="margin-bottom: 0;"><strong>OS #{{ $serviceOrder['code'] }} ({{ $serviceOrder['friendly_status'] }})</strong></p>

                    @if (isset($serviceOrder['protocol']['invoice_number']))
                        <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">{{ $serviceOrder['protocol']['invoice_number'] }}</p>
                    @endif

                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Cliente: {{ $serviceOrder['customer']['name'] }} | {{ $serviceOrder['customer']['trading_name'] }} (CNPJ: {{ $serviceOrder['customer']['friendly_tax_id_number'] }})</p>
                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Equipamento: {{ $serviceOrder['equipment']['name'] ?? '' }}</p>
                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Tipo de serviço: {{ $serviceOrder['service_type']['name'] ?? '' }}</p>
                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Técnico: {{ $serviceOrder['employee']['name'] ?? '' }}</p>
                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Emitida em: {{ format_date($serviceOrder['created_at']) }}</p>
                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Iniciada em: {{ format_date($serviceOrder['started_at']) }}</p>
                    <p style="margin-top: 0; margin-bottom: 0; font-size: 0.8rem;">Finalizada em: {{ format_date($serviceOrder['finished_at']) }}</p>
                    <table class="table-sm table-borderless table-striped table" style="margin-top: 0.5rem;">
                        <thead>
                            <tr>
                                @foreach ($serviceOrder['service_order_execution_checklist_steps'] as $serviceOrderExecutionChecklistStep)
                                    <th scope="col" style="text-align: left;">{{ $serviceOrderExecutionChecklistStep['checklist_step_name'] }}</th>
                                @endforeach
                            </tr>
                        </thead>
                        <tr>
                            @foreach ($serviceOrder['service_order_execution_checklist_steps'] as $serviceOrderExecutionChecklistStep)
                                @if ($serviceOrderExecutionChecklistStep['data_type'] === 'signature')
                                    <td scope="col" style="text-align: left;"><img src="{{ $serviceOrderExecutionChecklistStep['collected_value'] }}" alt="Assinatura" style="height: 100px"></td>
                                @else
                                    <td scope="col" style="text-align: left; vertical-align: middle;">{{ $serviceOrderExecutionChecklistStep['collected_value'] }}</td>
                                @endif
                            @endforeach
                        </tr>
                    </table>
                </div>
            @endforeach
        </x-slot>
    </x-report.report>
</body>
