<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>Impressão de resultado de roteiro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    {{-- <link href="https://fonts.googleapis.com/css2?family=Inter&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@700&display=swap" rel="stylesheet"> --}}
    <link rel="stylesheet" href="{{ global_asset('css/app/report-default.css') }}">
</head>

<body>
    <div id="report-header" class="mb-2">
        <div class="header float-left">
            <img class="ouess-img-nav" src="{{ $logoSrc }}" alt="Logo" />
        </div>
        <div class="float-right text-center" style="width: 350px">
            <h3 style="color: darkgreen;">{{ $serviceOrder->code }} - {{ $serviceOrder->serviceType->name }} - {{ $serviceOrder->customer->trading_name }}</h3>
        </div>
    </div>

    <div class="clear-both"></div>

    <div id="report-body">
        <table class="table table-bordered" width="100%">
            <tr>
                <td colspan="2" style="background-color: darkgreen; padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span style="color: white;"><strong>CLIENTE</strong></span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Razão social</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->customer->name }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Nome fantasia</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->customer->trading_name }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>CPF / CNPJ</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->customer->friendly_tax_id_number }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Endereço</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->customer->full_address }}</span>
                </td>
            </tr>
        </table>

        <table class="table table-bordered mt-4" width="100%" style="break-inside: auto;">
            <tr>
                <td style="background-color: darkgreen; padding-top: 0.25rem; padding-bottom: 0.25rem;" colspan="2">
                    <span style="color: white;"><strong>DADOS INICIAIS</strong></span>
                </td>
            </tr>
            @if ($serviceOrder->employee_id)
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Responsável pelo preenchimento</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->employee->name }}</span>
                </td>
            </tr>
            @endif
            @if ($serviceOrder->protocol)
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Doc./NF</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->protocol->invoice_number }}</span>
                </td>
            </tr>
            @endif
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Tipo de serviço</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->serviceType->name }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Equipamento</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->equipment->name }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Data de início de execução do serviço</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->friendly_started_at }}</span>
                </td>
            </tr>
            @if ($serviceOrder->finished_at)
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Data de finalização do serviço</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $serviceOrder->friendly_finished_at }}</span>
                </td>
            </tr>
            @endif
        </table>

        <table class="table table-bordered mt-4" width="100%">
            <tr>
                <td style="background-color: darkgreen; padding-top: 0.25rem; padding-bottom: 0.25rem;" colspan="2">
                    <span style="color: white;"><strong>ROTEIRO DE EXECUÇÃO</strong></span>
                </td>
            </tr>
            @foreach ($serviceOrder->serviceOrderExecutionChecklistSteps as $serviceOrderExecutionChecklistStep)
                <tr style="margin-top: 4px">
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span><strong>{{ $serviceOrderExecutionChecklistStep->checklist_step_name }}</strong></span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        @if (in_array($serviceOrderExecutionChecklistStep->data_type, [\App\Enums\ChecklistStepDataTypeEnum::Image->value, \App\Enums\ChecklistStepDataTypeEnum::Signature->value]))
                            <div>
                                <img src="{{ $serviceOrderExecutionChecklistStep->collected_value }}" alt="Imagem" />
                            </div>
                        @elseif ($serviceOrderExecutionChecklistStep->data_type === \App\Enums\ChecklistStepDataTypeEnum::SingleChoice->value)
                            <div>
                                <span>{{ \App\Models\ServiceOrderExecutionChecklistStepOption::find($serviceOrderExecutionChecklistStep->collected_value)?->value }}</span>
                            </div>
                        @elseif ($serviceOrderExecutionChecklistStep->data_type === \App\Enums\ChecklistStepDataTypeEnum::MultipleChoice->value)
                            @foreach ($serviceOrderExecutionChecklistStep->serviceOrderExecutionChecklistStepOptions as $option)
                                @if (in_array($option->id, json_decode($serviceOrderExecutionChecklistStep->collected_value ?? "{}", true)))
                                <div>
                                    <input type="checkbox" style="display: inline" checked />
                                    <span>{{ $option->value }}</span>
                                </div>
                                @endif
                            @endforeach
                        @elseif ($serviceOrderExecutionChecklistStep->data_type === \App\Enums\ChecklistStepDataTypeEnum::ProgressReport->value)
                            <div>
                                <span>Data: {{ json_decode($serviceOrderExecutionChecklistStep->collected_value, true)['date'] ? carbon(json_decode($serviceOrderExecutionChecklistStep->collected_value, true)['date'])->format('d/m/Y') : '' }}</span><br>
                                <span>Valor: {{ json_decode($serviceOrderExecutionChecklistStep->collected_value, true)['value'] }}</span>
                            </div>
                        @else
                            <div>
                                <span>{{ $serviceOrderExecutionChecklistStep->collected_value }}</span>
                            </div>
                        @endif
                        <div style="margin-top: 4px">
                            <span>{{ $serviceOrderExecutionChecklistStep->comments }}</span>
                        </div>
                    </td>
                </tr>
            @endforeach
        </table>
    </div>
</body>
