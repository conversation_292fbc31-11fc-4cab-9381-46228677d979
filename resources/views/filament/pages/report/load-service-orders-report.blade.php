<x-filament::page>
    <form method="post" class="filament-form space-y-6" wire:submit.prevent="generate">
        @csrf
        {{ $this->form }}

        <div class="filament-page-actions filament-form-actions flex flex-wrap items-center justify-start gap-4">
            <x-filament::button type="submit" color="success">
                Gerar
            </x-filament::button>
            <x-filament::button type="submit" color="gray" wire:click="cancel">
                Cancelar
            </x-filament::button>
        </div>
    </form>
</x-filament::page>

@script
    <script>
        $wire.on('runReport', (event) => {
            window.open(event.url, '_blank');
        });
    </script>
@endscript